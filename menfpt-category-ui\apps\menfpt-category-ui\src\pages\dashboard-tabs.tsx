import React, { useState, useRef, useEffect } from 'react';
// import { createPortal } from 'react-dom';
import Report from './report';
import LaggingIndicatorPage from './lagging-indicator-page';
import "./dashboard-tabs.scss";
import Drawer from '@albertsons/uds/molecule/Drawer';
import Button from '@albertsons/uds/molecule/Button';
import { useSelectorWrap } from '../rtk/rtk-utilities';
import Tabs, { Tab } from '@albertsons/uds/molecule/Tabs';
import Tag from '@albertsons/uds/molecule/Tag';
import EPBCSSyncMonitor from '../../src/features/EPBCSSyncMonitor';
import AllocatrInsights from '../components/AllocatrInsights/AllocatrInsights';
// Update the import path and casing to match the actual file location
import {  SelectWeek } from './../components/SnapShotDropDown/release-week-select';
import { CircleAlert } from 'lucide-react';
import { useCurrentQuarterNbr } from '../features/calendarServiceUtils';
import Tooltip from '@albertsons/uds/molecule/Tooltip';import Icon from '@albertsons/uds/molecule/Link';
import { ReactComponent as Download } from '../assets/download-icon-dashboard.svg'; 
import { handleDownloadExcel } from '../components/DashboardDownloadExcel/DashboardDownloadExcel';
import { getNowInPST } from '../util/dateUtils';
import { format } from 'date-fns-tz';

enum TabsLabels {
  LEADING_INDICATORS = 'Leading Indicators',
  PERFORMANCE_SUMMARY = 'Performance Summary',
  FORECAST_VARIANCE = 'Performance Variance'
}

// const tabClassNames = {
//   [Tabs.LEADING_INDICATORS]: 'bg-white rounded',
//   [Tabs.PERFORMANCE_SUMMARY]: 'bg-white rounded',
// };

const downloadedDate = format(getNowInPST(), 'yyyy-MM-dd');

const hasValidData = (data: any) => {
  if (!data) return false;
  if (Array.isArray(data)) {
    return data.length > 0;
  }
  return data.divisions?.length > 0 || data.id;
};

const DashboardTabs = () => {
  const [selectedTab, setSelectedTab] = useState(TabsLabels.PERFORMANCE_SUMMARY);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedWeek, setSelectedWeek] = useState<{ name: string; num: number; value: string; weekNumber: number} | null>(null);
  const [performanceSummaryData, setPerformanceSummaryData] = useState<any>(null);
  const [forecastVarianceData, setForecastVarianceData] = useState<any>(null);
  const [dashboardLoading, setDashboardLoading] = useState(true);
  const { data: worksheetFilters = {} } = useSelectorWrap('workSheetFilterList_rn');
   const [showMessage, setShowMessage] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState<{ top: number; left: number } | null>(null);
  const alertIconRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const smicData = worksheetFilters.smicData || [];

  // Safely access displayDate with a fallback
  const displayDateSelector = useSelectorWrap('displayDate_rn');
  const displayDate = displayDateSelector?.data || {};
  const { data: appliedFilters } = useSelectorWrap('appliedFilter_rn');

  // const handleTabClick = (tabName: Tabs) => {
  //   setSelectedTab(tabName);
  // };

  const handleSyncMonitorClick = () => {
    setIsDrawerOpen(true);
  };

  const handleWeekChange = (item: { name: string; num: number; value:string; weekNumber: number }) => {
    setSelectedWeek(item);
    // dispatch(setSelectedWeek(item)); // If you want to use redux
  };

  const handlePerformanceSummaryData = (data: any) => {
    setPerformanceSummaryData(data);
    setDashboardLoading(false);
  };

  const handleForecastVarianceData = (data: any) => {
    setForecastVarianceData(data);
    setDashboardLoading(false);
  };
  const renderTabContent = (tab: TabsLabels) => {
    switch (tab) {
      case TabsLabels.LEADING_INDICATORS:
        return <div><Report /></div>;
      case TabsLabels.PERFORMANCE_SUMMARY:
        return <div><AllocatrInsights selectedTab={TabsLabels.PERFORMANCE_SUMMARY} onDataLoaded={handlePerformanceSummaryData}/></div>
      case TabsLabels.FORECAST_VARIANCE:
        return <div><AllocatrInsights selectedTab={TabsLabels.FORECAST_VARIANCE} onDataLoaded={handleForecastVarianceData}/></div>
      default:
        return null;
    }
  };

  const visibleTabs = [
    TabsLabels.LEADING_INDICATORS,
    TabsLabels.PERFORMANCE_SUMMARY,
    TabsLabels.FORECAST_VARIANCE
  ];
  const classes = 'flex justify-center items-center h-48 text';

  return (
    <div>

      <div className="flex items-center justify-between px-2 py-2 overflow-x-auto">
        <div className="tabs-container">

          <div
            className="flex gap-1 text-center pt-5 items-center w-full rounded-lg cursor-pointer font-nunito-sans font-semibold text-base leading-6 tracking-normal"
            style={{ margin: '5px 10px', padding: '5px', width:'600px', borderColor: 'transparent' }}
          >

            <Tabs
              initialTab={visibleTabs.indexOf(selectedTab)}
              variant='light'
              onChange={idx => setSelectedTab(visibleTabs[idx])}
              className='w-full border-transparent dashboard-tab'
            >
              {visibleTabs.map((tab, idx) => (
                <Tab className={classes} key={tab}>
                  <Tab.Header>

                    {tab === TabsLabels.FORECAST_VARIANCE ? (
                      <span
                        tabIndex={2}
                        onBlur={() => setShowMessage(false)}
                        style={{
                          display: 'inline-flex',
                          alignItems: 'center',
                          gap: '4px',
                          position: 'relative'
                        }}>

                        <div 
                          className="relative inline-block"
                        >
                          <span className='tool-tip-initilizer-top'></span>
                        <Tooltip
                  zIndex={9999}
                  anchor='top'
                  variant='dark'
                  className={'uds-tooltip-top'}
                  label={' This table compares the latest value with data from Last Friday. You will be able to track how far things have changed.'}>
                  <CircleAlert
                    size={16}
                    style={{ cursor: 'pointer' }}
                    color=" #1B6EBB"
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                  />
                </Tooltip>
                        </div>
                        
                        {tab}
                      </span>
                    ) : (
                      tab
                    )}
                  </Tab.Header>
              </Tab>
            ))}
          </Tabs>
        </div>
      </div>
        <div className="flex flex-row items-center gap-1 w-auto h-auto mt-0 mb-0 ml-0 mr-0">
          <Icon
            before={
              <span className="w-4 h-4 flex items-center text-[#1B6EBB]"> <Download/> </span>
            }
            className={`flex items-center gap-1 h-6 px-4 py-0 text-base font-medium whitespace-nowrap cursor-pointer
              ${(dashboardLoading || (!hasValidData(performanceSummaryData) && !hasValidData(forecastVarianceData))) ? 'opacity-50 pointer-events-none': '' }`}
            onClick={
              dashboardLoading ||
              (selectedTab === TabsLabels.PERFORMANCE_SUMMARY && !hasValidData(performanceSummaryData)) ||
              (selectedTab === TabsLabels.FORECAST_VARIANCE && !hasValidData(forecastVarianceData))
                ? undefined
                : () => {
                    if (selectedTab === TabsLabels.PERFORMANCE_SUMMARY) {
                      handleDownloadExcel(performanceSummaryData, smicData, appliedFilters, `Allocatr Insights Performance Summary Excel Download-${downloadedDate}.xlsx`);
                    } else if (selectedTab === TabsLabels.FORECAST_VARIANCE) {
                      handleDownloadExcel(forecastVarianceData, smicData, appliedFilters, `Allocatr Insights Variance Summary Excel Download-${downloadedDate}.xlsx`);
                    }
                  }
            }
          >Download as Excel
          </Icon>
        </div>
      <div className='flex items-center gap-4'>
           <div className='mr-6'>
        <SelectWeek weekChange={handleWeekChange} selectedTab={selectedTab}/>
      </div>
          <div className="flex items-center gap-4">
            <Button
              className="sync-button"
              size="xs"
              variant="secondary"
              onClick={handleSyncMonitorClick}
            >
              EPBCS Sync Monitor
            </Button>
          </div>
      </div>
      </div>

      <div className="overflow-x-auto">
        {renderTabContent(selectedTab)}
      </div>

      <Drawer
        anchor="right"
        isOpen={isDrawerOpen}
        setOpen={setIsDrawerOpen}
        hideBackdrop={false}
        width="608px"
        header={<div>EPBCS Sync Monitor</div>}
      >
        <EPBCSSyncMonitor />
      </Drawer>
    </div>
  );
};

export default DashboardTabs;
