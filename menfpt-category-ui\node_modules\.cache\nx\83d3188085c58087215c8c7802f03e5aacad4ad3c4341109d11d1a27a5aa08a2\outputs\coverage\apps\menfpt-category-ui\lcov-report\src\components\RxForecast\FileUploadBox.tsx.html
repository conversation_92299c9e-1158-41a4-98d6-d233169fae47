
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/RxForecast/FileUploadBox.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">src/components/RxForecast</a> FileUploadBox.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/26</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/10</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/5</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/26</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import React, { useState, DragEvent, ChangeEvent } from "react";
&nbsp;
interface FileUploadBoxProps {
  onFileUpload: (files: File[] | FileList) =&gt; void;
  onValidationError?: (message: string) =&gt; void;
}
&nbsp;
const FileUploadBox: React.FC&lt;FileUploadBoxProps&gt; = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >({</span> onFileUpload, onValidationError }) =&gt; {</span>
  const [file, setFile] = <span class="cstat-no" title="statement not covered" >useState&lt;File | null&gt;(null);</span>
&nbsp;
  const uploadIcon = (
<span class="cstat-no" title="statement not covered" >    &lt;svg width={25} height={24} viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"&gt;</span>
      &lt;path d="M14.5 4H7.69998C7.27563 4 6.86866 4.16857 6.5686 4.46863C6.26855 4.76869 6.09998 5.17565 6.09998 5.6V18.4C6.09998 18.8243 6.26855 19.2313 6.5686 19.5314C6.86866 19.8314 7.27563 20 7.69998 20H17.3C17.7243 20 18.1313 19.8314 18.4313 19.5314C18.7314 19.2313 18.9 18.8243 18.9 18.4V8.4L14.5 4Z" fill="#EBF3FA" /&gt;
      &lt;path d="M14.1 4V8.8H18.9" fill="#EBF3FA" /&gt;
      &lt;path d="M12.5 12V16.8V12Z" fill="#EBF3FA" /&gt;
      &lt;path d="M14.9 14.4L12.5 12L10.1 14.4" fill="#EBF3FA" /&gt;
      &lt;path d="M14.1 4V8.8H18.9M12.5 12V16.8M12.5 12L14.9 14.4M12.5 12L10.1 14.4M14.5 4H7.69998C7.27563 4 6.86866 4.16857 6.5686 4.46863C6.26855 4.76869 6.09998 5.17565 6.09998 5.6V18.4C6.09998 18.8243 6.26855 19.2313 6.5686 19.5314C6.86866 19.8314 7.27563 20 7.69998 20H17.3C17.7243 20 18.1313 19.8314 18.4313 19.5314C18.7314 19.2313 18.9 18.8243 18.9 18.4V8.4L14.5 4Z" stroke="#5A697B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" /&gt;
    &lt;/svg&gt;
  );
&nbsp;
  const handleDrop = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(e</span>: DragEvent&lt;HTMLDivElement&gt;) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    e.preventDefault();</span>
    const droppedFile = <span class="cstat-no" title="statement not covered" >e.dataTransfer.files[0];</span>
<span class="cstat-no" title="statement not covered" >    if (validateFile(droppedFile)) {</span>
<span class="cstat-no" title="statement not covered" >      setFile(droppedFile);</span>
<span class="cstat-no" title="statement not covered" >      onFileUpload([droppedFile]);</span>
    }
  };
&nbsp;
  const handleBrowse = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(e</span>: ChangeEvent&lt;HTMLInputElement&gt;) =&gt; {</span>
    const selectedFile = <span class="cstat-no" title="statement not covered" >e.target.files?.[0];</span>
<span class="cstat-no" title="statement not covered" >    if (selectedFile &amp;&amp; validateFile(selectedFile)) {</span>
<span class="cstat-no" title="statement not covered" >      setFile(selectedFile);</span>
<span class="cstat-no" title="statement not covered" >      onFileUpload([selectedFile]);</span>
    }
  };
&nbsp;
  const validateFile = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(f</span>ile: File) =&gt; {</span>
    const isXlsx =
<span class="cstat-no" title="statement not covered" >      file.type ===</span>
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    const isLt25mb = <span class="cstat-no" title="statement not covered" >file.size / 1024 / 1024 &lt;= 25;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (!isXlsx) {</span>
<span class="cstat-no" title="statement not covered" >      onValidationError?.("Please upload only .xlsx files.");</span>
<span class="cstat-no" title="statement not covered" >      return false;</span>
    }
<span class="cstat-no" title="statement not covered" >    if (!isLt25mb) {</span>
<span class="cstat-no" title="statement not covered" >      onValidationError?.("The file size has been exceeded, please try again.");</span>
<span class="cstat-no" title="statement not covered" >      return false;</span>
    }
<span class="cstat-no" title="statement not covered" >    return true;</span>
  };
&nbsp;
<span class="cstat-no" title="statement not covered" >  return (</span>
    &lt;div
      onDragOver={<span class="fstat-no" title="function not covered" >(e</span>) =&gt; <span class="cstat-no" title="statement not covered" >e.preventDefault()}</span>
      onDrop={handleDrop}
      className="flex justify-center items-center gap-2.5 self-stretch py-6 px-0 rounded border-2 border-dashed border-[#3997ef] bg-[#ebf3fa]"
    &gt;
      &lt;div className="content flex flex-col items-center gap-1"&gt;
        &lt;div className="flex items-center pt-1 pb-0 pl-0 pr-3 w-9 h-7"&gt;
          {uploadIcon}
        &lt;/div&gt;
        &lt;div className="drag_and_drop_or_browse self-stretch text-center"&gt;
          &lt;span className="text-[#5a697b] font-['Nunito_Sans'] text-base font-bold leading-5"&gt;Drag and drop &lt;/span&gt;
          &lt;span className="text-[#5a697b] font-['Nunito_Sans'] text-base font-normal leading-5"&gt;document or &lt;/span&gt;
          &lt;label className="text-[#1b6ebb] font-['Nunito_Sans'] text-base font-normal leading-5 cursor-pointer "&gt;
            browse
            &lt;input
              type="file"
              className="hidden"
              accept=".xlsx"
              onChange={handleBrowse}
            /&gt;
          &lt;/label&gt;
        &lt;/div&gt;
        &lt;div className="helper_text self-stretch text-[#5a697b] text-center font-nunito text-sm italic"&gt;
          Only .xlsx files are allowed. 25 mb maximum file size
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  );
};
&nbsp;
export default FileUploadBox;
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-17T06:34:15.200Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    