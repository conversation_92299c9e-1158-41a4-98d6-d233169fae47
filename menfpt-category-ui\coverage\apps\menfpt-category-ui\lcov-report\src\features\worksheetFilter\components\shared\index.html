
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/features/worksheetFilter/components/shared</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../index.html">All files</a> src/features/worksheetFilter/components/shared</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">79.66% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>47/59</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">75.75% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>50/66</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">77.77% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>14/18</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">80% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>44/55</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="SelectAllCheckbox.tsx"><a href="SelectAllCheckbox.tsx.html">SelectAllCheckbox.tsx</a></td>
	<td data-value="70" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 70%"></div><div class="cover-empty" style="width: 30%"></div></div>
	</td>
	<td data-value="70" class="pct medium">70%</td>
	<td data-value="10" class="abs medium">7/10</td>
	<td data-value="69.23" class="pct medium">69.23%</td>
	<td data-value="13" class="abs medium">9/13</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="8" class="abs medium">6/8</td>
	</tr>

<tr>
	<td class="file high" data-value="SelectableList.tsx"><a href="SelectableList.tsx.html">SelectableList.tsx</a></td>
	<td data-value="82.05" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 82%"></div><div class="cover-empty" style="width: 18%"></div></div>
	</td>
	<td data-value="82.05" class="pct high">82.05%</td>
	<td data-value="39" class="abs high">32/39</td>
	<td data-value="82.14" class="pct high">82.14%</td>
	<td data-value="28" class="abs high">23/28</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="12" class="abs medium">9/12</td>
	<td data-value="81.08" class="pct high">81.08%</td>
	<td data-value="37" class="abs high">30/37</td>
	</tr>

<tr>
	<td class="file high" data-value="SelectableListItem.tsx"><a href="SelectableListItem.tsx.html">SelectableListItem.tsx</a></td>
	<td data-value="80" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 80%"></div><div class="cover-empty" style="width: 20%"></div></div>
	</td>
	<td data-value="80" class="pct high">80%</td>
	<td data-value="10" class="abs high">8/10</td>
	<td data-value="72" class="pct medium">72%</td>
	<td data-value="25" class="abs medium">18/25</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="4" class="abs medium">3/4</td>
	<td data-value="80" class="pct high">80%</td>
	<td data-value="10" class="abs high">8/10</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-17T06:07:00.681Z
            </div>
        <script src="../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../sorter.js"></script>
        <script src="../../../../../block-navigation.js"></script>
    </body>
</html>
    