import React, { useState, useEffect, useRef } from 'react';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
import { ChevronsDownUp, ChevronsUpDown, Settings2 } from 'lucide-react';
import AllocatrDepartmentRow from './AllocatrDepartmentRow';
import { useGetAllocatrTableDataMutation } from '../../server/Api/menfptCategoryAPI';
import AllocatrPeriodRow from './AllocatrPeriodRow';
import Spinner from '@albertsons/uds/molecule/Spinner';
import AllocatrWeekRow from './AllocatrWeekRow';
import { AllocatrInsightsResponse, WeekData } from '../../interfaces/allocatr-insights';
import './AllocatrInsightsTable.scss';
import { toTitleCase } from '@ui/utils';
import { borderClass } from './AllocatrInsightsHelper';
import AllocatrDivisionRow from './AllocatrDivisionRow';
import AllocatrBannerRow from './AllocatrBannerRow';
import AllocatrTotalRow from './AllocatrTotalRow';

interface AllocatrInsightsTableProps {
  insightsData: AllocatrInsightsResponse;
  selectedTab: String;
}

const AllocatrInsightsTable: React.FC<AllocatrInsightsTableProps> = ({ insightsData, selectedTab }) => {
  const [expandedDivisions, setExpandedDivisions] = useState<Record<string, boolean>>({});
  const [expandedBanners, setExpandedBanners] = useState<Record<string, boolean>>({});
  const [expandedWeeks, setExpandedWeeks] = useState<Record<string, boolean>>({});
  const [expandAll, setExpandAll] = useState(false);
  const [expandedTotal, setExpandedTotal] = useState(false);
  const { data: appliedFilters } = useSelectorWrap('appliedFilter_rn');
  const { data: worksheetFilters } = useSelectorWrap('workSheetFilterList_rn') || {};
  const { data: CalendarWeek } = useSelectorWrap('dataForQrtrDisplayedInTable_rn') || {};
  const { data: displayDate } = useSelectorWrap('displayDate_rn');
  const smicData = worksheetFilters?.smicData || [];
  const [isLoading, setIsLoading] = useState(false);
  const [getAllocatrTableData] = useGetAllocatrTableDataMutation();
  const [weeks, setWeeks] = useState<Map<string, WeekData[]>>(new Map());
  const [loadingWeeks, setLoadingWeeks] = useState<Record<string, boolean>>({});
  const weekRefs = useRef<Record<string, HTMLTableRowElement | null>>({});
  const [hoveredDeptId, setHoveredDeptId] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const deptRowRefs = useRef<Record<string, HTMLTableRowElement | null>>({});
  const periodRowRefs = useRef<Record<string, HTMLTableRowElement | null>>({});
  const [stickyHoveredDeptId, setStickyHoveredDeptId] = useState<string | null>(null);
  const getDeptKey = (divisionId: string, bannerId: string, departmentId: string) => `${divisionId}_${bannerId}_${departmentId}`;
  const toggleAll = () => {
    const newExpandAll = !expandAll;
    setExpandAll(newExpandAll);

    const newExpandedDivisions: Record<string, boolean> = {};
    const newExpandedBanners: Record<string, boolean> = {};
    const newExpandedWeeks: Record<string, boolean> = {};

    if (newExpandAll) {
      for (const division of insightsData.divisions || []) {
        newExpandedDivisions[division.id] = true;
        for (const banner of division.banners || []) {
          newExpandedBanners[`${division.id}-${banner.id}`] = true;
          // for (const department of banner.departments || []) {
          //   newExpandedWeeks[department.id] = true;
          // }
        }
      }
    }

    setExpandedDivisions(newExpandedDivisions);
    setExpandedBanners(newExpandedBanners);
    // setExpandedWeeks(newExpandedWeeks);
    setExpandedTotal(newExpandAll);
  };

  const toggleDivision = (divisionId: string) => {
    setExpandedDivisions(prev => ({ ...prev, [divisionId]: !prev[divisionId] }));
  };

  const toggleBanner = (divisionId: string, bannerId: string) => {
    const key = `${divisionId}-${bannerId}`;
    setExpandedBanners(prev => ({ ...prev, [key]: !prev[key] }));
  };
  const toggleWeeks = (divisionId: string, bannerId: string, departmentId: string) => {
    setExpandedWeeks(prev => ({
      ...prev,
      [getDeptKey(divisionId, bannerId, departmentId)]: !prev[getDeptKey(divisionId, bannerId, departmentId)]
    }));
  };

  const toggleTotal = () => {
    setExpandedTotal(prev => !prev);
  };
  const toggleTotalDivisions = () => {
    setExpandedDivisions(prev => {
      const allExpanded = Object.values(prev).every(Boolean);
      const newExpandedDivisions: Record<string, boolean> = {};
      for (const division of insightsData.divisions || []) {
        newExpandedDivisions[division.id] = !allExpanded;
      }
      return newExpandedDivisions;
    });
  }
 const getWeeks = (divisionId: string, bannerId: string, departmentId: string, periodNumber: any) => {
  const deptKey = getDeptKey(divisionId, bannerId, departmentId);
  if (expandedWeeks[deptKey]) {
    if (loadingWeeks[deptKey]) {
      return (
        <tr ref={el => (weekRefs.current[departmentId] = el)}>
          <td colSpan={15} style={{ padding: '40px 0' }}>
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', minHeight: 60 }}>
              <Spinner />
            </div>
          </td>
        </tr>
      );
    }

    const division = (insightsData.divisions || []).find(d => d.id === divisionId);
    const banner = division?.banners?.find(b => b.id === bannerId);
    const department = banner?.departments?.find(dep => dep.id === departmentId);

    if (department?.weeks) {
      const weekList = department.weeks;
      return weekList
        .filter(wk => wk.periodNumber === periodNumber)
        .map((week: WeekData, idx: number) => (
          <AllocatrWeekRow
            key={week.id}
            week={week}
            isLastFiscalWeek={idx === weekList.length - 1}
            onLastFiscalWeekHover={() => setHoveredDeptId(departmentId)}
            ref={idx === 0 ? (el => { weekRefs.current[departmentId] = el; }) as React.Ref<HTMLTableRowElement> : undefined}
            isActualUsed={week.isActualUsed}
            isCurrentWeek={week.weekNumber === displayDate?.fiscalWeekNumber}
          />
        ));
    }
  }
};
  const getTotalWeeks = (periodNumber: any) => {
    if (expandedTotal) {
      const weekList = insightsData.weeks || [];
      return weekList.filter(wk => wk.periodNumber === periodNumber)
        .map((week: WeekData, idx: number) => (
          <AllocatrWeekRow
            key={week.id}
            week={week}
            isLastFiscalWeek={idx === weekList.length - 1}
            onLastFiscalWeekHover={() => {}}
            isActualUsed={week.isActualUsed}
            isCurrentWeek={week.weekNumber === displayDate?.fiscalWeekNumber}
          />
        ));
    }
  }

  const getDeptName = (deptId: string, fallback: string) => {
    const found = smicData.find((item: any) => String(item.deptId) === String(deptId));
    return found ? toTitleCase(found.deptName || '') : toTitleCase(fallback || '');
  };

  const getDivisionName = (divisionId: string, fallback: string) => {
    const found = smicData.find((item: any) => String(item.divisionId) === String(divisionId));
    return found ? toTitleCase(found.divisionName || '') : toTitleCase(fallback || divisionId);
  };

  const getBannerName = (divisionId: string, bannerId: string, fallback: string) => {
    const found = smicData.find((item: any) => String(item.divisionId) === String(divisionId) && String(item.bannerId) === String(bannerId));
    return found ? toTitleCase(found.bannerName || '') : toTitleCase(fallback || bannerId);
  };
  
  const tableHeader = (
    <thead className="text-left">
      <tr className="sticky-header-row">
        <th rowSpan={2} className="header-cellsticky left-0 sticky-header-cell quarter-cell"></th>
        <th rowSpan={2} className="header-cellsticky left-0 sticky-header-cell quarter-cell qc-cell-with-icon">
          <div className="quarter-wrapper">
            <span>
              {appliedFilters?.timeframe
                ? `Q${appliedFilters.timeframe?.num?.toString().slice(-1)} ${appliedFilters.timeframe.fiscalYear}`
                : 'Quarter'}
            </span>
           <div className="flex gap-6 flex-shrink-0 ml-2">
              {expandAll ? (
                <ChevronsDownUp
                  size={18}
                  className="cursor-pointer"
                  onClick={toggleAll}
                />
              ) : (
                <ChevronsUpDown
                  size={18}
                  className="cursor-pointer"
                  onClick={toggleAll}
                />
              )}
              <Settings2 size={18} color='#000'/>
            </div>
          </div>
        </th>
        <th colSpan={6} className="header-cell">Line 1 (Sales to Public)</th>
        <th colSpan={5} className="header-cell ">Book Gross Profit %</th>
        <th colSpan={5} className="header-cell ">Markdown %</th>
        <th colSpan={5} className="header-cell ">Shrink %</th>
        <th colSpan={6} className="header-cell ">Line 5 (Realized Gross Profit): Total</th>
        <th colSpan={3} className="header-cell ">Line 6 (Supplies Packaging)</th>
        <th colSpan={3} className="header-cell">Line 7 (Retail Allowance)</th>
        <th colSpan={6} className="header-cell">Line 8 (Realized Gross Profit Before Other Revenue – Sales)</th>
      </tr>
      <tr className="sticky-subheader-row">
        <th className="subheader-cell">$ Projection</th>
        <th className="subheader-cell">$ Last Year</th>
        <th className="subheader-cell">$ Actual/Merch. Forecast</th>
        <th className="subheader-cell">Keeper% (Includes ID)</th>
        <th className="subheader-cell">$ vs LY</th>
        <th className={`subheader-cell ${borderClass}`}>$ vs Projection</th>

        <th className="subheader-cell">$ Projection</th>
        <th className="subheader-cell">% Projection</th>
        <th className="subheader-cell">$ Actual/Merch. Forecast</th>
        <th className="subheader-cell">% Actual/Merch. Forecast</th>
        <th className={`subheader-cell ${borderClass}`}>% vs Projection</th>

        <th className="subheader-cell">$ Projection</th>
        <th className="subheader-cell">% Projection</th>
        <th className="subheader-cell">$ Actual/Merch. Forecast</th>
        <th className="subheader-cell">% Actual/Merch. Forecast</th>
        <th className={`subheader-cell ${borderClass}`}>% vs Projection</th>

        <th className="subheader-cell">$ Projection</th>
        <th className="subheader-cell">% Projection</th>
        <th className="subheader-cell">$ Actual/Merch. Forecast</th>
        <th className="subheader-cell">% Actual/Merch. Forecast</th>
        <th className={`subheader-cell ${borderClass}`}>% vs Projection</th>

        <th className="subheader-cell">$ Projection</th>
        <th className="subheader-cell">% Projection</th>
        <th className="subheader-cell">$ Actual/Merch. Forecast</th>
        <th className="subheader-cell">% Actual/Merch. Forecast</th>
        <th className="subheader-cell">$ vs Projection</th>
        <th className={`subheader-cell ${borderClass}`}>% vs Projection</th>

        <th className="subheader-cell">$ Projection</th>
        <th className="subheader-cell">$ Actual/Merch. Forecast</th>
        <th className={`subheader-cell ${borderClass}`}>$ vs Projection</th>

        <th className="subheader-cell">$ Projection</th>
        <th className="subheader-cell">$ Actual/Merch. Forecast</th>
        <th className={`subheader-cell ${borderClass}`}>$ vs Projection</th>

        <th className="subheader-cell">$ Projection</th>
        <th className="subheader-cell">% Projection</th>
        <th className="subheader-cell">$ Actual/Merch. Forecast</th>
        <th className="subheader-cell">% Actual/Merch. Forecast</th>
        <th className="subheader-cell">$ vs Projection</th>
        <th className={`subheader-cell ${borderClass}`}>% vs Projection</th>
      </tr>
    </thead>
  );

  const tableBody = (
    <tbody>   
      {insightsData && insightsData.id === 'Total' &&  (
        <>
          <AllocatrTotalRow
          quarter={ insightsData.quarter as import('../../interfaces/allocatr-insights').QuarterData}
          expanded={expandedTotal}
          onToggle={toggleTotal}
          onToggleTotalDivisions={toggleTotalDivisions}
          divisionCount={(insightsData.divisions || []).length}
        />
       
      {expandedTotal && (insightsData.periods || []).map(period => (
        <React.Fragment key={period.id}>
          <AllocatrPeriodRow
            period={period}
          />
          {getTotalWeeks(period.periodNumber)}
        </React.Fragment>
      ))}

        </>
      )}

      {(insightsData.divisions || []).map(division => {
        const divisionName = getDivisionName(division.id, division.name);
        return (
          <React.Fragment key={division.id}>
            <AllocatrDivisionRow
              division={{ ...division, name: divisionName }}
              expanded={!!expandedDivisions[division.id]}
              onToggle={toggleDivision}
            />
            {expandedDivisions[division.id] && (
              Array.isArray(division.banners) && division.banners.length > 0 ? (
                <>
                  {/* Render all banners except '00' as usual */}
                  {division.banners.filter(banner => banner.id !== '00').map(banner => {
                    const bannerName = getBannerName(division.id, banner.id, banner.name);
                    return (
                      <React.Fragment key={`${division.id}-${banner.id}`}>
                        <AllocatrBannerRow
                          banner={{ ...banner, name: bannerName }}
                          expanded={!!expandedBanners[`${division.id}-${banner.id}`]}
                          onToggle={(bannerId) => toggleBanner(division.id, bannerId)}
                        />
                        {expandedBanners[`${division.id}-${banner.id}`] && (banner.departments || []).map(department => (
                          <React.Fragment key={department.id}>
                            <AllocatrDepartmentRow
                              department={{ ...department, name: getDeptName(department.id, department.name) }}
                              expanded={!!expandedWeeks[getDeptKey(division.id, banner.id, department.id)]}
                               onToggleWeeks={() => toggleWeeks(division.id, banner.id, department.id)}
                              expandedWeeks={!!expandedWeeks[getDeptKey(division.id, banner.id, department.id)]}
                            />
                            {department.periods && department.periods.map(period => (
                              <React.Fragment key={period.id}>
                                <AllocatrPeriodRow
                                  period={period}
                                />
                                 {getWeeks(division.id, banner.id, department.id, period.periodNumber)}
                              
                              </React.Fragment>
                            ))}
                          </React.Fragment>
                        ))}
                      </React.Fragment>
                    );
                  })}
                  {/* If there is a banner with id '00', display its departments/periods directly under the division */}
                  {division.banners.filter(banner => banner.id === '00').flatMap(banner => banner.departments || []).map(department => (
                    <React.Fragment key={department.id}>
                      <AllocatrDepartmentRow
                        department={{ ...department, name: getDeptName(department.id, department.name) }}
                        expanded={!!expandedWeeks[getDeptKey(division.id, '00', department.id)]}
                         onToggleWeeks={() => toggleWeeks(division.id, '00', department.id)}
                        expandedWeeks={!!expandedWeeks[getDeptKey(division.id, '00', department.id)]}
                      />
                      {department.periods && department.periods.map(period => (
                        <React.Fragment key={period.id}>
                          <AllocatrPeriodRow
                            period={period}
                          />
                           {getWeeks(division.id, '00', department.id, period.periodNumber)}  
                          
                        </React.Fragment>
                      ))}
                    </React.Fragment>
                  ))}
                </>
              ) : null
            )}
          </React.Fragment>
        );
      })}
    </tbody>
  );

  return (
    <div ref={containerRef} style={{ maxHeight: 'calc(100vh - 280px)', overflowY: 'auto', overflowX: 'auto', width: '100%' }}>
      {isLoading ? <Spinner /> :
        <table className="text-sm/[0px] allocatr-insights-table">
          {tableHeader}
          {tableBody}
        </table>
      }
    </div>
  );
};

export default AllocatrInsightsTable;
