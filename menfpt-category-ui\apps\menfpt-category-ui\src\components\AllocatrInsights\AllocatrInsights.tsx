import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
import { useGetAllocatrTableDataMutation, useGetDisplayDateQuery } from '../../server/Api/menfptCategoryAPI';
import Spinner from '@albertsons/uds/molecule/Spinner';
import { allocatrDashboardTableQuery } from '../../server/Query/allocatrDashboardTableQuery';
import { AllocatrDashboardReq, DepartmentData, AllocatrInsightsResponse } from '../../interfaces/allocatr-insights';
import AllocatrInsightsTable from './AllocatrInsightsTable';
import { worksheetFilterConfig } from '../../features/worksheetFilter/worksheetFilterConfig';
import { getAllCategoriesFromFilter, getSmicsForDeptsAndDivisions } from './AllocatrInsightsHelper';
import {
  createQtrPayloadForCalendar,
  handleCalendarApiResp,
  useCurrentQuarterNbr,
} from '../../features/calendarServiceUtils';
import { useDispatch } from 'react-redux';
import { setDataForQrtrDisplayedInTable } from '../quarterDetails.slice';
import { addDaysToDate } from '../../util/dateUtils';
import {
  getLastFriday,
  getLastFridayInQuarter,
  getNextMondayAfterQuarter,
  getFridayOfSelectedWeek,
} from './utils/getLastFriday';
import { subtractForecastVarianceData } from './Forecastvariance';
import { utcToZonedTime, format } from 'date-fns-tz';
import { mockAllocatrInsightsData } from '../../mock/allocatr-insights-mock';
import {
  getPerformanceVarianceSnapshotTimestamps,
  formatSnapshotTimestamps
} from './utils/performanceVarianceUtils';
import { useExtractBannerId } from '../../util/filterUtils';
const DAY_END_TS = 'T23:59:59.000';
const PERFORMANCE_SUMMARY = 'Performance Summary';
const FORECAST_VARIANCE = 'Performance Variance';
const LATEST_DATA = 'Latest data';
const QUARTER_CHANGE = 'quarterchange';
const DROPDOWN = 'dropdown';

interface AllocatrInsightsProps {
  selectedTab: string;
  onDataLoaded?: (data: any) => void;
}

const AllocatrInsights: React.FC<AllocatrInsightsProps> = ({ onDataLoaded, selectedTab }) => {
  const dispatch = useDispatch();
  const [departments, setDepartments] = useState<DepartmentData[]>([]);
  const [insightsData, setInsightsData] = useState<AllocatrInsightsResponse>(
    { id: '', name: '', divisions: [] }
  );
  const [lastFridayDepartments, setLastFridayDepartments] = useState<AllocatrInsightsResponse[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { data: appliedFilters } = useSelectorWrap('appliedFilter_rn');
  const { data: worksheetFilters } = useSelectorWrap('workSheetFilterList_rn') || {};
  const { data: selectedWeek } = useSelectorWrap('saveWeekSelection_rn');
  const smicData = worksheetFilters?.smicData || [];
  const [getAllocatrTableData] = useGetAllocatrTableDataMutation();
  const [calendarApiPayload, setCalendarApiPayload] = useState<any | null>(null);
  const { data: calendarApiResp } = useGetDisplayDateQuery(calendarApiPayload, {
    skip: !calendarApiPayload,
  });
  const { filterPg, department, timeframe, periods, selectedSm } = appliedFilters || {};
  const prevSelectedWeek = useRef<typeof selectedWeek | null>(null);
  const { data: CalendarWeek } = useSelectorWrap('dataForQrtrDisplayedInTable_rn') || {};
  const prevTimeframe = useRef<typeof Number | null>(null);
  const currentQuarterNbr = useCurrentQuarterNbr();
  const { data: displayDate } = useSelectorWrap('displayDate_rn');
  const currentWeekFiscalNumber = displayDate?.fiscalWeekNumber;
  const timeZone = 'America/Los_Angeles';
  const weekNumbersInQuarter = Array.isArray(CalendarWeek)
    ? CalendarWeek.map((w: any) => w.fiscalWeekNumber)
    : [];
  const currentWeekIndexInQuarter = currentWeekFiscalNumber
    ? weekNumbersInQuarter.findIndex((num: number) => num === currentWeekFiscalNumber) + 1
    : 0;
  const totalWeeksInQuarter = weekNumbersInQuarter.length;
  const divisionBannerPairs = useExtractBannerId();

  const getRequestedQuarterNbr = React.useCallback(() => appliedFilters.timeframe?.num || 1, [appliedFilters]);

  const getAllPreviousweeks = (selectedWeek: any) => {
    if (!Array.isArray(CalendarWeek) || !selectedWeek) return [];
    const weekNum = parseInt(selectedWeek.value.split('/')[2] + '' + selectedWeek.name.split(' ')[2]);
    if (isNaN(weekNum)) return [];
    return CalendarWeek.filter((week: { fiscalWeekNumber: number }) => week.fiscalWeekNumber <= weekNum).map(
      (week: { fiscalWeekNumber: number }) => week.fiscalWeekNumber
    );
  };
  const getWorksheetTable = async (deptIds: string[]) => {
    const currentFiscalYearNbr = appliedFilters?.timeframe?.fiscalYear;
    const quarterNbr = getRequestedQuarterNbr();
    const selectedPeriods = appliedFilters?.periods?.map(item => item.num) || [];
    const selectedWeeks = appliedFilters?.selectedWeeks?.map(item => item.weekNum) || [];
    // const divisionIds =
    //   appliedFilters?.division?.map((item: { num: number | string }) =>
    //     item.num ? item.num.toString() : 0
    //   ) || [];

    const getLatestReleaseWeek = () => {
      if (!selectedWeek || selectedWeek.name === LATEST_DATA) return {};
      return {
        latestReleaseWeek: {
          value: addDaysToDate(selectedWeek.value, 2),
          name: selectedWeek.name,
        },
        weekNumbers: getAllPreviousweeks(selectedWeek),
      };
    };

    if (selectedTab === PERFORMANCE_SUMMARY) {
      const filterArgs = {
        query: allocatrDashboardTableQuery,
        variables: {
          allocatrDashboardReq: {
            currentFiscalYearNbr,
            quarterNbr,
            deptIds,
            divisionBannerPairs,
            smicCategoryIds: getAllCategoriesFromFilter(appliedFilters),
            type: '',
            periodNumbers: selectedPeriods,
            filteredWeekNumbers: selectedWeeks,
             ...getLatestReleaseWeek(),
          },
        },
      };
      return await getAllocatrTableData(filterArgs);
    }

    if (selectedTab === FORECAST_VARIANCE) {
      const appliedFiscalQtrNbr = appliedFilters?.timeframe?.fiscalQuarterNumber || appliedFilters?.timeframe?.num;

      // Get snapshot timestamps using modular utility function
      const snapshotTimestamps = getPerformanceVarianceSnapshotTimestamps({
        selectedWeek,
        currentQuarterNbr,
        appliedFiscalQtrNbr,
        appliedFilters,
        weekNumbersInQuarter,
        currentWeekIndexInQuarter,
        totalWeeksInQuarter,
        getAllPreviousweeks,
        timeZone
      });

      // Format timestamps for API payload
      const { snapshotFridayPT, snapshotPT } = formatSnapshotTimestamps(snapshotTimestamps, timeZone);
      const { weekNumbersPayload } = snapshotTimestamps;
      const filterArgsWithSnapshot = {
        query: allocatrDashboardTableQuery,
        variables: {
          allocatrDashboardReq: {
            currentFiscalYearNbr,
            quarterNbr,
            deptIds,
            divisionBannerPairs,
            smicCategoryIds: getAllCategoriesFromFilter(appliedFilters),
            type: '',
            weekNumbers: weekNumbersPayload,
            filteredWeekNumbers: selectedWeeks,
            periodNumbers: selectedPeriods,
             latestReleaseWeek: {
             name: 'Last Friday',
             value: snapshotFridayPT,
        },
          },
        },
      };

      const filterArgsWithoutSnapshot = {
        query: allocatrDashboardTableQuery,
        variables: {
          allocatrDashboardReq: {
            currentFiscalYearNbr,
            quarterNbr,
            deptIds,
            divisionBannerPairs,
            smicCategoryIds: getAllCategoriesFromFilter(appliedFilters),
            type: '',
            weekNumbers: [],
            filteredWeekNumbers: selectedWeeks,
            periodNumbers: selectedPeriods,
            latestReleaseWeek: {
            name: 'Current/Monday',
             value: snapshotPT,
        },
          },
        },
      };
      const [lastFridayData, currentData] = await Promise.all([
        getAllocatrTableData(filterArgsWithSnapshot),
        getAllocatrTableData(filterArgsWithoutSnapshot),
      ]);
      const lastFridayArray =
        'data' in lastFridayData && lastFridayData.data?.getAllocatrDashboardTableData?.allocatrDashboardTableData
          ? lastFridayData.data.getAllocatrDashboardTableData.allocatrDashboardTableData
          : [];

      const currentArray =
        'data' in currentData && currentData.data?.getAllocatrDashboardTableData?.allocatrDashboardTableData
          ? currentData.data.getAllocatrDashboardTableData.allocatrDashboardTableData
          : [];
      const variance = subtractForecastVarianceData([currentArray], [lastFridayArray], currentQuarterNbr === appliedFiscalQtrNbr);
      return variance;
    }
  };

  const fetchAllocatrDashboardTableData = () => {
    setIsLoading(true);
    let deptIds: string[] = [];

    if (Array.isArray(appliedFilters?.department)) {
      deptIds = appliedFilters.department.map((dept: { num: string | number }) => dept.num.toString());
    } else if (appliedFilters?.department?.num) {
      deptIds = [appliedFilters.department.num.toString()];
    }
    if (deptIds.length !== 0) {
        getWorksheetTable(deptIds).then(response => {
          setIsLoading(false);
          if (selectedTab === PERFORMANCE_SUMMARY) {
          if (response && 'data' in response) {
            const formattedTableData = response.data?.getAllocatrDashboardTableData?.allocatrDashboardTableData;
            if (formattedTableData) {
              if (Array.isArray(formattedTableData)) {
                if (formattedTableData.length > 0) {
                  setInsightsData(formattedTableData[0]);
                } 
              }
               else if (typeof formattedTableData === 'object') {
                setInsightsData(formattedTableData);
              } 
            } 
          } 
          handleCalendarApiResp({ calendarApiResp, dispatch });
        }
           if (selectedTab === FORECAST_VARIANCE) {
          if (Array.isArray(response)) {
            setLastFridayDepartments(
              response.map((item: any) => ({ ...item }))
            );
          } 
          else {
            setLastFridayDepartments([]);
          }
        }
        handleCalendarApiResp({ calendarApiResp, dispatch });
      });
      }
    };
  const isDashboardPage = () => {
    return filterPg === worksheetFilterConfig.lsKeyDashboardPg;
  }

  useEffect(() => {
    if (isDashboardPage()) {
      if (appliedFilters?.timeframe?.num === prevTimeframe.current) {
        fetchAllocatrDashboardTableData();
      } else {
        setCalendarApiPayload(createQtrPayloadForCalendar(getRequestedQuarterNbr()));
      }
      prevTimeframe.current = appliedFilters?.timeframe?.num || null;
    }
  }, [filterPg, department, timeframe, periods, selectedTab, selectedSm]);

  useEffect(() => {
    if(isDashboardPage()) {
      dispatch(setDataForQrtrDisplayedInTable(calendarApiResp));
    }
  }, [calendarApiResp]);

  useEffect(() => {
    if (isDashboardPage() && selectedWeek?.from === QUARTER_CHANGE && JSON.stringify(selectedWeek) !== JSON.stringify(prevSelectedWeek.current)) {
      fetchAllocatrDashboardTableData();
      prevSelectedWeek.current = selectedWeek;
    }
  }, [CalendarWeek, selectedWeek]);

  useEffect(() => {
    if (isDashboardPage() && selectedWeek?.from === DROPDOWN && JSON.stringify(selectedWeek) !== JSON.stringify(prevSelectedWeek.current)) {
      fetchAllocatrDashboardTableData();
      prevSelectedWeek.current = selectedWeek;
    }
  }, [selectedWeek]);

useEffect(() => {
    if (onDataLoaded) {
      if (selectedTab === PERFORMANCE_SUMMARY) {
        onDataLoaded(insightsData);
      } else if (selectedTab === FORECAST_VARIANCE) {
        onDataLoaded(lastFridayDepartments.length > 0 ? lastFridayDepartments[0] : lastFridayDepartments);
      }
    }
  }, [insightsData, lastFridayDepartments, onDataLoaded, selectedTab]);

  return (
    <div className="flex-1 w-full h-full relative">
      <div className="table-container overflow-hidden">
        {isLoading ? (
          <Spinner />
        ) : (
          <AllocatrInsightsTable
            insightsData={
              selectedTab === PERFORMANCE_SUMMARY
                ? insightsData
                : selectedTab === FORECAST_VARIANCE
                ? lastFridayDepartments[0] || { id: '', name: '', divisions: [], periods: [], weeks: [] }
                : { id: '', name: '', divisions: [], periods: [], weeks: [] }
            }
            selectedTab={selectedTab}
          />
        )}
      </div>
    </div>
  );
};

export default AllocatrInsights;
