
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/features/worksheetFilter/components/roles/rolesUtils.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../index.html">All files</a> / <a href="index.html">src/features/worksheetFilter/components/roles</a> rolesUtils.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">40.54% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>15/37</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">40% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>8/20</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">50% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>6/12</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">44.11% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>15/34</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">10x</span>
<span class="cline-any cline-yes">10x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">12x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">94x</span>
<span class="cline-any cline-yes">94x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">172x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">94x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">94x</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import { SmDataType } from "../../types/smTypes";
&nbsp;
export const filterDesksByDivisionAndDept = (selectedDeptId, deptListForSelectedDivision) =&gt; {
  return deptListForSelectedDivision.filter(obj =&gt; {
    return selectedDeptId?.includes(obj.num) || <span class="branch-1 cbranch-no" title="branch not covered" >obj.num === selectedDeptId;</span>
  });
};
&nbsp;
export const getSmDataByDivisionAndDept = ({ selectedDeptId, deptListForSelectedDivision }) =&gt; {
  if (!selectedDeptId || !deptListForSelectedDivision || !Array.isArray(deptListForSelectedDivision) || deptListForSelectedDivision.length === 0) {
    return [];
  }
  const selectedDepartmentsData = filterDesksByDivisionAndDept(selectedDeptId, deptListForSelectedDivision);
<span class="cstat-no" title="statement not covered" >  if (!selectedDepartmentsData || selectedDepartmentsData.length === 0) {</span>
<span class="cstat-no" title="statement not covered" >    return [];</span>
  }
<span class="cstat-no" title="statement not covered" >  return extractSmDataFromDesks(selectedDepartmentsData);</span>
};
&nbsp;
export const extractSmDataFromDesks = <span class="fstat-no" title="function not covered" >(s</span>electedDepartmentsData) =&gt; {  
  //get deskName from the selectedDepartmentsData
  const deskNamesForSelectedDept = <span class="cstat-no" title="statement not covered" >selectedDepartmentsData.flatMap(<span class="fstat-no" title="function not covered" >ob</span>j =&gt; <span class="cstat-no" title="statement not covered" >obj.deskNameArr || [])</span>;</span>
  
  // Create a map to group ASMs by SM
  const smAsmMap: SmDataType = <span class="cstat-no" title="statement not covered" >new Map();</span>
  
  // Process each desk name and group ASMs by SM
<span class="cstat-no" title="statement not covered" >  deskNamesForSelectedDept.forEach(<span class="fstat-no" title="function not covered" >de</span>sk =&gt; {</span>
    const parts = <span class="cstat-no" title="statement not covered" >desk.split('-');</span>
    const sm = <span class="cstat-no" title="statement not covered" >parts[2];</span>
    const asm = <span class="cstat-no" title="statement not covered" >parts[3];</span>
&nbsp;
    let asmSet = <span class="cstat-no" title="statement not covered" >smAsmMap.get(sm);</span>
<span class="cstat-no" title="statement not covered" >    if (!asmSet) {</span>
<span class="cstat-no" title="statement not covered" >      asmSet = new Set();</span>
    }
<span class="cstat-no" title="statement not covered" >    if (asm) {</span>
<span class="cstat-no" title="statement not covered" >      asmSet.add(asm);</span>
    }
<span class="cstat-no" title="statement not covered" >    smAsmMap.set(sm, asmSet);</span>
  });
  
<span class="cstat-no" title="statement not covered" >  return smAsmMap;</span>
};
&nbsp;
export const getAsmListForSelectedSM = <span class="fstat-no" title="function not covered" >({</span>selectedSm, smData}) =&gt; {
  const asmListForSelectedSM = <span class="cstat-no" title="statement not covered" >smData.filter(<span class="fstat-no" title="function not covered" >ob</span>j =&gt; <span class="cstat-no" title="statement not covered" >obj.sm === selectedSm)</span>.map(<span class="fstat-no" title="function not covered" >ob</span>j =&gt; <span class="cstat-no" title="statement not covered" >obj.asmArr)</span>.flat();</span>
<span class="cstat-no" title="statement not covered" >  return asmListForSelectedSM;</span>
}
&nbsp;
export const formatName = (name: string | string[]) =&gt; {
  const formatString = (str: string) =&gt; {
    return str.toLowerCase()
      .split(' ')
      .map(word =&gt; word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };
&nbsp;
  <span class="missing-if-branch" title="if path not taken" >I</span>if (Array.isArray(name)) {
<span class="cstat-no" title="statement not covered" >    return formatString(name.join(' '));</span>
  }
  return formatString(name);
};</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-17T06:07:00.681Z
            </div>
        <script src="../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../sorter.js"></script>
        <script src="../../../../../block-navigation.js"></script>
    </body>
</html>
    