import '@testing-library/jest-dom';
import React from 'react';
import { render, screen } from '@testing-library/react';
import EPBCSSyncMonitor from '../EPBCSSyncMonitor';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import menfptCategoryApi, { useGetJobRunsFromDatabricksQuery } from '../../server/Api/menfptCategoryAPI';
import { useSelectorWrap } from '../../rtk/rtk-utilities';

jest.mock('@albertsons/uds/molecule/Tag', () => (props: any) => (
  <div data-testid={`tag-${props.preset}`}>{props.label}</div>
));
jest.mock('../../rtk/rtk-utilities', () => ({
  useSelectorWrap: jest.fn(),
}));
jest.mock('../../server/Api/menfptCategoryAPI', () => ({
 ...jest.requireActual('../../server/Api/menfptCategoryAPI'),
 useGetJobRunsFromDatabricksQuery: jest.fn(),
}));

const mockDisplayDate = { fiscalWeekNumber: 104 };
const mockSyncData = {
  getJobRunsFromDatabricks: {
    nextSync: { sync_day: 'Friday', sync_time: '10:00' },
    lastSync: { sync_day: 'Thursday', sync_time: '09:00' },
    syncSessions: [
      { time: '08:00', status: 'Completed', day: 'Monday', date: '2024-06-10' },
      { time: '09:00', status: 'Processing', day: 'Monday', date: '2024-06-10' },
      { time: '10:00', status: 'Failed', day: 'Monday', date: '2024-06-10' },
      { time: '11:00', status: 'Unknown', day: 'Monday', date: '2024-06-10' },
    ],
    syncHistory: {
      weeks: [
        { lastRun: { date: '2024-06-07', time: '10:00', weekNumber: 23 } },
        { lastRun: { date: '2024-05-31', time: '09:00', weekNumber: 22 } },
      ],
    },
  }
};

const mockStore = configureStore({ 
  reducer: { 
    [menfptCategoryApi.reducerPath]: menfptCategoryApi.reducer 
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(menfptCategoryApi.middleware),
});

const renderWithProvider = (ui: React.ReactElement) =>
  render(<Provider store={mockStore}>{ui}</Provider>);

describe('EPBCSSyncMonitor', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useSelectorWrap as jest.Mock).mockReturnValue({ data: mockDisplayDate });
    (useGetJobRunsFromDatabricksQuery as jest.Mock).mockReturnValue({ data: mockSyncData, isLoading: false });
  });

  it('renders sync history rows', async () => {
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findByText((content) => content.includes('Sync History'))).toBeInTheDocument();
    expect(screen.getByText((content) => content.includes('Last Sync Time'))).toBeInTheDocument();
    expect(screen.getAllByText((content) => content.trim() === 'Week').length).toBeGreaterThan(0);
  });


  it('renders fallback for missing syncData', async () => {
    (useGetJobRunsFromDatabricksQuery as jest.Mock).mockReturnValue({ data: undefined, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findAllByText((content) => content.trim() === '-')).toHaveLength(2);
  });

  it('renders only summary cards and no sync history or sessions when all data is missing', async () => {
    (useSelectorWrap as jest.Mock).mockReturnValue({ data: undefined });
    (useGetJobRunsFromDatabricksQuery as jest.Mock).mockReturnValue({ data: undefined, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(screen.queryByText((content) => content.includes('Week 23'))).toBeNull();
    expect(screen.queryByText((content) => content.includes('Week 22'))).toBeNull();
    expect(screen.queryByText((content) => content.includes('Completed'))).toBeNull();
    expect(screen.queryByText((content) => content.includes('Processing'))).toBeNull();
    expect(screen.queryByText((content) => content.includes('Fail'))).toBeNull();
  });

  it('renders correct week number when fiscalWeekNumber is a three-digit number', async () => {
    (useSelectorWrap as jest.Mock).mockReturnValue({ data: { fiscalWeekNumber: 104 } });
    (useGetJobRunsFromDatabricksQuery as jest.Mock).mockReturnValue({ data: mockSyncData, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findByText((content) => content.trim() === '4')).toBeInTheDocument();
  });
  it('renders spinner when loading', async () => {
    (useGetJobRunsFromDatabricksQuery as jest.Mock).mockReturnValue({ data: undefined, isLoading: true });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(document.querySelector('.animate-spin-slow')).toBeInTheDocument();
  });
  });

  it('renders fallback for empty syncHistory', async () => {
    const data = JSON.parse(JSON.stringify(mockSyncData));
    data.getJobRunsFromDatabricks.syncHistory.weeks = [];
    (useGetJobRunsFromDatabricksQuery as jest.Mock).mockReturnValue({ data, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(screen.queryByText((content) => content.includes('Week 23'))).toBeNull();
    expect(screen.queryByText((content) => content.includes('Week 22'))).toBeNull();
  });

  it('handles syncSessions with missing fields gracefully', async () => {
    const data = JSON.parse(JSON.stringify(mockSyncData));
    data.getJobRunsFromDatabricks.syncSessions = [
      { time: undefined, status: undefined, day: undefined, date: undefined },
    ];
    (useGetJobRunsFromDatabricksQuery as jest.Mock).mockReturnValue({ data, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findByText((content) => content.includes('Sync Day'))).toBeInTheDocument();
  });

  it('handles missing fiscalWeekNumber', async () => {
    (useSelectorWrap as jest.Mock).mockReturnValue({ data: {} });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findByText((content) => content.trim() === '4')).toBeInTheDocument();
  });

  it('renders unknown status with no tag', async () => {
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(screen.queryByTestId('tag-unknown')).toBeNull();
  });

  it('renders the component without crashing', async () => {
    (useGetJobRunsFromDatabricksQuery as jest.Mock).mockReturnValue({ data: mockSyncData, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    const weekHeaders = await screen.findAllByText('Week');
    expect(weekHeaders.length).toBeGreaterThan(0);
  });

  it('renders no Completed/Processing/Failed tags if syncSessions are empty', async () => {
    const data = JSON.parse(JSON.stringify(mockSyncData));
    data.getJobRunsFromDatabricks.syncSessions = [];
    (useGetJobRunsFromDatabricksQuery as jest.Mock).mockReturnValue({ data, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(screen.queryByTestId('tag-green')).toBeNull();
    expect(screen.queryByTestId('tag-yellow')).toBeNull();
    expect(screen.queryByTestId('tag-red')).toBeNull();
  });

  it('renders no tag for unknown status', async () => {
    const data = JSON.parse(JSON.stringify(mockSyncData));
    data.getJobRunsFromDatabricks.syncSessions = [
      { time: '11:00', status: 'Unknown', day: 'Monday', date: '2024-06-10' }
    ];
    (useGetJobRunsFromDatabricksQuery as jest.Mock).mockReturnValue({ data, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(screen.queryByTestId('tag-unknown')).toBeNull();
  });

  it('shows the correct current week number', async () => {
    (useGetJobRunsFromDatabricksQuery as jest.Mock).mockReturnValue({ data: mockSyncData, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findByText('4')).toBeInTheDocument();
  });

  it('renders the Sync Day and Scheduled time headers', async () => {
    (useGetJobRunsFromDatabricksQuery as jest.Mock).mockReturnValue({ data: mockSyncData, isLoading: false });
    renderWithProvider(<EPBCSSyncMonitor />);
    expect(await screen.findByText('Sync Day')).toBeInTheDocument();
    expect(await screen.findByText('Scheduled time')).toBeInTheDocument();
  });
});
