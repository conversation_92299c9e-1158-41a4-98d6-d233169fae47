[1m[2mDetermining test suites to run...[22m[22m[999D[K

[K
[1A[999D[K

[K
[1ABrowserslist: browsers data (caniuse-lite) is 7 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme


[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A[999D[K
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A  [2m[33mconsole.warn[39m[22m
[33m    Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments.[39m
[2m[22m
[2m    [0m [90m 10 |[39m [36mconst[39m entitlementApi [33m=[39m createApi({[22m
[2m     [90m 11 |[39m   reducerPath[33m:[39m [32m'menfptCategoryApi'[39m[33m,[39m[22m
[2m    [31m[1m>[22m[2m[39m[90m 12 |[39m   baseQuery[33m:[39m fetchBaseQuery({[22m
[2m     [90m    |[39m                            [31m[1m^[22m[2m[39m[22m
[2m     [90m 13 |[39m     baseUrl[33m:[39m [32m`${getEnvParamVal('MENFPT_GRAPHQL_ENDPOINT')}/api`[39m[22m
[2m     [90m 14 |[39m   })[33m,[39m[22m
[2m     [90m 15 |[39m   tagTypes[33m:[39m [[32m'DisplayDate'[39m[33m,[39m [32m'WorkSheetFilter'[39m[33m,[39m [32m'CombinedFiltersAndQuarters'[39m][33m,[39m[0m[22m
[2m[22m
[2m      [2mat fetchBaseQuery ([22m[2m../../node_modules/@reduxjs/toolkit/src/query/fetchBaseQuery.ts[2m:211:13)[22m[2m[22m
[2m      [2mat Object.<anonymous> ([22m[2msrc/server/Api/menfptCategoryAPI.ts[2m:12:28)[22m[2m[22m
[2m      [2mat [22m[2m[0m[36msrc/features/_tests_/EPCBSSyncMonitor.spec.tsx[39m[0m[2m:18:10[22m[2m[22m
[2m      [2mat Object.<anonymous> ([22m[2msrc/features/EPBCSSyncMonitor.tsx[2m:5:1)[22m[2m[22m
[2m      [2mat Object.<anonymous> ([22m[2m[0m[36msrc/features/_tests_/EPCBSSyncMonitor.spec.tsx[39m[0m[2m:4:1)[22m[2m[22m


[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A[0m[7m[1m[32m PASS [39m[22m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m ([0m[1m[41m10.505 s[49m[22m[0m)

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A  EPBCSSyncMonitor

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A    [32m√[39m [2mrenders sync history rows (37 ms)[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A    [32m√[39m [2mrenders fallback for missing syncData (4 ms)[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A    [32m√[39m [2mrenders only summary cards and no sync history or sessions when all data is missing (5 ms)[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A    [32m√[39m [2mrenders correct week number when fiscalWeekNumber is a three-digit number (6 ms)[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A    [32m√[39m [2mrenders spinner when loading (3 ms)[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A    [32m√[39m [2mrenders fallback for empty syncHistory (10 ms)[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A    [32m√[39m [2mhandles syncSessions with missing fields gracefully (5 ms)[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A    [32m√[39m [2mhandles missing fiscalWeekNumber (6 ms)[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A    [32m√[39m [2mrenders unknown status with no tag (5 ms)[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A    [32m√[39m [2mrenders the component without crashing (5 ms)[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A    [32m√[39m [2mrenders no Completed/Processing/Failed tags if syncSessions are empty (4 ms)[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A    [32m√[39m [2mrenders no tag for unknown status (5 ms)[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A    [32m√[39m [2mshows the correct current week number (5 ms)[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A    [32m√[39m [2mrenders the Sync Day and Scheduled time headers (6 ms)[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [0m[7m[37m menfpt-category-ui [39m[27m[0m [2mapps/menfpt-category-ui/src/features/_tests_/[22m[1mEPCBSSyncMonitor.spec.tsx[22m

[K
[1A
[K
[1A[999D[K[1m[2mRunning coverage on untested files...[22m[22m[999D[K-----------------------------------------------------|---------|----------|---------|---------|-------------------
File                                                 | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s 
-----------------------------------------------------|---------|----------|---------|---------|-------------------
[31;1mAll files                                           [0m | [31;1m   1.28[0m | [31;1m    1.24[0m | [31;1m   1.35[0m | [31;1m   1.25[0m | [31;1m                 [0m 
[31;1m src                                                [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  app-main-container.tsx                            [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m19-69            [0m 
[31;1m  app.tsx                                           [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m18               [0m 
  remote-entry.ts                                    |       0 |        0 |       0 |       0 | [31;1m                 [0m 
[31;1m src/components                                     [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  AlertMessage.tsx                                  [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m12-13            [0m 
[31;1m  ForecastTimestampsFooter.tsx                      [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m11-22            [0m 
[31;1m  StatusCellIcon.tsx                                [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m10-63            [0m 
[31;1m  activeQuarterTab.slice.ts                         [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m5-16             [0m 
[31;1m  confirmationModal.tsx                             [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m13-25            [0m 
[31;1m  quarterDetails.slice.ts                           [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m5-37             [0m 
[31;1m  quarterTabs.tsx                                   [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m12-92            [0m 
[31;1m  renderRowCloseStatus.tsx                          [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m27-102           [0m 
[31;1m  udsTable.tsx                                      [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m67-746           [0m 
[31;1m src/components/Admin                               [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  access-control-card-header.tsx                    [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m19-51            [0m 
[31;1m  access-control-card.tsx                           [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m11-129           [0m 
[31;1m  access-control-drawer.tsx                         [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m21-117           [0m 
[31;1m  access-control-table.tsx                          [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m15-243           [0m 
[31;1m  addUserForm.tsx                                   [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m17-68            [0m 
[31;1m  addUserModal.tsx                                  [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m11-29            [0m 
[31;1m  admin-tabs.tsx                                    [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m11-14            [0m 
[31;1m  admin.tsx                                         [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m6-27             [0m 
[31;1m  deactivateAndReplaceUserModal.tsx                 [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m40-117           [0m 
[31;1m  deactivateUserModal.tsx                           [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m26-73            [0m 
[31;1m  editUserAccess.tsx                                [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m15-118           [0m 
[31;1m  editUserDetailsModal.tsx                          [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m13-45            [0m 
[31;1m src/components/Admin/shared                        [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  DeactivateUserFormConfig.tsx                      [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m3                [0m 
[31;1m  DeactivatedUserSection.tsx                        [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m21-26            [0m 
[31;1m  DynamicFormFields.tsx                             [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m17-120           [0m 
[31;1m  FormActions.tsx                                   [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m4-42             [0m 
[31;1m  FormFields.tsx                                    [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m6-92             [0m 
[31;1m  InfoIcon.tsx                                      [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m4-15             [0m 
[31;1m  ReplacementUserFormConfig.tsx                     [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m3                [0m 
[31;1m  ReplacementUserSection.tsx                        [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m29-115           [0m 
[31;1m  formConfig.ts                                     [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m20               [0m 
[31;1m  formUtils.ts                                      [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m4-13             [0m 
[31;1m  useFormData.ts                                    [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m11-29            [0m 
[31;1m  validationSchemas.ts                              [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m4-11             [0m 
[31;1m src/components/AllocatrInsights                    [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  ActualUsedIndicator.tsx                           [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m3-4              [0m 
[31;1m  AllocatrBannerRow.tsx                             [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m13-31            [0m 
[31;1m  AllocatrDepartmentRow.tsx                         [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m21-38            [0m 
[31;1m  AllocatrDivisionRow.tsx                           [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m13-31            [0m 
[31;1m  AllocatrInsights.tsx                              [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m32-294           [0m 
[31;1m  AllocatrInsightsHelper.ts                         [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m3-39             [0m 
[31;1m  AllocatrInsightsTable.tsx                         [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m22-353           [0m 
[31;1m  AllocatrPeriodRow.tsx                             [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m16-23            [0m 
[31;1m  AllocatrTotalRow.tsx                              [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m15-17            [0m 
[31;1m  AllocatrWeekRow.tsx                               [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m16-28            [0m 
[31;1m  Forecastvariance.tsx                              [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m8-75             [0m 
[31;1m src/components/AllocatrInsights/utils              [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  getLastFriday.ts                                  [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m2-55             [0m 
[31;1m  insightsFormatters.ts                             [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m1-30             [0m 
[31;1m  performanceVarianceUtils.ts                       [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m4-90             [0m 
[31;1m  tableCell.tsx                                     [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m12-54            [0m 
[31;1m src/components/DashboardDownloadExcel              [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  DashboardDownloadExcel.tsx                        [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m7-322            [0m 
[31;1m  DashboardDownloadExcelHelper.tsx                  [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m4-167            [0m 
[31;1m  DashboardDownloadExcelPrint.tsx                   [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m1-10             [0m 
[31;1m src/components/Facilities                          [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  ClosingFacilities.tsx                             [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m26-308           [0m 
[31;1m  DynamicCardBottom.tsx                             [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m12-17            [0m 
[31;1m  DynamicCardSpacer.tsx                             [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m1-2              [0m 
[31;1m  DynamicCardTop.tsx                                [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m1-2              [0m 
[31;1m  ExpandedFacilitiesView.tsx                        [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m25-62            [0m 
[31;1m  FacilitiesTableCard.tsx                           [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m17-49            [0m 
[31;1m  IdFacilities.tsx                                  [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m24-204           [0m 
[31;1m  KeeperFacilities.tsx                              [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m22-193           [0m 
[31;1m  NewFacilities.tsx                                 [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m23-393           [0m 
[31;1m  ViewMoreFacilities.tsx                            [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m9-10             [0m 
[31;1m  ViewMoreIdFacilities.tsx                          [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m19-84            [0m 
[31;1m src/components/ForecastEdit                        [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  constants.ts                                      [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m6                [0m 
[31;1m  editForecast.slice.ts                             [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m4-13             [0m 
[31;1m  editForecastAdjustment.tsx                        [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m32-1765          [0m 
[31;1m  editForecastHelper.ts                             [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m10-61            [0m 
[31;1m  forecastCalculations.ts                           [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m54-112           [0m 
  types.ts                                           |       0 |        0 |       0 |       0 | [31;1m                 [0m 
[31;1m  useAdjustmentWorksheetData.ts                     [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m59-111           [0m 
[31;1m  weekSelection.tsx                                 [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m16-76            [0m 
[31;1m src/components/HelpIcon                            [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  HelpIcon.tsx                                      [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m14-38            [0m 
[31;1m src/components/InfoTooltip                         [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  InfoTooltip.tsx                                   [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m16-24            [0m 
[31;1m src/components/RouteProtection                     [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  RouteProtection.tsx                               [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m13-57            [0m 
[31;1m src/components/RxForecast                          [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  FileUploadBox.tsx                                 [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m8-57             [0m 
[31;1m  NoDocumentsMessage.tsx                            [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m3-4              [0m 
[31;1m  rxforecast-title-component.tsx                    [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m4-7              [0m 
[31;1m  rxforecast.tsx                                    [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m4-5              [0m 
[31;1m  rxforecastAlertMessage.tsx                        [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m6-29             [0m 
[31;1m  rxforecastAlerts.tsx                              [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m12-19            [0m 
[31;1m  rxforecastDocuments.tsx                           [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m15-232           [0m 
[31;1m  uploadDocument.components.tsx                     [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m17-76            [0m 
[31;1m  uploadDocument.hooks.ts                           [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m5-33             [0m 
[31;1m  uploadDocument.tsx                                [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m17-147           [0m 
  uploadDocument.types.ts                            |       0 |        0 |       0 |       0 | [31;1m                 [0m 
[31;1m  uploadDocument.utils.ts                           [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m6-54             [0m 
[31;1m src/components/SnapShotDropDown                    [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  release-week-select.tsx                           [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m15-129           [0m 
[31;1m  releaseWeek.slice.ts                              [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m3-13             [0m 
[31;1m src/components/_tests_                             [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  edit-forecast-adjustment-test-data.ts             [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m1-4459           [0m 
[31;1m  editForecastAdjustment-mock-test-expects.ts       [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m3-126            [0m 
[31;1m src/constants                                      [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  menfpt-category-constants.ts                      [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m1                [0m 
[31;1m  userRoles.ts                                      [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m1                [0m 
[31;1m src/environments                                   [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  environment.prod.ts                               [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m1                [0m 
[31;1m  environment.ts                                    [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m4                [0m 
[31;1m src/features                                       [0m | [31;1m  23.85[0m | [31;1m    19.8[0m | [31;1m  29.41[0m | [31;1m   22.7[0m | [31;1m                 [0m 
[32;1m  EPBCSSyncMonitor.tsx                              [0m | [32;1m  95.91[0m | [32;1m   95.23[0m | [32;1m    100[0m | [32;1m    100[0m | [33;1m38,152           [0m 
[31;1m  WorksheetHeaderControls.tsx                       [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m33-206           [0m 
[31;1m  calendarServiceUtils.ts                           [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m10-28            [0m 
[31;1m  historyDrawer.tsx                                 [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m19-70            [0m 
[31;1m  historyTimeline.tsx                               [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m13-210           [0m 
  menfpt-category.tsx                                |       0 |        0 |       0 |       0 | [31;1m                 [0m 
[31;1m src/features/envVariables                          [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  envVariables.slice.ts                             [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m5-27             [0m 
[31;1m  useEnvVariables.ts                                [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m6-20             [0m 
[31;1m src/features/periodClose                           [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  generatePeriodStatuses.ts                         [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m2-94             [0m 
[31;1m  periodClose.flags.ts                              [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m10-148           [0m 
[31;1m  periodClose.slice.ts                              [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m15-45            [0m 
[31;1m  periodCloseLastQrtr.utils.ts                      [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m6-61             [0m 
[31;1m  periodIcons.tsx                                   [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m3-23             [0m 
[31;1m  periodStatuses.slice.ts                           [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m5-14             [0m 
[31;1m  usePeriodCloseEffect.ts                           [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m9-64             [0m 
[31;1m src/features/periodClose/modals                    [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  PeriodCloseModal.tsx                              [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m3-10             [0m 
[31;1m  PeriodLockedModal.tsx                             [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m3-10             [0m 
[31;1m  periodModalsContainer.tsx                         [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m17-134           [0m 
[31;1m src/features/worksheetFilter                       [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  GetAdjustmentWorksheetData.mock.ts                [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m1                [0m 
[31;1m  categoryOperations.ts                             [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m5-170            [0m 
[31;1m  departmentUtils.ts                                [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m3-75             [0m 
[31;1m  deskNameUtils.ts                                  [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m7-36             [0m 
[31;1m  deskUtils.ts                                      [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m3-40             [0m 
[31;1m  divisionUtils.ts                                  [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m3-68             [0m 
[31;1m  roleCascadeSearchFocus.slice.ts                   [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m4-14             [0m 
[31;1m  searchUtils.ts                                    [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m3-79             [0m 
[31;1m  useCombinedFiltersAndQuarters.ts                  [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m17-98            [0m 
[31;1m  worksheetFilter.slice.ts                          [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m4-13             [0m 
[31;1m  worksheetFilterConfig.ts                          [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m3                [0m 
[31;1m  worksheetFilterContainer.tsx                      [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m21-195           [0m 
[31;1m  worksheetFilterContext.tsx                        [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m13-102           [0m 
[31;1m  worksheetFilterDisplay.tsx                        [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m9-81             [0m 
[31;1m  worksheetFilterModal.tsx                          [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m13-56            [0m 
[31;1m  worksheetFilterRouteUtils.ts                      [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m6-45             [0m 
  worksheetFilterTypes.ts                            |       0 |        0 |       0 |       0 | [31;1m                 [0m 
[31;1m  worksheetFilterUtils.ts                           [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m8-188            [0m 
[31;1m src/features/worksheetFilter/components            [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  alert-box.tsx                                     [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m11-18            [0m 
[31;1m  departmentDeskSelector.tsx                        [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m31-218           [0m 
[31;1m  deskSelection.tsx                                 [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m12-25            [0m 
[31;1m  filterData.mock.ts                                [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m1                [0m 
[31;1m  readOnlyList.tsx                                  [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m5-26             [0m 
[31;1m src/features/worksheetFilter/components/department [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  departmentHeader.tsx                              [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m7-8              [0m 
[31;1m  departmentList.tsx                                [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m18-81            [0m 
[31;1m  departmentSearch.tsx                              [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m11-22            [0m 
[31;1m  departmentSelection.effects.ts                    [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m9-23             [0m 
[31;1m  departmentSelection.handlers.ts                   [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m13-108           [0m 
[31;1m  departmentSelection.render.tsx                    [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m8-40             [0m 
[31;1m  departmentSelection.styles.ts                     [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m1-3              [0m 
[31;1m  departmentSelection.tsx                           [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m25-90            [0m 
[31;1m  departmentSelection.utils.ts                      [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m4-49             [0m 
[31;1m  departments.slice.ts                              [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m4-13             [0m 
[31;1m src/features/worksheetFilter/components/deptDesk   [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  departmentDeskSearchQuery.slice.ts                [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m4-17             [0m 
[31;1m  departmentDeskTabs.tsx                            [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m29-56            [0m 
[31;1m src/features/worksheetFilter/components/division   [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  bannerSelection.tsx                               [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m18-52            [0m 
[31;1m  division.slice.tsx                                [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m3-12             [0m 
[31;1m  divisionSelector.tsx                              [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m16-191           [0m 
[31;1m src/features/worksheetFilter/components/filterModal[0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  ModalCategorySection.tsx                          [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m11-17            [0m 
[31;1m  ModalContainer.tsx                                [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m4-12             [0m 
[31;1m  ModalContent.tsx                                  [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m12-114           [0m 
[31;1m  ModalDepartmentDeskSection.tsx                    [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m15-24            [0m 
  ModalDeptRoleCascadeSection.tsx                    |       0 |        0 |       0 |       0 | [31;1m                 [0m 
[31;1m  ModalDivisionSection.tsx                          [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m11-16            [0m 
[31;1m  ModalFooter.tsx                                   [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m7-59             [0m 
[31;1m  ModalHeader.tsx                                   [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m11-12            [0m 
[31;1m  ModalTimeframeSection.tsx                         [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m14-18            [0m 
[31;1m  useModalContentState.ts                           [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m16-129           [0m 
[31;1m src/features/worksheetFilter/components/period     [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  periodSelector.tsx                                [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m22-220           [0m 
[31;1m src/features/worksheetFilter/components/roles      [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  asmRoleUsersList.tsx                              [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m13-56            [0m 
[31;1m  clearSelection.tsx                                [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m8-10             [0m 
[31;1m  rolesFilter.slice.ts                              [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m4-25             [0m 
[31;1m  rolesUtils.ts                                     [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m3-62             [0m 
[31;1m  smRoleUsersList.tsx                               [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m21-110           [0m 
[31;1m src/features/worksheetFilter/components/shared     [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  SelectAllCheckbox.tsx                             [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m13-30            [0m 
[31;1m  SelectableList.tsx                                [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m24-132           [0m 
[31;1m  SelectableListItem.tsx                            [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m30-83            [0m 
[31;1m src/features/worksheetFilter/components/suggestions[0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  RoleSearchSuggestions.tsx                         [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m24-105           [0m 
[31;1m  deptRoleSuggestions.slice.ts                      [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m3-31             [0m 
[31;1m src/features/worksheetFilter/components/timeframe  [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  timeframeSelector.tsx                             [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m14-94            [0m 
[31;1m src/features/worksheetFilter/hooks                 [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  usePersistentSearchQuery.ts                       [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m9-26             [0m 
[31;1m  useWorksheetFilterState.ts                        [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m30-326           [0m 
 src/features/worksheetFilter/types                  |       0 |        0 |       0 |       0 | [31;1m                 [0m 
  smTypes.ts                                         |       0 |        0 |       0 |       0 | [31;1m                 [0m 
  timeframeTypes.ts                                  |       0 |        0 |       0 |       0 | [31;1m                 [0m 
[31;1m src/features/worksheetFilter/utils                 [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  quarterUtils.ts                                   [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m3-58             [0m 
[31;1m  serializationUtils.ts                             [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m6-46             [0m 
[31;1m src/hooks                                          [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  usePdfHelp.ts                                     [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m6-40             [0m 
 src/interfaces                                      |       0 |        0 |       0 |       0 | [31;1m                 [0m 
  EPBCSSyncMonitor.ts                                |       0 |        0 |       0 |       0 | [31;1m                 [0m 
  allocatr-insights.ts                               |       0 |        0 |       0 |       0 | [31;1m                 [0m 
  downloadFilePharma.ts                              |       0 |        0 |       0 |       0 | [31;1m                 [0m 
  edit-forecast-adjustments.ts                       |       0 |        0 |       0 |       0 | [31;1m                 [0m 
  envVariables.ts                                    |       0 |        0 |       0 |       0 | [31;1m                 [0m 
  forecast-adjustments.ts                            |       0 |        0 |       0 |       0 | [31;1m                 [0m 
  menfpt-category.ts                                 |       0 |        0 |       0 |       0 | [31;1m                 [0m 
  uploadFilePharma.ts                                |       0 |        0 |       0 |       0 | [31;1m                 [0m 
  worksheetFilter.ts                                 |       0 |        0 |       0 |       0 | [31;1m                 [0m 
[31;1m src/mock                                           [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  allocatr-insights-mock.ts                         [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m3-143            [0m 
[31;1m  forecast-changelog-mock.ts                        [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m3                [0m 
[31;1m  mockDashboardData.ts                              [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m2-27             [0m 
[31;1m src/pages                                          [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  PowerBiReport.tsx                                 [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m7-154            [0m 
[31;1m  access-denied.tsx                                 [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m5-9              [0m 
[31;1m  adjustment-worksheet.tsx                          [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m14-37            [0m 
[31;1m  dashboard-side-panel.tsx                          [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m5-6              [0m 
[31;1m  dashboard-tabs.tsx                                [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m34-185           [0m 
[31;1m  dashboard-title-component.tsx                     [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m12-133           [0m 
[31;1m  home.tsx                                          [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m11-18            [0m 
[31;1m  lagging-indicator-page.tsx                        [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m4-43             [0m 
[31;1m  mockWorsheetTableData.ts                          [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m1                [0m 
[31;1m  report.tsx                                        [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m7-18             [0m 
[31;1m  worksheet-table-container.tsx                     [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m25-338           [0m 
[31;1m src/rtk                                            [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  rtk-api.ts                                        [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m4-13             [0m 
[31;1m  rtk-slice.ts                                      [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m12-44            [0m 
[31;1m  rtk-utilities.ts                                  [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m3-10             [0m 
[31;1m  store.ts                                          [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m10-58            [0m 
[31;1m src/server/Api                                     [0m | [31;1m   9.67[0m | [31;1m       0[0m | [31;1m   3.44[0m | [31;1m   9.67[0m | [31;1m                 [0m 
[31;1m  menfptCategoryAPI.ts                              [0m | [31;1m   9.67[0m | [31;1m       0[0m | [31;1m   3.44[0m | [31;1m   9.67[0m | [31;1m18-188           [0m 
[31;1m src/server/Query                                   [0m | [31;1m  21.42[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m  21.42[0m | [31;1m                 [0m 
[32;1m  EPBCSSyncMonitorQuery.ts                          [0m | [32;1m    100[0m | [32;1m     100[0m | [32;1m    100[0m | [32;1m    100[0m | [33;1m                 [0m 
[31;1m  adjustmentWorksheetQuery.ts                       [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m1                [0m 
[31;1m  allocatrDashboardTableQuery.ts                    [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m1                [0m 
[31;1m  combinedFilterAndQuartersQuery.ts                 [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m1                [0m 
[31;1m  displayDateQuery.ts                               [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m1                [0m 
[32;1m  downloadFilePharmaQuery.ts                        [0m | [32;1m    100[0m | [32;1m     100[0m | [32;1m    100[0m | [32;1m    100[0m | [33;1m                 [0m 
[32;1m  envVariablesQuery.ts                              [0m | [32;1m    100[0m | [32;1m     100[0m | [32;1m    100[0m | [32;1m    100[0m | [33;1m                 [0m 
[31;1m  menfptCategoryQuery.ts                            [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m2                [0m 
[31;1m  menfptForecastChangeLog.ts                        [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m1                [0m 
[31;1m  menfptSaveAdjustmentQuery.ts                      [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m1                [0m 
[31;1m  menfptWorkSheetFiltersQuery.ts                    [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m1                [0m 
[31;1m  quartersInYearQuery.ts                            [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m1                [0m 
[31;1m  uploadFilePharmaQuery.ts                          [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m1                [0m 
[31;1m  userInfo.ts                                       [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m1                [0m 
[31;1m src/server/Reducer                                 [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m                 [0m 
[31;1m  menfpt-category.slice.ts                          [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m4-141            [0m 
[31;1m src/util                                           [0m | [31;1m   7.69[0m | [31;1m    5.95[0m | [31;1m   4.16[0m | [31;1m      8[0m | [31;1m                 [0m 
[31;1m  apiEndpoints.ts                                   [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m8-29             [0m 
[31;1m  authProvider.ts                                   [0m | [31;1m  16.66[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m  17.39[0m | [31;1m4-12,17-21,26-35 [0m 
[31;1m  dateUtils.ts                                      [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m10-28            [0m 
[33;1m  envVarsManager.ts                                 [0m | [33;1m  54.54[0m | [31;1m   35.71[0m | [33;1m     50[0m | [33;1m  54.54[0m | [31;1m3-6,18-20        [0m 
[31;1m  filterUtils.ts                                    [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m13-61            [0m 
[31;1m  getFiscalWeekNumber.ts                            [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m6-14             [0m 
[31;1m  routeConstants.ts                                 [0m | [31;1m      0[0m | [32;1m     100[0m | [32;1m    100[0m | [31;1m      0[0m | [31;1m1                [0m 
[31;1m  timeUtils.ts                                      [0m | [31;1m      0[0m | [31;1m       0[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m3-64             [0m 
[31;1m  useCleanupOnRouteChange.ts                        [0m | [31;1m      0[0m | [32;1m     100[0m | [31;1m      0[0m | [31;1m      0[0m | [31;1m13-24            [0m 
-----------------------------------------------------|---------|----------|---------|---------|-------------------

=============================== Coverage summary ===============================
[31;1mStatements   : 1.28% ( 63/4894 )[0m
[31;1mBranches     : 1.24% ( 45/3605 )[0m
[31;1mFunctions    : 1.35% ( 17/1251 )[0m
[31;1mLines        : 1.25% ( 58/4634 )[0m
================================================================================
[1mTest Suites: [22m[1m[32m1 passed[39m[22m, 1 total
[1mTests:       [22m[1m[32m14 passed[39m[22m, 14 total
[1mSnapshots:   [22m0 total
[1mTime:[22m        [1m[33m27.681 s[39m[22m
[2mRan all test suites[22m[2m matching [22m/EPCBSSyncMonitor/i[2m.[22m
