
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/AllocatrInsights/AllocatrInsightsTable.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">src/components/AllocatrInsights</a> AllocatrInsightsTable.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/112</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/81</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/45</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/99</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import React, { useState, useEffect, useRef } from 'react';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
import { ChevronsDownUp, ChevronsUpDown, Settings2 } from 'lucide-react';
import AllocatrDepartmentRow from './AllocatrDepartmentRow';
import { useGetAllocatrTableDataMutation } from '../../server/Api/menfptCategoryAPI';
import AllocatrPeriodRow from './AllocatrPeriodRow';
import Spinner from '@albertsons/uds/molecule/Spinner';
import AllocatrWeekRow from './AllocatrWeekRow';
import { AllocatrInsightsResponse, WeekData } from '../../interfaces/allocatr-insights';
import './AllocatrInsightsTable.scss';
import { toTitleCase } from '@ui/utils';
import { borderClass } from './AllocatrInsightsHelper';
import AllocatrDivisionRow from './AllocatrDivisionRow';
import AllocatrBannerRow from './AllocatrBannerRow';
import AllocatrTotalRow from './AllocatrTotalRow';
&nbsp;
interface AllocatrInsightsTableProps {
  insightsData: AllocatrInsightsResponse;
  selectedTab: String;
}
&nbsp;
const AllocatrInsightsTable: React.FC&lt;AllocatrInsightsTableProps&gt; = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >({</span> insightsData, selectedTab }) =&gt; {</span>
  const [expandedDivisions, setExpandedDivisions] = <span class="cstat-no" title="statement not covered" >useState&lt;Record&lt;string, boolean&gt;&gt;({});</span>
  const [expandedBanners, setExpandedBanners] = <span class="cstat-no" title="statement not covered" >useState&lt;Record&lt;string, boolean&gt;&gt;({});</span>
  const [expandedWeeks, setExpandedWeeks] = <span class="cstat-no" title="statement not covered" >useState&lt;Record&lt;string, boolean&gt;&gt;({});</span>
  const [expandAll, setExpandAll] = <span class="cstat-no" title="statement not covered" >useState(false);</span>
  const [expandedTotal, setExpandedTotal] = <span class="cstat-no" title="statement not covered" >useState(false);</span>
  const { data: appliedFilters } = <span class="cstat-no" title="statement not covered" >useSelectorWrap('appliedFilter_rn');</span>
  const { data: worksheetFilters } = <span class="cstat-no" title="statement not covered" >useSelectorWrap('workSheetFilterList_rn') || {};</span>
  const { data: CalendarWeek } = <span class="cstat-no" title="statement not covered" >useSelectorWrap('dataForQrtrDisplayedInTable_rn') || {};</span>
  const { data: displayDate } = <span class="cstat-no" title="statement not covered" >useSelectorWrap('displayDate_rn');</span>
  const smicData = <span class="cstat-no" title="statement not covered" >worksheetFilters?.smicData || [];</span>
  const [isLoading, setIsLoading] = <span class="cstat-no" title="statement not covered" >useState(false);</span>
  const [getAllocatrTableData] = <span class="cstat-no" title="statement not covered" >useGetAllocatrTableDataMutation();</span>
  const [weeks, setWeeks] = <span class="cstat-no" title="statement not covered" >useState&lt;Map&lt;string, WeekData[]&gt;&gt;(new Map());</span>
  const [loadingWeeks, setLoadingWeeks] = <span class="cstat-no" title="statement not covered" >useState&lt;Record&lt;string, boolean&gt;&gt;({});</span>
  const weekRefs = <span class="cstat-no" title="statement not covered" >useRef&lt;Record&lt;string, HTMLTableRowElement | null&gt;&gt;({});</span>
  const [hoveredDeptId, setHoveredDeptId] = <span class="cstat-no" title="statement not covered" >useState&lt;string | null&gt;(null);</span>
  const containerRef = <span class="cstat-no" title="statement not covered" >useRef&lt;HTMLDivElement&gt;(null);</span>
  const deptRowRefs = <span class="cstat-no" title="statement not covered" >useRef&lt;Record&lt;string, HTMLTableRowElement | null&gt;&gt;({});</span>
  const periodRowRefs = <span class="cstat-no" title="statement not covered" >useRef&lt;Record&lt;string, HTMLTableRowElement | null&gt;&gt;({});</span>
  const [stickyHoveredDeptId, setStickyHoveredDeptId] = <span class="cstat-no" title="statement not covered" >useState&lt;string | null&gt;(null);</span>
  const getDeptKey = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(d</span>ivisionId: string, bannerId: string, departmentId: string) =&gt; <span class="cstat-no" title="statement not covered" >`${divisionId}_${bannerId}_${departmentId}`;</span></span>
  const toggleAll = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >()</span> =&gt; {</span>
    const newExpandAll = <span class="cstat-no" title="statement not covered" >!expandAll;</span>
<span class="cstat-no" title="statement not covered" >    setExpandAll(newExpandAll);</span>
&nbsp;
    const newExpandedDivisions: Record&lt;string, boolean&gt; = <span class="cstat-no" title="statement not covered" >{};</span>
    const newExpandedBanners: Record&lt;string, boolean&gt; = <span class="cstat-no" title="statement not covered" >{};</span>
    const newExpandedWeeks: Record&lt;string, boolean&gt; = <span class="cstat-no" title="statement not covered" >{};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (newExpandAll) {</span>
<span class="cstat-no" title="statement not covered" >      for (const division of insightsData.divisions || []) {</span>
<span class="cstat-no" title="statement not covered" >        newExpandedDivisions[division.id] = true;</span>
<span class="cstat-no" title="statement not covered" >        for (const banner of division.banners || []) {</span>
<span class="cstat-no" title="statement not covered" >          newExpandedBanners[`${division.id}-${banner.id}`] = true;</span>
          // for (const department of banner.departments || []) {
          //   newExpandedWeeks[department.id] = true;
          // }
        }
      }
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    setExpandedDivisions(newExpandedDivisions);</span>
<span class="cstat-no" title="statement not covered" >    setExpandedBanners(newExpandedBanners);</span>
    // setExpandedWeeks(newExpandedWeeks);
<span class="cstat-no" title="statement not covered" >    setExpandedTotal(newExpandAll);</span>
  };
&nbsp;
  const toggleDivision = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(d</span>ivisionId: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    setExpandedDivisions(<span class="fstat-no" title="function not covered" >pr</span>ev =&gt; (<span class="cstat-no" title="statement not covered" >{ ...prev, [divisionId]: !prev[divisionId] })</span>);</span>
  };
&nbsp;
  const toggleBanner = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(d</span>ivisionId: string, bannerId: string) =&gt; {</span>
    const key = <span class="cstat-no" title="statement not covered" >`${divisionId}-${bannerId}`;</span>
<span class="cstat-no" title="statement not covered" >    setExpandedBanners(<span class="fstat-no" title="function not covered" >pr</span>ev =&gt; (<span class="cstat-no" title="statement not covered" >{ ...prev, [key]: !prev[key] })</span>);</span>
  };
  const toggleWeeks = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(d</span>ivisionId: string, bannerId: string, departmentId: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    setExpandedWeeks(<span class="fstat-no" title="function not covered" >pr</span>ev =&gt; (<span class="cstat-no" title="statement not covered" >{</span></span>
      ...prev,
      [getDeptKey(divisionId, bannerId, departmentId)]: !prev[getDeptKey(divisionId, bannerId, departmentId)]
    }));
  };
&nbsp;
  const toggleTotal = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >()</span> =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    setExpandedTotal(<span class="fstat-no" title="function not covered" >pr</span>ev =&gt; <span class="cstat-no" title="statement not covered" >!prev)</span>;</span>
  };
  const toggleTotalDivisions = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >()</span> =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    setExpandedDivisions(<span class="fstat-no" title="function not covered" >pr</span>ev =&gt; {</span>
      const allExpanded = <span class="cstat-no" title="statement not covered" >Object.values(prev).every(Boolean);</span>
      const newExpandedDivisions: Record&lt;string, boolean&gt; = <span class="cstat-no" title="statement not covered" >{};</span>
<span class="cstat-no" title="statement not covered" >      for (const division of insightsData.divisions || []) {</span>
<span class="cstat-no" title="statement not covered" >        newExpandedDivisions[division.id] = !allExpanded;</span>
      }
<span class="cstat-no" title="statement not covered" >      return newExpandedDivisions;</span>
    });
  }
 const getWeeks = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(d</span>ivisionId: string, bannerId: string, departmentId: string, periodNumber: any) =&gt; {</span>
  const deptKey = <span class="cstat-no" title="statement not covered" >getDeptKey(divisionId, bannerId, departmentId);</span>
<span class="cstat-no" title="statement not covered" >  if (expandedWeeks[deptKey]) {</span>
<span class="cstat-no" title="statement not covered" >    if (loadingWeeks[deptKey]) {</span>
<span class="cstat-no" title="statement not covered" >      return (</span>
        &lt;tr ref={<span class="fstat-no" title="function not covered" >el</span> =&gt; (<span class="cstat-no" title="statement not covered" >weekRefs.current[departmentId] = el)</span>}&gt;
          &lt;td colSpan={15} style={{ padding: '40px 0' }}&gt;
            &lt;div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', minHeight: 60 }}&gt;
              &lt;Spinner /&gt;
            &lt;/div&gt;
          &lt;/td&gt;
        &lt;/tr&gt;
      );
    }
&nbsp;
    const division = <span class="cstat-no" title="statement not covered" >(insightsData.divisions || []).find(<span class="fstat-no" title="function not covered" >d </span>=&gt; <span class="cstat-no" title="statement not covered" >d.id === divisionId)</span>;</span>
    const banner = <span class="cstat-no" title="statement not covered" >division?.banners?.find(<span class="fstat-no" title="function not covered" >b </span>=&gt; <span class="cstat-no" title="statement not covered" >b.id === bannerId)</span>;</span>
    const department = <span class="cstat-no" title="statement not covered" >banner?.departments?.find(<span class="fstat-no" title="function not covered" >de</span>p =&gt; <span class="cstat-no" title="statement not covered" >dep.id === departmentId)</span>;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (department?.weeks) {</span>
      const weekList = <span class="cstat-no" title="statement not covered" >department.weeks;</span>
<span class="cstat-no" title="statement not covered" >      return weekList</span>
        .filter(<span class="fstat-no" title="function not covered" >wk</span> =&gt; <span class="cstat-no" title="statement not covered" >wk.periodNumber === periodNumber)</span>
        .map(<span class="fstat-no" title="function not covered" >(w</span>eek: WeekData, idx: number) =&gt; (
<span class="cstat-no" title="statement not covered" >          &lt;AllocatrWeekRow</span>
            key={week.id}
            week={week}
            isLastFiscalWeek={idx === weekList.length - 1}
            onLastFiscalWeekHover={<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >setHoveredDeptId(departmentId)}</span>
            ref={idx === 0 ? (<span class="fstat-no" title="function not covered" >el</span> =&gt; { <span class="cstat-no" title="statement not covered" >weekRefs.current[departmentId] = el; </span>}) as React.Ref&lt;HTMLTableRowElement&gt; : undefined}
            isActualUsed={week.isActualUsed}
            isCurrentWeek={week.weekNumber === displayDate?.fiscalWeekNumber}
          /&gt;
        ));
    }
  }
};
  const getTotalWeeks = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(p</span>eriodNumber: any) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (expandedTotal) {</span>
      const weekList = <span class="cstat-no" title="statement not covered" >insightsData.weeks || [];</span>
<span class="cstat-no" title="statement not covered" >      return weekList.filter(<span class="fstat-no" title="function not covered" >wk</span> =&gt; <span class="cstat-no" title="statement not covered" >wk.periodNumber === periodNumber)</span></span>
        .map(<span class="fstat-no" title="function not covered" >(w</span>eek: WeekData, idx: number) =&gt; (
<span class="cstat-no" title="statement not covered" >          &lt;AllocatrWeekRow</span>
            key={week.id}
            week={week}
            isLastFiscalWeek={idx === weekList.length - 1}
            onLastFiscalWeekHover={<span class="fstat-no" title="function not covered" >()</span> =&gt; {}}
            isActualUsed={week.isActualUsed}
            isCurrentWeek={week.weekNumber === displayDate?.fiscalWeekNumber}
          /&gt;
        ));
    }
  }
&nbsp;
  const getDeptName = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(d</span>eptId: string, fallback: string) =&gt; {</span>
    const found = <span class="cstat-no" title="statement not covered" >smicData.find(<span class="fstat-no" title="function not covered" >(i</span>tem: any) =&gt; <span class="cstat-no" title="statement not covered" >String(item.deptId) === String(deptId))</span>;</span>
<span class="cstat-no" title="statement not covered" >    return found ? toTitleCase(found.deptName || '') : toTitleCase(fallback || '');</span>
  };
&nbsp;
  const getDivisionName = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(d</span>ivisionId: string, fallback: string) =&gt; {</span>
    const found = <span class="cstat-no" title="statement not covered" >smicData.find(<span class="fstat-no" title="function not covered" >(i</span>tem: any) =&gt; <span class="cstat-no" title="statement not covered" >String(item.divisionId) === String(divisionId))</span>;</span>
<span class="cstat-no" title="statement not covered" >    return found ? toTitleCase(found.divisionName || '') : toTitleCase(fallback || divisionId);</span>
  };
&nbsp;
  const getBannerName = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(d</span>ivisionId: string, bannerId: string, fallback: string) =&gt; {</span>
    const found = <span class="cstat-no" title="statement not covered" >smicData.find(<span class="fstat-no" title="function not covered" >(i</span>tem: any) =&gt; <span class="cstat-no" title="statement not covered" >String(item.divisionId) === String(divisionId) &amp;&amp; String(item.bannerId) === String(bannerId))</span>;</span>
<span class="cstat-no" title="statement not covered" >    return found ? toTitleCase(found.bannerName || '') : toTitleCase(fallback || bannerId);</span>
  };
  
  const tableHeader = (
<span class="cstat-no" title="statement not covered" >    &lt;thead className="text-left"&gt;</span>
      &lt;tr className="sticky-header-row"&gt;
        &lt;th rowSpan={2} className="header-cellsticky left-0 sticky-header-cell quarter-cell"&gt;&lt;/th&gt;
        &lt;th rowSpan={2} className="header-cellsticky left-0 sticky-header-cell quarter-cell qc-cell-with-icon"&gt;
          &lt;div className="quarter-wrapper"&gt;
            &lt;span&gt;
              {appliedFilters?.timeframe
                ? `Q${appliedFilters.timeframe?.num?.toString().slice(-1)} ${appliedFilters.timeframe.fiscalYear}`
                : 'Quarter'}
            &lt;/span&gt;
           &lt;div className="flex gap-6 flex-shrink-0 ml-2"&gt;
              {expandAll ? (
                &lt;ChevronsDownUp
                  size={18}
                  className="cursor-pointer"
                  onClick={toggleAll}
                /&gt;
              ) : (
                &lt;ChevronsUpDown
                  size={18}
                  className="cursor-pointer"
                  onClick={toggleAll}
                /&gt;
              )}
              &lt;Settings2 size={18} color='#000'/&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/th&gt;
        &lt;th colSpan={6} className="header-cell"&gt;Line 1 (Sales to Public)&lt;/th&gt;
        &lt;th colSpan={5} className="header-cell "&gt;Book Gross Profit %&lt;/th&gt;
        &lt;th colSpan={5} className="header-cell "&gt;Markdown %&lt;/th&gt;
        &lt;th colSpan={5} className="header-cell "&gt;Shrink %&lt;/th&gt;
        &lt;th colSpan={6} className="header-cell "&gt;Line 5 (Realized Gross Profit): Total&lt;/th&gt;
        &lt;th colSpan={3} className="header-cell "&gt;Line 6 (Supplies Packaging)&lt;/th&gt;
        &lt;th colSpan={3} className="header-cell"&gt;Line 7 (Retail Allowance)&lt;/th&gt;
        &lt;th colSpan={6} className="header-cell"&gt;Line 8 (Realized Gross Profit Before Other Revenue – Sales)&lt;/th&gt;
      &lt;/tr&gt;
      &lt;tr className="sticky-subheader-row"&gt;
        &lt;th className="subheader-cell"&gt;$ Projection&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;$ Last Year&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;$ Actual/Merch. Forecast&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;Keeper% (Includes ID)&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;$ vs LY&lt;/th&gt;
        &lt;th className={`subheader-cell ${borderClass}`}&gt;$ vs Projection&lt;/th&gt;
&nbsp;
        &lt;th className="subheader-cell"&gt;$ Projection&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;% Projection&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;$ Actual/Merch. Forecast&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;% Actual/Merch. Forecast&lt;/th&gt;
        &lt;th className={`subheader-cell ${borderClass}`}&gt;% vs Projection&lt;/th&gt;
&nbsp;
        &lt;th className="subheader-cell"&gt;$ Projection&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;% Projection&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;$ Actual/Merch. Forecast&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;% Actual/Merch. Forecast&lt;/th&gt;
        &lt;th className={`subheader-cell ${borderClass}`}&gt;% vs Projection&lt;/th&gt;
&nbsp;
        &lt;th className="subheader-cell"&gt;$ Projection&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;% Projection&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;$ Actual/Merch. Forecast&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;% Actual/Merch. Forecast&lt;/th&gt;
        &lt;th className={`subheader-cell ${borderClass}`}&gt;% vs Projection&lt;/th&gt;
&nbsp;
        &lt;th className="subheader-cell"&gt;$ Projection&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;% Projection&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;$ Actual/Merch. Forecast&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;% Actual/Merch. Forecast&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;$ vs Projection&lt;/th&gt;
        &lt;th className={`subheader-cell ${borderClass}`}&gt;% vs Projection&lt;/th&gt;
&nbsp;
        &lt;th className="subheader-cell"&gt;$ Projection&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;$ Actual/Merch. Forecast&lt;/th&gt;
        &lt;th className={`subheader-cell ${borderClass}`}&gt;$ vs Projection&lt;/th&gt;
&nbsp;
        &lt;th className="subheader-cell"&gt;$ Projection&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;$ Actual/Merch. Forecast&lt;/th&gt;
        &lt;th className={`subheader-cell ${borderClass}`}&gt;$ vs Projection&lt;/th&gt;
&nbsp;
        &lt;th className="subheader-cell"&gt;$ Projection&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;% Projection&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;$ Actual/Merch. Forecast&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;% Actual/Merch. Forecast&lt;/th&gt;
        &lt;th className="subheader-cell"&gt;$ vs Projection&lt;/th&gt;
        &lt;th className={`subheader-cell ${borderClass}`}&gt;% vs Projection&lt;/th&gt;
      &lt;/tr&gt;
    &lt;/thead&gt;
  );
&nbsp;
  const tableBody = (
<span class="cstat-no" title="statement not covered" >    &lt;tbody&gt;   </span>
      {insightsData &amp;&amp; insightsData.id === 'Total' &amp;&amp;  (
        &lt;&gt;
          &lt;AllocatrTotalRow
          quarter={ insightsData.quarter as import('../../interfaces/allocatr-insights').QuarterData}
          expanded={expandedTotal}
          onToggle={toggleTotal}
          onToggleTotalDivisions={toggleTotalDivisions}
          divisionCount={(insightsData.divisions || []).length}
        /&gt;
       
      {expandedTotal &amp;&amp; (insightsData.periods || []).map(<span class="fstat-no" title="function not covered" >pe</span>riod =&gt; (
<span class="cstat-no" title="statement not covered" >        &lt;React.Fragment key={period.id}&gt;</span>
          &lt;AllocatrPeriodRow
            period={period}
          /&gt;
          {getTotalWeeks(period.periodNumber)}
        &lt;/React.Fragment&gt;
      ))}
&nbsp;
        &lt;/&gt;
      )}
&nbsp;
      {(insightsData.divisions || []).map(<span class="fstat-no" title="function not covered" >di</span>vision =&gt; {
        const divisionName = <span class="cstat-no" title="statement not covered" >getDivisionName(division.id, division.name);</span>
<span class="cstat-no" title="statement not covered" >        return (</span>
          &lt;React.Fragment key={division.id}&gt;
            &lt;AllocatrDivisionRow
              division={{ ...division, name: divisionName }}
              expanded={!!expandedDivisions[division.id]}
              onToggle={toggleDivision}
            /&gt;
            {expandedDivisions[division.id] &amp;&amp; (
              Array.isArray(division.banners) &amp;&amp; division.banners.length &gt; 0 ? (
                &lt;&gt;
                  {/* Render all banners except '00' as usual */}
                  {division.banners.filter(<span class="fstat-no" title="function not covered" >ba</span>nner =&gt; <span class="cstat-no" title="statement not covered" >banner.id !== '00')</span>.map(<span class="fstat-no" title="function not covered" >ba</span>nner =&gt; {
                    const bannerName = <span class="cstat-no" title="statement not covered" >getBannerName(division.id, banner.id, banner.name);</span>
<span class="cstat-no" title="statement not covered" >                    return (</span>
                      &lt;React.Fragment key={`${division.id}-${banner.id}`}&gt;
                        &lt;AllocatrBannerRow
                          banner={{ ...banner, name: bannerName }}
                          expanded={!!expandedBanners[`${division.id}-${banner.id}`]}
                          onToggle={<span class="fstat-no" title="function not covered" >(b</span>annerId) =&gt; <span class="cstat-no" title="statement not covered" >toggleBanner(division.id, bannerId)}</span>
                        /&gt;
                        {expandedBanners[`${division.id}-${banner.id}`] &amp;&amp; (banner.departments || []).map(<span class="fstat-no" title="function not covered" >de</span>partment =&gt; (
<span class="cstat-no" title="statement not covered" >                          &lt;React.Fragment key={department.id}&gt;</span>
                            &lt;AllocatrDepartmentRow
                              department={{ ...department, name: getDeptName(department.id, department.name) }}
                              expanded={!!expandedWeeks[getDeptKey(division.id, banner.id, department.id)]}
                               onToggleWeeks={<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >toggleWeeks(division.id, banner.id, department.id)}</span>
                              expandedWeeks={!!expandedWeeks[getDeptKey(division.id, banner.id, department.id)]}
                            /&gt;
                            {department.periods &amp;&amp; department.periods.map(<span class="fstat-no" title="function not covered" >pe</span>riod =&gt; (
<span class="cstat-no" title="statement not covered" >                              &lt;React.Fragment key={period.id}&gt;</span>
                                &lt;AllocatrPeriodRow
                                  period={period}
                                /&gt;
                                 {getWeeks(division.id, banner.id, department.id, period.periodNumber)}
                              
                              &lt;/React.Fragment&gt;
                            ))}
                          &lt;/React.Fragment&gt;
                        ))}
                      &lt;/React.Fragment&gt;
                    );
                  })}
                  {/* If there is a banner with id '00', display its departments/periods directly under the division */}
                  {division.banners.filter(<span class="fstat-no" title="function not covered" >ba</span>nner =&gt; <span class="cstat-no" title="statement not covered" >banner.id === '00')</span>.flatMap(<span class="fstat-no" title="function not covered" >ba</span>nner =&gt; <span class="cstat-no" title="statement not covered" >banner.departments || [])</span>.map(<span class="fstat-no" title="function not covered" >de</span>partment =&gt; (
<span class="cstat-no" title="statement not covered" >                    &lt;React.Fragment key={department.id}&gt;</span>
                      &lt;AllocatrDepartmentRow
                        department={{ ...department, name: getDeptName(department.id, department.name) }}
                        expanded={!!expandedWeeks[getDeptKey(division.id, '00', department.id)]}
                         onToggleWeeks={<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >toggleWeeks(division.id, '00', department.id)}</span>
                        expandedWeeks={!!expandedWeeks[getDeptKey(division.id, '00', department.id)]}
                      /&gt;
                      {department.periods &amp;&amp; department.periods.map(<span class="fstat-no" title="function not covered" >pe</span>riod =&gt; (
<span class="cstat-no" title="statement not covered" >                        &lt;React.Fragment key={period.id}&gt;</span>
                          &lt;AllocatrPeriodRow
                            period={period}
                          /&gt;
                           {getWeeks(division.id, '00', department.id, period.periodNumber)}  
                          
                        &lt;/React.Fragment&gt;
                      ))}
                    &lt;/React.Fragment&gt;
                  ))}
                &lt;/&gt;
              ) : null
            )}
          &lt;/React.Fragment&gt;
        );
      })}
    &lt;/tbody&gt;
  );
&nbsp;
<span class="cstat-no" title="statement not covered" >  return (</span>
    &lt;div ref={containerRef} style={{ maxHeight: 'calc(100vh - 280px)', overflowY: 'auto', overflowX: 'auto', width: '100%' }}&gt;
      {isLoading ? &lt;Spinner /&gt; :
        &lt;table className="text-sm/[0px] allocatr-insights-table"&gt;
          {tableHeader}
          {tableBody}
        &lt;/table&gt;
      }
    &lt;/div&gt;
  );
};
&nbsp;
export default AllocatrInsightsTable;
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-17T06:34:15.200Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    