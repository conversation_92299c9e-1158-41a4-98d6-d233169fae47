
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/DashboardDownloadExcel/DashboardDownloadExcel.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">src/components/DashboardDownloadExcel</a> DashboardDownloadExcel.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/173</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/217</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/31</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/162</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';
import { toTitleCase } from '@ui/utils';
import { getParentHeaderRow, COMMON_HEADERS, VS_PROJECTION_HEADERS, VS_PROJECTION_DOLLAR_HEADERS, mapRow } from './DashboardDownloadExcelHelper';
import { applyPrintSettings } from './DashboardDownloadExcelPrint';
&nbsp;
export const formatCurrency = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(v</span>alue: any) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  if (value === null || value === undefined || value === '') <span class="cstat-no" title="statement not covered" >return '';</span></span>
  const num = <span class="cstat-no" title="statement not covered" >Number(value);</span>
<span class="cstat-no" title="statement not covered" >  return isNaN(num) ? value : `$${num.toLocaleString('en-US', { maximumFractionDigits: 0 })}`;</span>
};
&nbsp;
export const getDeptName = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(s</span>micData: any[], deptId: string, fallback: string) =&gt; {</span>
  const found = <span class="cstat-no" title="statement not covered" >smicData.find(<span class="fstat-no" title="function not covered" >(i</span>tem: any) =&gt; <span class="cstat-no" title="statement not covered" >String(item.deptId).trim() === String(deptId).trim())</span>;</span>
<span class="cstat-no" title="statement not covered" >  return toTitleCase(found?.deptName || fallback || '');</span>
};
&nbsp;
export const getDivisionName = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(s</span>micData: any[], divisionId: string, fallback: string) =&gt; {</span>
  const found = <span class="cstat-no" title="statement not covered" >smicData.find(<span class="fstat-no" title="function not covered" >(i</span>tem: any) =&gt; <span class="cstat-no" title="statement not covered" >String(item.divisionId) === String(divisionId))</span>;</span>
<span class="cstat-no" title="statement not covered" >  return found ? toTitleCase(found.divisionName || '') : toTitleCase(fallback || divisionId);</span>
};
&nbsp;
export const getBannerName = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(s</span>micData: any[], divisionId: string, bannerId: string, fallback: string) =&gt; {</span>
  const found = <span class="cstat-no" title="statement not covered" >smicData.find(<span class="fstat-no" title="function not covered" >(i</span>tem: any) =&gt;</span>
<span class="cstat-no" title="statement not covered" >    String(item.divisionId) === String(divisionId) &amp;&amp;</span>
    String(item.bannerId) === String(bannerId)
  );
<span class="cstat-no" title="statement not covered" >  return found ? toTitleCase(found.bannerName || '') : toTitleCase(fallback || bannerId);</span>
};
&nbsp;
const addDepartmentRows = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(r</span>ows: any[], dept: any, smicData: any[], useWeekId: boolean = <span class="branch-0 cbranch-no" title="branch not covered" >false)</span> =&gt; {</span>
  const deptName = <span class="cstat-no" title="statement not covered" >getDeptName(smicData, dept.id, dept?.name ?? '');</span>
  const isTotal = <span class="cstat-no" title="statement not covered" >dept.id === 'Total';</span>
  const baseRow = <span class="cstat-no" title="statement not covered" >{ departmentName: isTotal ? (dept.name || 'Total') : `${dept.id} - ${deptName}` };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (dept.quarter) {</span>
<span class="cstat-no" title="statement not covered" >    rows.push(mapRow(baseRow, dept.quarter, formatCurrency, 'Quarter'));</span>
  }
&nbsp;
  // Add periods and weeks for departments
<span class="cstat-no" title="statement not covered" >  if (dept.periods &amp;&amp; Array.isArray(dept.periods)) {</span>
<span class="cstat-no" title="statement not covered" >    dept.periods.forEach(<span class="fstat-no" title="function not covered" >(p</span>eriod: any) =&gt; {</span>
      const periodNum = <span class="cstat-no" title="statement not covered" >period.periodNumber ?? period.periodNbr ?? '';</span>
<span class="cstat-no" title="statement not covered" >      rows.push(</span>
        mapRow(
          { ...baseRow, departmentName: periodNum ? `Period ${parseInt(String(periodNum).slice(-2), 10)}` : 'Period' },
          period,
          formatCurrency,
          'Period',
          periodNum
        )
      );
    });
  }
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (dept.weeks &amp;&amp; Array.isArray(dept.weeks)) {</span>
    const sortedWeeks = <span class="cstat-no" title="statement not covered" >dept.weeks.slice().sort(<span class="fstat-no" title="function not covered" >(a</span>: any, b: any) =&gt; {</span>
      const aNum = <span class="cstat-no" title="statement not covered" >typeof a.weekNumber === 'number' ? a.weekNumber : parseInt((a.weekNumber || '').slice(-2), 10);</span>
      const bNum = <span class="cstat-no" title="statement not covered" >typeof b.weekNumber === 'number' ? b.weekNumber : parseInt((b.weekNumber || '').slice(-2), 10);</span>
<span class="cstat-no" title="statement not covered" >      return aNum - bNum;</span>
    });
<span class="cstat-no" title="statement not covered" >    sortedWeeks.forEach(<span class="fstat-no" title="function not covered" >(w</span>eek: any) =&gt; {</span>
      let weekNum = <span class="cstat-no" title="statement not covered" >'--';</span>
<span class="cstat-no" title="statement not covered" >      if (useWeekId &amp;&amp; typeof week.id === 'string' &amp;&amp; week.id.startsWith('Week-')) {</span>
<span class="cstat-no" title="statement not covered" >        weekNum = String(parseInt(week.id.slice(-2), 10));</span>
      } else <span class="cstat-no" title="statement not covered" >if (!useWeekId &amp;&amp; typeof week.weekNumber === 'number') {</span>
<span class="cstat-no" title="statement not covered" >        weekNum = String(week.weekNumber % 100);</span>
      } else <span class="cstat-no" title="statement not covered" >if (!useWeekId &amp;&amp; typeof week.weekNumber === 'string') {</span>
<span class="cstat-no" title="statement not covered" >        weekNum = String(parseInt(week.weekNumber.slice(-2), 10));</span>
      }
<span class="cstat-no" title="statement not covered" >      rows.push(</span>
        mapRow(
          { ...baseRow, departmentName: `Week ${weekNum} (fiscal wk ${weekNum})` },
          week,
          formatCurrency,
          'Week',
          '',
          String(weekNum)
        )
      );
    });
  }
};
&nbsp;
const addRows = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(r</span>ows: any[], data: any, smicData: any[], useWeekId: boolean = <span class="branch-0 cbranch-no" title="branch not covered" >false)</span> =&gt; {</span>
  // Handle null/undefined data
<span class="cstat-no" title="statement not covered" >  if (!data) <span class="cstat-no" title="statement not covered" >return;</span></span>
&nbsp;
  // Handle the new structure with divisions and banners
<span class="cstat-no" title="statement not covered" >  if (data.divisions &amp;&amp; Array.isArray(data.divisions)) {</span>
    // First, add the Total row data if it exists at the root level
<span class="cstat-no" title="statement not covered" >    if (data.id === 'Total' || data.name === 'Total' || data.quarter || data.periods || data.weeks) {</span>
      const divisionsCount = <span class="cstat-no" title="statement not covered" >data.divisions ? data.divisions.length : 0;</span>
      const totalData = <span class="cstat-no" title="statement not covered" >{</span>
        id: 'Total',
        name: `Total of ${divisionsCount} Division${divisionsCount !== 1 ? 's' : ''}`,
        quarter: data.quarter,
        periods: data.periods || [],
        weeks: data.weeks || []
      };
<span class="cstat-no" title="statement not covered" >      addDepartmentRows(rows, totalData, smicData, useWeekId);</span>
    }
&nbsp;
    // Then, process divisions and banners
<span class="cstat-no" title="statement not covered" >    data.divisions.forEach(<span class="fstat-no" title="function not covered" >(d</span>ivision: any) =&gt; {</span>
      const divisionName = <span class="cstat-no" title="statement not covered" >getDivisionName(smicData, division.id, division.name);</span>
      const divisionBaseRow = <span class="cstat-no" title="statement not covered" >{ departmentName: `${division.id} - ${divisionName}` };</span>
&nbsp;
      // Add division quarter row
<span class="cstat-no" title="statement not covered" >      if (division.quarter) {</span>
<span class="cstat-no" title="statement not covered" >        rows.push(mapRow(divisionBaseRow, division.quarter, formatCurrency, 'Quarter'));</span>
      }
&nbsp;
      // Process division-level periods and weeks if they exist
<span class="cstat-no" title="statement not covered" >      if (division.periods &amp;&amp; Array.isArray(division.periods)) {</span>
<span class="cstat-no" title="statement not covered" >        division.periods.forEach(<span class="fstat-no" title="function not covered" >(p</span>eriod: any) =&gt; {</span>
          const periodNum = <span class="cstat-no" title="statement not covered" >period.periodNumber ?? period.periodNbr ?? '';</span>
<span class="cstat-no" title="statement not covered" >          rows.push(</span>
            mapRow(
              { departmentName: periodNum ? `Period ${parseInt(String(periodNum).slice(-2), 10)}` : 'Period' },
              period,
              formatCurrency,
              'Period',
              periodNum
            )
          );
        });
      }
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (division.weeks &amp;&amp; Array.isArray(division.weeks)) {</span>
        const sortedWeeks = <span class="cstat-no" title="statement not covered" >division.weeks.slice().sort(<span class="fstat-no" title="function not covered" >(a</span>: any, b: any) =&gt; {</span>
          const aNum = <span class="cstat-no" title="statement not covered" >typeof a.weekNumber === 'number' ? a.weekNumber : parseInt((a.weekNumber || '').slice(-2), 10);</span>
          const bNum = <span class="cstat-no" title="statement not covered" >typeof b.weekNumber === 'number' ? b.weekNumber : parseInt((b.weekNumber || '').slice(-2), 10);</span>
<span class="cstat-no" title="statement not covered" >          return aNum - bNum;</span>
        });
<span class="cstat-no" title="statement not covered" >        sortedWeeks.forEach(<span class="fstat-no" title="function not covered" >(w</span>eek: any) =&gt; {</span>
          let weekNum = <span class="cstat-no" title="statement not covered" >'--';</span>
<span class="cstat-no" title="statement not covered" >          if (useWeekId &amp;&amp; typeof week.id === 'string' &amp;&amp; week.id.startsWith('Week-')) {</span>
<span class="cstat-no" title="statement not covered" >            weekNum = String(parseInt(week.id.slice(-2), 10));</span>
          } else <span class="cstat-no" title="statement not covered" >if (!useWeekId &amp;&amp; typeof week.weekNumber === 'number') {</span>
<span class="cstat-no" title="statement not covered" >            weekNum = String(week.weekNumber % 100);</span>
          } else <span class="cstat-no" title="statement not covered" >if (!useWeekId &amp;&amp; typeof week.weekNumber === 'string') {</span>
<span class="cstat-no" title="statement not covered" >            weekNum = String(parseInt(week.weekNumber.slice(-2), 10));</span>
          }
<span class="cstat-no" title="statement not covered" >          rows.push(</span>
            mapRow(
              { departmentName: `Week ${weekNum} (fiscal wk ${weekNum})` },
              week,
              formatCurrency,
              'Week',
              '',
              String(weekNum)
            )
          );
        });
      }
&nbsp;
      // Process banners within division
<span class="cstat-no" title="statement not covered" >      if (division.banners &amp;&amp; Array.isArray(division.banners) &amp;&amp; division.banners.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >        division.banners.forEach(<span class="fstat-no" title="function not covered" >(b</span>anner: any) =&gt; {</span>
          // Skip "Default" banners entirely - we don't want them in the Excel export
          const isDefaultBanner = <span class="cstat-no" title="statement not covered" >banner.id === '00' ||</span>
                                 banner.id === 'default' ||
                                 (banner.name &amp;&amp; banner.name.toLowerCase().includes('default'));
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (isDefaultBanner) {</span>
            // For default banners, process their departments directly at division level
            // without showing the banner row itself
<span class="cstat-no" title="statement not covered" >            if (banner.departments &amp;&amp; Array.isArray(banner.departments)) {</span>
<span class="cstat-no" title="statement not covered" >              banner.departments.forEach(<span class="fstat-no" title="function not covered" >(d</span>ept: any) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                addDepartmentRows(rows, dept, smicData, useWeekId);</span>
              });
            }
<span class="cstat-no" title="statement not covered" >            return; </span>// Skip the banner row but process its departments
          }
&nbsp;
          // Check if non-default banner has valid data before displaying
          const hasValidBannerData = <span class="cstat-no" title="statement not covered" >(banner.quarter &amp;&amp; Object.keys(banner.quarter).length &gt; 0 &amp;&amp;</span>
                                     Object.values(banner.quarter).some(<span class="fstat-no" title="function not covered" >va</span>l =&gt; <span class="cstat-no" title="statement not covered" >val !== null &amp;&amp; val !== undefined &amp;&amp; val !== '')</span>) ||
                                   (banner.departments &amp;&amp; banner.departments.length &gt; 0) ||
                                   (banner.periods &amp;&amp; banner.periods.length &gt; 0) ||
                                   (banner.weeks &amp;&amp; banner.weeks.length &gt; 0);
&nbsp;
          // For non-default banners, process normally if they have valid data
<span class="cstat-no" title="statement not covered" >          if (hasValidBannerData) {</span>
            const bannerName = <span class="cstat-no" title="statement not covered" >getBannerName(smicData, division.id, banner.id, banner.name);</span>
            const bannerBaseRow = <span class="cstat-no" title="statement not covered" >{ departmentName: `  ${bannerName}` };</span>
&nbsp;
            // Add banner quarter row
<span class="cstat-no" title="statement not covered" >            if (banner.quarter) {</span>
<span class="cstat-no" title="statement not covered" >              rows.push(mapRow(bannerBaseRow, banner.quarter, formatCurrency, 'Quarter'));</span>
            }
&nbsp;
            // Process departments within banner
<span class="cstat-no" title="statement not covered" >            if (banner.departments &amp;&amp; Array.isArray(banner.departments)) {</span>
<span class="cstat-no" title="statement not covered" >              banner.departments.forEach(<span class="fstat-no" title="function not covered" >(d</span>ept: any) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                addDepartmentRows(rows, dept, smicData, useWeekId);</span>
              });
            }
          }
        });
      }
    });
  } else {
    // Fallback to old structure for backward compatibility
<span class="cstat-no" title="statement not covered" >    addDepartmentRows(rows, data, smicData, useWeekId);</span>
  }
};
&nbsp;
export const styleWorksheet = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(w</span>orksheet: any) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  worksheet.getRow(2).eachCell(<span class="fstat-no" title="function not covered" >(c</span>ell: any, colNumber: any) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (colNumber !== 1) {</span>
<span class="cstat-no" title="statement not covered" >      worksheet.getColumn(colNumber).width = Math.max(String(cell.value ?? '').length + 1, 16);</span>
    }
  });
  let maxA = <span class="cstat-no" title="statement not covered" >0;</span>
<span class="cstat-no" title="statement not covered" >  worksheet.eachRow(<span class="fstat-no" title="function not covered" >(r</span>ow: any, rowNumber: any) =&gt; {</span>
    const cellValue = <span class="cstat-no" title="statement not covered" >String(row.getCell(1).value ?? '');</span>
<span class="cstat-no" title="statement not covered" >    if (cellValue.length &gt; maxA) <span class="cstat-no" title="statement not covered" >maxA = cellValue.length;</span></span>
  });
<span class="cstat-no" title="statement not covered" >  worksheet.getColumn(1).width = Math.max(maxA + 1, 16);</span>
<span class="cstat-no" title="statement not covered" >  worksheet.getRow(1).height = 20;</span>
<span class="cstat-no" title="statement not covered" >  worksheet.getRow(2).height = 20;</span>
<span class="cstat-no" title="statement not covered" >  worksheet.getRow(1).font = { bold: true };</span>
<span class="cstat-no" title="statement not covered" >  worksheet.getRow(2).font = { bold: true };</span>
<span class="cstat-no" title="statement not covered" >  worksheet.getRow(1).alignment = { horizontal: 'center', vertical: 'middle' };</span>
<span class="cstat-no" title="statement not covered" >  worksheet.getRow(2).alignment = { horizontal: 'center', vertical: 'middle' };</span>
};
&nbsp;
export const styleVsProjection = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(w</span>orksheet: any) =&gt; {</span>
  const vsProjectionColIndices: number[] = <span class="cstat-no" title="statement not covered" >[];</span>
<span class="cstat-no" title="statement not covered" >  worksheet.getRow(2).eachCell(<span class="fstat-no" title="function not covered" >(c</span>ell: any, colNumber: any) =&gt; {</span>
    const header = <span class="cstat-no" title="statement not covered" >String(cell.value ?? '').trim();</span>
<span class="cstat-no" title="statement not covered" >    if (VS_PROJECTION_HEADERS.includes(header)) {</span>
<span class="cstat-no" title="statement not covered" >      vsProjectionColIndices.push(colNumber);</span>
    }
  });
<span class="cstat-no" title="statement not covered" >  worksheet.eachRow(<span class="fstat-no" title="function not covered" >(r</span>ow: any, rowNumber: any) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (rowNumber &gt;= 3) {</span>
<span class="cstat-no" title="statement not covered" >      vsProjectionColIndices.forEach(<span class="fstat-no" title="function not covered" >co</span>lIdx =&gt; {</span>
        const cell = <span class="cstat-no" title="statement not covered" >row.getCell(colIdx);</span>
        let raw = <span class="cstat-no" title="statement not covered" >typeof cell.value === 'string' ? cell.value.replace(/[\$, %\(\)]/g, '').trim() : cell.value;</span>
        const num = <span class="cstat-no" title="statement not covered" >Number(raw);</span>
<span class="cstat-no" title="statement not covered" >        if (!isNaN(num) &amp;&amp; raw !== '') {</span>
<span class="cstat-no" title="statement not covered" >          cell.font = { ...cell.font, color: { argb: num &lt; 0 ? 'FFFF0000' : 'FF008000' } };</span>
          const header = <span class="cstat-no" title="statement not covered" >worksheet.getRow(2).getCell(colIdx).value;</span>
<span class="cstat-no" title="statement not covered" >          if (VS_PROJECTION_DOLLAR_HEADERS.includes(String(header).trim())) {</span>
<span class="cstat-no" title="statement not covered" >            if (num &lt; 0) {</span>
<span class="cstat-no" title="statement not covered" >              cell.value = `($${Math.abs(num).toLocaleString('en-US', { maximumFractionDigits: 0 })})`;</span>
            } else {
<span class="cstat-no" title="statement not covered" >              cell.value = `$${num.toLocaleString('en-US', { maximumFractionDigits: 0 })}`;</span>
            }
          }
        }
      });
    }
  });
};
&nbsp;
export const handleDownloadExcel = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >as</span>ync (</span>
  dashboardData?: any,
  smicData: any[] = <span class="branch-0 cbranch-no" title="branch not covered" >[],</span>
  appliedFilters?: any,
  fileName: string = <span class="branch-0 cbranch-no" title="branch not covered" >'Dashboard Excel Download.xlsx'</span>
) =&gt; {
  // If no data provided, use mock data for testing
<span class="cstat-no" title="statement not covered" >  if (!dashboardData) {</span>
    const date = <span class="cstat-no" title="statement not covered" >new Date().toLocaleDateString('en-CA');</span>
<span class="cstat-no" title="statement not covered" >    fileName = `Allocatr Insights Performance and Variance Mock Excel Download-${date}.xlsx`;</span>
<span class="cstat-no" title="statement not covered" >    dashboardData = [{ id: 'mock', name: 'Mock Data', quarter: { line1Projection: 1000 } }];</span>
  }
&nbsp;
  // Handle both array and object data structures
  const dataArray = <span class="cstat-no" title="statement not covered" >Array.isArray(dashboardData) ? dashboardData : [dashboardData];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (!dataArray?.length) <span class="cstat-no" title="statement not covered" >return alert('No dashboard data to export!');</span></span>
&nbsp;
  let quarterNumber = <span class="cstat-no" title="statement not covered" >appliedFilters?.timeframe?.quarter;</span>
<span class="cstat-no" title="statement not covered" >  if (!quarterNumber &amp;&amp; dataArray.length) {</span>
<span class="cstat-no" title="statement not covered" >    quarterNumber = dataArray[0]?.quarter?.quarterNumber || dataArray[0]?.quarterNumber || '';</span>
<span class="cstat-no" title="statement not covered" >    if (typeof quarterNumber === 'number' || typeof quarterNumber === 'string') {</span>
      const qStr = <span class="cstat-no" title="statement not covered" >String(quarterNumber);</span>
<span class="cstat-no" title="statement not covered" >      if (qStr.length === 6) <span class="cstat-no" title="statement not covered" >quarterNumber = Number(qStr.slice(4, 6));</span></span>
    }
  }
  const fiscalYear = <span class="cstat-no" title="statement not covered" >appliedFilters?.timeframe?.fiscalYear || '';</span>
  const quarterDisplay = <span class="cstat-no" title="statement not covered" >`Q${quarterNumber} ${fiscalYear}`;</span>
  const parentHeaderRow = <span class="cstat-no" title="statement not covered" >getParentHeaderRow(quarterDisplay);</span>
  const isVariance = <span class="cstat-no" title="statement not covered" >fileName.toLowerCase().includes('variance');</span>
  const rows: any[] = <span class="cstat-no" title="statement not covered" >[];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  dataArray.forEach(<span class="fstat-no" title="function not covered" >da</span>ta =&gt; <span class="cstat-no" title="statement not covered" >addRows(rows, data, smicData, isVariance))</span>;</span>
&nbsp;
  const workbook = <span class="cstat-no" title="statement not covered" >new ExcelJS.Workbook();</span>
  const worksheet = <span class="cstat-no" title="statement not covered" >workbook.addWorksheet('Dashboard');</span>
<span class="cstat-no" title="statement not covered" >  worksheet.addRow(parentHeaderRow);</span>
<span class="cstat-no" title="statement not covered" >  worksheet.addRow(COMMON_HEADERS);</span>
<span class="cstat-no" title="statement not covered" >  rows.forEach(<span class="fstat-no" title="function not covered" >ro</span>w =&gt; <span class="cstat-no" title="statement not covered" >worksheet.addRow(Object.values(row)))</span>;</span>
&nbsp;
  const mergeRanges = <span class="cstat-no" title="statement not covered" >['A1:A2', 'B1:G1', 'H1:L1', 'M1:Q1', 'R1:V1', 'W1:AB1', 'AC1:AE1', 'AF1:AH1', 'AI1:AN1'];</span>
<span class="cstat-no" title="statement not covered" >  mergeRanges.forEach(<span class="fstat-no" title="function not covered" >ra</span>nge =&gt; <span class="cstat-no" title="statement not covered" >worksheet.mergeCells(range))</span>;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  styleWorksheet(worksheet);</span>
<span class="cstat-no" title="statement not covered" >  styleVsProjection(worksheet);</span>
<span class="cstat-no" title="statement not covered" >  applyPrintSettings(worksheet);</span>
&nbsp;
  let ySplit = <span class="cstat-no" title="statement not covered" >2;</span>
  const totalRows = <span class="cstat-no" title="statement not covered" >worksheet.rowCount;</span>
<span class="cstat-no" title="statement not covered" >  for (let i = <span class="cstat-no" title="statement not covered" >3;</span> i &lt;= totalRows; i++) {</span>
    const cellValue = <span class="cstat-no" title="statement not covered" >String(worksheet.getRow(i).getCell(1).value ?? '');</span>
<span class="cstat-no" title="statement not covered" >    if (/^\d+ - /.test(cellValue.trim()) &amp;&amp; i !== 3) {</span>
<span class="cstat-no" title="statement not covered" >      ySplit = i - 1;</span>
<span class="cstat-no" title="statement not covered" >      break;</span>
    }
<span class="cstat-no" title="statement not covered" >    if (i === totalRows) {</span>
<span class="cstat-no" title="statement not covered" >      ySplit = totalRows;</span>
    }
  }
<span class="cstat-no" title="statement not covered" >  worksheet.views = [{ state: 'frozen', ySplit, xSplit: 1 }];</span>
&nbsp;
  const styledBuffer = <span class="cstat-no" title="statement not covered" >await workbook.xlsx.writeBuffer();</span>
<span class="cstat-no" title="statement not covered" >  saveAs(new Blob([styledBuffer]), fileName);</span>
};</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-17T06:34:15.200Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    