
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/features/worksheetFilter/components/roles</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../index.html">All files</a> src/features/worksheetFilter/components/roles</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">58.11% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>68/117</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">44.77% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>30/67</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">51.72% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>15/29</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">59.63% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>65/109</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="asmRoleUsersList.tsx"><a href="asmRoleUsersList.tsx.html">asmRoleUsersList.tsx</a></td>
	<td data-value="81.81" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 81%"></div><div class="cover-empty" style="width: 19%"></div></div>
	</td>
	<td data-value="81.81" class="pct high">81.81%</td>
	<td data-value="22" class="abs high">18/22</td>
	<td data-value="57.89" class="pct medium">57.89%</td>
	<td data-value="19" class="abs medium">11/19</td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="4" class="abs low">1/4</td>
	<td data-value="90" class="pct high">90%</td>
	<td data-value="20" class="abs high">18/20</td>
	</tr>

<tr>
	<td class="file high" data-value="clearSelection.tsx"><a href="clearSelection.tsx.html">clearSelection.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	</tr>

<tr>
	<td class="file high" data-value="rolesFilter.slice.ts"><a href="rolesFilter.slice.ts.html">rolesFilter.slice.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="6" class="abs high">6/6</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="6" class="abs high">6/6</td>
	</tr>

<tr>
	<td class="file low" data-value="rolesUtils.ts"><a href="rolesUtils.ts.html">rolesUtils.ts</a></td>
	<td data-value="40.54" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 40%"></div><div class="cover-empty" style="width: 60%"></div></div>
	</td>
	<td data-value="40.54" class="pct low">40.54%</td>
	<td data-value="37" class="abs low">15/37</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="20" class="abs low">8/20</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="12" class="abs medium">6/12</td>
	<td data-value="44.11" class="pct low">44.11%</td>
	<td data-value="34" class="abs low">15/34</td>
	</tr>

<tr>
	<td class="file medium" data-value="smRoleUsersList.tsx"><a href="smRoleUsersList.tsx.html">smRoleUsersList.tsx</a></td>
	<td data-value="52.08" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 52%"></div><div class="cover-empty" style="width: 48%"></div></div>
	</td>
	<td data-value="52.08" class="pct medium">52.08%</td>
	<td data-value="48" class="abs medium">25/48</td>
	<td data-value="34.61" class="pct low">34.61%</td>
	<td data-value="26" class="abs low">9/26</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="10" class="abs medium">5/10</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="46" class="abs medium">23/46</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-17T06:07:00.681Z
            </div>
        <script src="../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../sorter.js"></script>
        <script src="../../../../../block-navigation.js"></script>
    </body>
</html>
    