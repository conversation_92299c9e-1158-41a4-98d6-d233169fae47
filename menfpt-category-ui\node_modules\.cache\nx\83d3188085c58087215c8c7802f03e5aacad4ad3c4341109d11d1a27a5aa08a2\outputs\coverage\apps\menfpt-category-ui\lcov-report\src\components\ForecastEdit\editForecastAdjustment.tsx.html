
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/ForecastEdit/editForecastAdjustment.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">src/components/ForecastEdit</a> editForecastAdjustment.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/638</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/635</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/100</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/617</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a>
<a name='L506'></a><a href='#L506'>506</a>
<a name='L507'></a><a href='#L507'>507</a>
<a name='L508'></a><a href='#L508'>508</a>
<a name='L509'></a><a href='#L509'>509</a>
<a name='L510'></a><a href='#L510'>510</a>
<a name='L511'></a><a href='#L511'>511</a>
<a name='L512'></a><a href='#L512'>512</a>
<a name='L513'></a><a href='#L513'>513</a>
<a name='L514'></a><a href='#L514'>514</a>
<a name='L515'></a><a href='#L515'>515</a>
<a name='L516'></a><a href='#L516'>516</a>
<a name='L517'></a><a href='#L517'>517</a>
<a name='L518'></a><a href='#L518'>518</a>
<a name='L519'></a><a href='#L519'>519</a>
<a name='L520'></a><a href='#L520'>520</a>
<a name='L521'></a><a href='#L521'>521</a>
<a name='L522'></a><a href='#L522'>522</a>
<a name='L523'></a><a href='#L523'>523</a>
<a name='L524'></a><a href='#L524'>524</a>
<a name='L525'></a><a href='#L525'>525</a>
<a name='L526'></a><a href='#L526'>526</a>
<a name='L527'></a><a href='#L527'>527</a>
<a name='L528'></a><a href='#L528'>528</a>
<a name='L529'></a><a href='#L529'>529</a>
<a name='L530'></a><a href='#L530'>530</a>
<a name='L531'></a><a href='#L531'>531</a>
<a name='L532'></a><a href='#L532'>532</a>
<a name='L533'></a><a href='#L533'>533</a>
<a name='L534'></a><a href='#L534'>534</a>
<a name='L535'></a><a href='#L535'>535</a>
<a name='L536'></a><a href='#L536'>536</a>
<a name='L537'></a><a href='#L537'>537</a>
<a name='L538'></a><a href='#L538'>538</a>
<a name='L539'></a><a href='#L539'>539</a>
<a name='L540'></a><a href='#L540'>540</a>
<a name='L541'></a><a href='#L541'>541</a>
<a name='L542'></a><a href='#L542'>542</a>
<a name='L543'></a><a href='#L543'>543</a>
<a name='L544'></a><a href='#L544'>544</a>
<a name='L545'></a><a href='#L545'>545</a>
<a name='L546'></a><a href='#L546'>546</a>
<a name='L547'></a><a href='#L547'>547</a>
<a name='L548'></a><a href='#L548'>548</a>
<a name='L549'></a><a href='#L549'>549</a>
<a name='L550'></a><a href='#L550'>550</a>
<a name='L551'></a><a href='#L551'>551</a>
<a name='L552'></a><a href='#L552'>552</a>
<a name='L553'></a><a href='#L553'>553</a>
<a name='L554'></a><a href='#L554'>554</a>
<a name='L555'></a><a href='#L555'>555</a>
<a name='L556'></a><a href='#L556'>556</a>
<a name='L557'></a><a href='#L557'>557</a>
<a name='L558'></a><a href='#L558'>558</a>
<a name='L559'></a><a href='#L559'>559</a>
<a name='L560'></a><a href='#L560'>560</a>
<a name='L561'></a><a href='#L561'>561</a>
<a name='L562'></a><a href='#L562'>562</a>
<a name='L563'></a><a href='#L563'>563</a>
<a name='L564'></a><a href='#L564'>564</a>
<a name='L565'></a><a href='#L565'>565</a>
<a name='L566'></a><a href='#L566'>566</a>
<a name='L567'></a><a href='#L567'>567</a>
<a name='L568'></a><a href='#L568'>568</a>
<a name='L569'></a><a href='#L569'>569</a>
<a name='L570'></a><a href='#L570'>570</a>
<a name='L571'></a><a href='#L571'>571</a>
<a name='L572'></a><a href='#L572'>572</a>
<a name='L573'></a><a href='#L573'>573</a>
<a name='L574'></a><a href='#L574'>574</a>
<a name='L575'></a><a href='#L575'>575</a>
<a name='L576'></a><a href='#L576'>576</a>
<a name='L577'></a><a href='#L577'>577</a>
<a name='L578'></a><a href='#L578'>578</a>
<a name='L579'></a><a href='#L579'>579</a>
<a name='L580'></a><a href='#L580'>580</a>
<a name='L581'></a><a href='#L581'>581</a>
<a name='L582'></a><a href='#L582'>582</a>
<a name='L583'></a><a href='#L583'>583</a>
<a name='L584'></a><a href='#L584'>584</a>
<a name='L585'></a><a href='#L585'>585</a>
<a name='L586'></a><a href='#L586'>586</a>
<a name='L587'></a><a href='#L587'>587</a>
<a name='L588'></a><a href='#L588'>588</a>
<a name='L589'></a><a href='#L589'>589</a>
<a name='L590'></a><a href='#L590'>590</a>
<a name='L591'></a><a href='#L591'>591</a>
<a name='L592'></a><a href='#L592'>592</a>
<a name='L593'></a><a href='#L593'>593</a>
<a name='L594'></a><a href='#L594'>594</a>
<a name='L595'></a><a href='#L595'>595</a>
<a name='L596'></a><a href='#L596'>596</a>
<a name='L597'></a><a href='#L597'>597</a>
<a name='L598'></a><a href='#L598'>598</a>
<a name='L599'></a><a href='#L599'>599</a>
<a name='L600'></a><a href='#L600'>600</a>
<a name='L601'></a><a href='#L601'>601</a>
<a name='L602'></a><a href='#L602'>602</a>
<a name='L603'></a><a href='#L603'>603</a>
<a name='L604'></a><a href='#L604'>604</a>
<a name='L605'></a><a href='#L605'>605</a>
<a name='L606'></a><a href='#L606'>606</a>
<a name='L607'></a><a href='#L607'>607</a>
<a name='L608'></a><a href='#L608'>608</a>
<a name='L609'></a><a href='#L609'>609</a>
<a name='L610'></a><a href='#L610'>610</a>
<a name='L611'></a><a href='#L611'>611</a>
<a name='L612'></a><a href='#L612'>612</a>
<a name='L613'></a><a href='#L613'>613</a>
<a name='L614'></a><a href='#L614'>614</a>
<a name='L615'></a><a href='#L615'>615</a>
<a name='L616'></a><a href='#L616'>616</a>
<a name='L617'></a><a href='#L617'>617</a>
<a name='L618'></a><a href='#L618'>618</a>
<a name='L619'></a><a href='#L619'>619</a>
<a name='L620'></a><a href='#L620'>620</a>
<a name='L621'></a><a href='#L621'>621</a>
<a name='L622'></a><a href='#L622'>622</a>
<a name='L623'></a><a href='#L623'>623</a>
<a name='L624'></a><a href='#L624'>624</a>
<a name='L625'></a><a href='#L625'>625</a>
<a name='L626'></a><a href='#L626'>626</a>
<a name='L627'></a><a href='#L627'>627</a>
<a name='L628'></a><a href='#L628'>628</a>
<a name='L629'></a><a href='#L629'>629</a>
<a name='L630'></a><a href='#L630'>630</a>
<a name='L631'></a><a href='#L631'>631</a>
<a name='L632'></a><a href='#L632'>632</a>
<a name='L633'></a><a href='#L633'>633</a>
<a name='L634'></a><a href='#L634'>634</a>
<a name='L635'></a><a href='#L635'>635</a>
<a name='L636'></a><a href='#L636'>636</a>
<a name='L637'></a><a href='#L637'>637</a>
<a name='L638'></a><a href='#L638'>638</a>
<a name='L639'></a><a href='#L639'>639</a>
<a name='L640'></a><a href='#L640'>640</a>
<a name='L641'></a><a href='#L641'>641</a>
<a name='L642'></a><a href='#L642'>642</a>
<a name='L643'></a><a href='#L643'>643</a>
<a name='L644'></a><a href='#L644'>644</a>
<a name='L645'></a><a href='#L645'>645</a>
<a name='L646'></a><a href='#L646'>646</a>
<a name='L647'></a><a href='#L647'>647</a>
<a name='L648'></a><a href='#L648'>648</a>
<a name='L649'></a><a href='#L649'>649</a>
<a name='L650'></a><a href='#L650'>650</a>
<a name='L651'></a><a href='#L651'>651</a>
<a name='L652'></a><a href='#L652'>652</a>
<a name='L653'></a><a href='#L653'>653</a>
<a name='L654'></a><a href='#L654'>654</a>
<a name='L655'></a><a href='#L655'>655</a>
<a name='L656'></a><a href='#L656'>656</a>
<a name='L657'></a><a href='#L657'>657</a>
<a name='L658'></a><a href='#L658'>658</a>
<a name='L659'></a><a href='#L659'>659</a>
<a name='L660'></a><a href='#L660'>660</a>
<a name='L661'></a><a href='#L661'>661</a>
<a name='L662'></a><a href='#L662'>662</a>
<a name='L663'></a><a href='#L663'>663</a>
<a name='L664'></a><a href='#L664'>664</a>
<a name='L665'></a><a href='#L665'>665</a>
<a name='L666'></a><a href='#L666'>666</a>
<a name='L667'></a><a href='#L667'>667</a>
<a name='L668'></a><a href='#L668'>668</a>
<a name='L669'></a><a href='#L669'>669</a>
<a name='L670'></a><a href='#L670'>670</a>
<a name='L671'></a><a href='#L671'>671</a>
<a name='L672'></a><a href='#L672'>672</a>
<a name='L673'></a><a href='#L673'>673</a>
<a name='L674'></a><a href='#L674'>674</a>
<a name='L675'></a><a href='#L675'>675</a>
<a name='L676'></a><a href='#L676'>676</a>
<a name='L677'></a><a href='#L677'>677</a>
<a name='L678'></a><a href='#L678'>678</a>
<a name='L679'></a><a href='#L679'>679</a>
<a name='L680'></a><a href='#L680'>680</a>
<a name='L681'></a><a href='#L681'>681</a>
<a name='L682'></a><a href='#L682'>682</a>
<a name='L683'></a><a href='#L683'>683</a>
<a name='L684'></a><a href='#L684'>684</a>
<a name='L685'></a><a href='#L685'>685</a>
<a name='L686'></a><a href='#L686'>686</a>
<a name='L687'></a><a href='#L687'>687</a>
<a name='L688'></a><a href='#L688'>688</a>
<a name='L689'></a><a href='#L689'>689</a>
<a name='L690'></a><a href='#L690'>690</a>
<a name='L691'></a><a href='#L691'>691</a>
<a name='L692'></a><a href='#L692'>692</a>
<a name='L693'></a><a href='#L693'>693</a>
<a name='L694'></a><a href='#L694'>694</a>
<a name='L695'></a><a href='#L695'>695</a>
<a name='L696'></a><a href='#L696'>696</a>
<a name='L697'></a><a href='#L697'>697</a>
<a name='L698'></a><a href='#L698'>698</a>
<a name='L699'></a><a href='#L699'>699</a>
<a name='L700'></a><a href='#L700'>700</a>
<a name='L701'></a><a href='#L701'>701</a>
<a name='L702'></a><a href='#L702'>702</a>
<a name='L703'></a><a href='#L703'>703</a>
<a name='L704'></a><a href='#L704'>704</a>
<a name='L705'></a><a href='#L705'>705</a>
<a name='L706'></a><a href='#L706'>706</a>
<a name='L707'></a><a href='#L707'>707</a>
<a name='L708'></a><a href='#L708'>708</a>
<a name='L709'></a><a href='#L709'>709</a>
<a name='L710'></a><a href='#L710'>710</a>
<a name='L711'></a><a href='#L711'>711</a>
<a name='L712'></a><a href='#L712'>712</a>
<a name='L713'></a><a href='#L713'>713</a>
<a name='L714'></a><a href='#L714'>714</a>
<a name='L715'></a><a href='#L715'>715</a>
<a name='L716'></a><a href='#L716'>716</a>
<a name='L717'></a><a href='#L717'>717</a>
<a name='L718'></a><a href='#L718'>718</a>
<a name='L719'></a><a href='#L719'>719</a>
<a name='L720'></a><a href='#L720'>720</a>
<a name='L721'></a><a href='#L721'>721</a>
<a name='L722'></a><a href='#L722'>722</a>
<a name='L723'></a><a href='#L723'>723</a>
<a name='L724'></a><a href='#L724'>724</a>
<a name='L725'></a><a href='#L725'>725</a>
<a name='L726'></a><a href='#L726'>726</a>
<a name='L727'></a><a href='#L727'>727</a>
<a name='L728'></a><a href='#L728'>728</a>
<a name='L729'></a><a href='#L729'>729</a>
<a name='L730'></a><a href='#L730'>730</a>
<a name='L731'></a><a href='#L731'>731</a>
<a name='L732'></a><a href='#L732'>732</a>
<a name='L733'></a><a href='#L733'>733</a>
<a name='L734'></a><a href='#L734'>734</a>
<a name='L735'></a><a href='#L735'>735</a>
<a name='L736'></a><a href='#L736'>736</a>
<a name='L737'></a><a href='#L737'>737</a>
<a name='L738'></a><a href='#L738'>738</a>
<a name='L739'></a><a href='#L739'>739</a>
<a name='L740'></a><a href='#L740'>740</a>
<a name='L741'></a><a href='#L741'>741</a>
<a name='L742'></a><a href='#L742'>742</a>
<a name='L743'></a><a href='#L743'>743</a>
<a name='L744'></a><a href='#L744'>744</a>
<a name='L745'></a><a href='#L745'>745</a>
<a name='L746'></a><a href='#L746'>746</a>
<a name='L747'></a><a href='#L747'>747</a>
<a name='L748'></a><a href='#L748'>748</a>
<a name='L749'></a><a href='#L749'>749</a>
<a name='L750'></a><a href='#L750'>750</a>
<a name='L751'></a><a href='#L751'>751</a>
<a name='L752'></a><a href='#L752'>752</a>
<a name='L753'></a><a href='#L753'>753</a>
<a name='L754'></a><a href='#L754'>754</a>
<a name='L755'></a><a href='#L755'>755</a>
<a name='L756'></a><a href='#L756'>756</a>
<a name='L757'></a><a href='#L757'>757</a>
<a name='L758'></a><a href='#L758'>758</a>
<a name='L759'></a><a href='#L759'>759</a>
<a name='L760'></a><a href='#L760'>760</a>
<a name='L761'></a><a href='#L761'>761</a>
<a name='L762'></a><a href='#L762'>762</a>
<a name='L763'></a><a href='#L763'>763</a>
<a name='L764'></a><a href='#L764'>764</a>
<a name='L765'></a><a href='#L765'>765</a>
<a name='L766'></a><a href='#L766'>766</a>
<a name='L767'></a><a href='#L767'>767</a>
<a name='L768'></a><a href='#L768'>768</a>
<a name='L769'></a><a href='#L769'>769</a>
<a name='L770'></a><a href='#L770'>770</a>
<a name='L771'></a><a href='#L771'>771</a>
<a name='L772'></a><a href='#L772'>772</a>
<a name='L773'></a><a href='#L773'>773</a>
<a name='L774'></a><a href='#L774'>774</a>
<a name='L775'></a><a href='#L775'>775</a>
<a name='L776'></a><a href='#L776'>776</a>
<a name='L777'></a><a href='#L777'>777</a>
<a name='L778'></a><a href='#L778'>778</a>
<a name='L779'></a><a href='#L779'>779</a>
<a name='L780'></a><a href='#L780'>780</a>
<a name='L781'></a><a href='#L781'>781</a>
<a name='L782'></a><a href='#L782'>782</a>
<a name='L783'></a><a href='#L783'>783</a>
<a name='L784'></a><a href='#L784'>784</a>
<a name='L785'></a><a href='#L785'>785</a>
<a name='L786'></a><a href='#L786'>786</a>
<a name='L787'></a><a href='#L787'>787</a>
<a name='L788'></a><a href='#L788'>788</a>
<a name='L789'></a><a href='#L789'>789</a>
<a name='L790'></a><a href='#L790'>790</a>
<a name='L791'></a><a href='#L791'>791</a>
<a name='L792'></a><a href='#L792'>792</a>
<a name='L793'></a><a href='#L793'>793</a>
<a name='L794'></a><a href='#L794'>794</a>
<a name='L795'></a><a href='#L795'>795</a>
<a name='L796'></a><a href='#L796'>796</a>
<a name='L797'></a><a href='#L797'>797</a>
<a name='L798'></a><a href='#L798'>798</a>
<a name='L799'></a><a href='#L799'>799</a>
<a name='L800'></a><a href='#L800'>800</a>
<a name='L801'></a><a href='#L801'>801</a>
<a name='L802'></a><a href='#L802'>802</a>
<a name='L803'></a><a href='#L803'>803</a>
<a name='L804'></a><a href='#L804'>804</a>
<a name='L805'></a><a href='#L805'>805</a>
<a name='L806'></a><a href='#L806'>806</a>
<a name='L807'></a><a href='#L807'>807</a>
<a name='L808'></a><a href='#L808'>808</a>
<a name='L809'></a><a href='#L809'>809</a>
<a name='L810'></a><a href='#L810'>810</a>
<a name='L811'></a><a href='#L811'>811</a>
<a name='L812'></a><a href='#L812'>812</a>
<a name='L813'></a><a href='#L813'>813</a>
<a name='L814'></a><a href='#L814'>814</a>
<a name='L815'></a><a href='#L815'>815</a>
<a name='L816'></a><a href='#L816'>816</a>
<a name='L817'></a><a href='#L817'>817</a>
<a name='L818'></a><a href='#L818'>818</a>
<a name='L819'></a><a href='#L819'>819</a>
<a name='L820'></a><a href='#L820'>820</a>
<a name='L821'></a><a href='#L821'>821</a>
<a name='L822'></a><a href='#L822'>822</a>
<a name='L823'></a><a href='#L823'>823</a>
<a name='L824'></a><a href='#L824'>824</a>
<a name='L825'></a><a href='#L825'>825</a>
<a name='L826'></a><a href='#L826'>826</a>
<a name='L827'></a><a href='#L827'>827</a>
<a name='L828'></a><a href='#L828'>828</a>
<a name='L829'></a><a href='#L829'>829</a>
<a name='L830'></a><a href='#L830'>830</a>
<a name='L831'></a><a href='#L831'>831</a>
<a name='L832'></a><a href='#L832'>832</a>
<a name='L833'></a><a href='#L833'>833</a>
<a name='L834'></a><a href='#L834'>834</a>
<a name='L835'></a><a href='#L835'>835</a>
<a name='L836'></a><a href='#L836'>836</a>
<a name='L837'></a><a href='#L837'>837</a>
<a name='L838'></a><a href='#L838'>838</a>
<a name='L839'></a><a href='#L839'>839</a>
<a name='L840'></a><a href='#L840'>840</a>
<a name='L841'></a><a href='#L841'>841</a>
<a name='L842'></a><a href='#L842'>842</a>
<a name='L843'></a><a href='#L843'>843</a>
<a name='L844'></a><a href='#L844'>844</a>
<a name='L845'></a><a href='#L845'>845</a>
<a name='L846'></a><a href='#L846'>846</a>
<a name='L847'></a><a href='#L847'>847</a>
<a name='L848'></a><a href='#L848'>848</a>
<a name='L849'></a><a href='#L849'>849</a>
<a name='L850'></a><a href='#L850'>850</a>
<a name='L851'></a><a href='#L851'>851</a>
<a name='L852'></a><a href='#L852'>852</a>
<a name='L853'></a><a href='#L853'>853</a>
<a name='L854'></a><a href='#L854'>854</a>
<a name='L855'></a><a href='#L855'>855</a>
<a name='L856'></a><a href='#L856'>856</a>
<a name='L857'></a><a href='#L857'>857</a>
<a name='L858'></a><a href='#L858'>858</a>
<a name='L859'></a><a href='#L859'>859</a>
<a name='L860'></a><a href='#L860'>860</a>
<a name='L861'></a><a href='#L861'>861</a>
<a name='L862'></a><a href='#L862'>862</a>
<a name='L863'></a><a href='#L863'>863</a>
<a name='L864'></a><a href='#L864'>864</a>
<a name='L865'></a><a href='#L865'>865</a>
<a name='L866'></a><a href='#L866'>866</a>
<a name='L867'></a><a href='#L867'>867</a>
<a name='L868'></a><a href='#L868'>868</a>
<a name='L869'></a><a href='#L869'>869</a>
<a name='L870'></a><a href='#L870'>870</a>
<a name='L871'></a><a href='#L871'>871</a>
<a name='L872'></a><a href='#L872'>872</a>
<a name='L873'></a><a href='#L873'>873</a>
<a name='L874'></a><a href='#L874'>874</a>
<a name='L875'></a><a href='#L875'>875</a>
<a name='L876'></a><a href='#L876'>876</a>
<a name='L877'></a><a href='#L877'>877</a>
<a name='L878'></a><a href='#L878'>878</a>
<a name='L879'></a><a href='#L879'>879</a>
<a name='L880'></a><a href='#L880'>880</a>
<a name='L881'></a><a href='#L881'>881</a>
<a name='L882'></a><a href='#L882'>882</a>
<a name='L883'></a><a href='#L883'>883</a>
<a name='L884'></a><a href='#L884'>884</a>
<a name='L885'></a><a href='#L885'>885</a>
<a name='L886'></a><a href='#L886'>886</a>
<a name='L887'></a><a href='#L887'>887</a>
<a name='L888'></a><a href='#L888'>888</a>
<a name='L889'></a><a href='#L889'>889</a>
<a name='L890'></a><a href='#L890'>890</a>
<a name='L891'></a><a href='#L891'>891</a>
<a name='L892'></a><a href='#L892'>892</a>
<a name='L893'></a><a href='#L893'>893</a>
<a name='L894'></a><a href='#L894'>894</a>
<a name='L895'></a><a href='#L895'>895</a>
<a name='L896'></a><a href='#L896'>896</a>
<a name='L897'></a><a href='#L897'>897</a>
<a name='L898'></a><a href='#L898'>898</a>
<a name='L899'></a><a href='#L899'>899</a>
<a name='L900'></a><a href='#L900'>900</a>
<a name='L901'></a><a href='#L901'>901</a>
<a name='L902'></a><a href='#L902'>902</a>
<a name='L903'></a><a href='#L903'>903</a>
<a name='L904'></a><a href='#L904'>904</a>
<a name='L905'></a><a href='#L905'>905</a>
<a name='L906'></a><a href='#L906'>906</a>
<a name='L907'></a><a href='#L907'>907</a>
<a name='L908'></a><a href='#L908'>908</a>
<a name='L909'></a><a href='#L909'>909</a>
<a name='L910'></a><a href='#L910'>910</a>
<a name='L911'></a><a href='#L911'>911</a>
<a name='L912'></a><a href='#L912'>912</a>
<a name='L913'></a><a href='#L913'>913</a>
<a name='L914'></a><a href='#L914'>914</a>
<a name='L915'></a><a href='#L915'>915</a>
<a name='L916'></a><a href='#L916'>916</a>
<a name='L917'></a><a href='#L917'>917</a>
<a name='L918'></a><a href='#L918'>918</a>
<a name='L919'></a><a href='#L919'>919</a>
<a name='L920'></a><a href='#L920'>920</a>
<a name='L921'></a><a href='#L921'>921</a>
<a name='L922'></a><a href='#L922'>922</a>
<a name='L923'></a><a href='#L923'>923</a>
<a name='L924'></a><a href='#L924'>924</a>
<a name='L925'></a><a href='#L925'>925</a>
<a name='L926'></a><a href='#L926'>926</a>
<a name='L927'></a><a href='#L927'>927</a>
<a name='L928'></a><a href='#L928'>928</a>
<a name='L929'></a><a href='#L929'>929</a>
<a name='L930'></a><a href='#L930'>930</a>
<a name='L931'></a><a href='#L931'>931</a>
<a name='L932'></a><a href='#L932'>932</a>
<a name='L933'></a><a href='#L933'>933</a>
<a name='L934'></a><a href='#L934'>934</a>
<a name='L935'></a><a href='#L935'>935</a>
<a name='L936'></a><a href='#L936'>936</a>
<a name='L937'></a><a href='#L937'>937</a>
<a name='L938'></a><a href='#L938'>938</a>
<a name='L939'></a><a href='#L939'>939</a>
<a name='L940'></a><a href='#L940'>940</a>
<a name='L941'></a><a href='#L941'>941</a>
<a name='L942'></a><a href='#L942'>942</a>
<a name='L943'></a><a href='#L943'>943</a>
<a name='L944'></a><a href='#L944'>944</a>
<a name='L945'></a><a href='#L945'>945</a>
<a name='L946'></a><a href='#L946'>946</a>
<a name='L947'></a><a href='#L947'>947</a>
<a name='L948'></a><a href='#L948'>948</a>
<a name='L949'></a><a href='#L949'>949</a>
<a name='L950'></a><a href='#L950'>950</a>
<a name='L951'></a><a href='#L951'>951</a>
<a name='L952'></a><a href='#L952'>952</a>
<a name='L953'></a><a href='#L953'>953</a>
<a name='L954'></a><a href='#L954'>954</a>
<a name='L955'></a><a href='#L955'>955</a>
<a name='L956'></a><a href='#L956'>956</a>
<a name='L957'></a><a href='#L957'>957</a>
<a name='L958'></a><a href='#L958'>958</a>
<a name='L959'></a><a href='#L959'>959</a>
<a name='L960'></a><a href='#L960'>960</a>
<a name='L961'></a><a href='#L961'>961</a>
<a name='L962'></a><a href='#L962'>962</a>
<a name='L963'></a><a href='#L963'>963</a>
<a name='L964'></a><a href='#L964'>964</a>
<a name='L965'></a><a href='#L965'>965</a>
<a name='L966'></a><a href='#L966'>966</a>
<a name='L967'></a><a href='#L967'>967</a>
<a name='L968'></a><a href='#L968'>968</a>
<a name='L969'></a><a href='#L969'>969</a>
<a name='L970'></a><a href='#L970'>970</a>
<a name='L971'></a><a href='#L971'>971</a>
<a name='L972'></a><a href='#L972'>972</a>
<a name='L973'></a><a href='#L973'>973</a>
<a name='L974'></a><a href='#L974'>974</a>
<a name='L975'></a><a href='#L975'>975</a>
<a name='L976'></a><a href='#L976'>976</a>
<a name='L977'></a><a href='#L977'>977</a>
<a name='L978'></a><a href='#L978'>978</a>
<a name='L979'></a><a href='#L979'>979</a>
<a name='L980'></a><a href='#L980'>980</a>
<a name='L981'></a><a href='#L981'>981</a>
<a name='L982'></a><a href='#L982'>982</a>
<a name='L983'></a><a href='#L983'>983</a>
<a name='L984'></a><a href='#L984'>984</a>
<a name='L985'></a><a href='#L985'>985</a>
<a name='L986'></a><a href='#L986'>986</a>
<a name='L987'></a><a href='#L987'>987</a>
<a name='L988'></a><a href='#L988'>988</a>
<a name='L989'></a><a href='#L989'>989</a>
<a name='L990'></a><a href='#L990'>990</a>
<a name='L991'></a><a href='#L991'>991</a>
<a name='L992'></a><a href='#L992'>992</a>
<a name='L993'></a><a href='#L993'>993</a>
<a name='L994'></a><a href='#L994'>994</a>
<a name='L995'></a><a href='#L995'>995</a>
<a name='L996'></a><a href='#L996'>996</a>
<a name='L997'></a><a href='#L997'>997</a>
<a name='L998'></a><a href='#L998'>998</a>
<a name='L999'></a><a href='#L999'>999</a>
<a name='L1000'></a><a href='#L1000'>1000</a>
<a name='L1001'></a><a href='#L1001'>1001</a>
<a name='L1002'></a><a href='#L1002'>1002</a>
<a name='L1003'></a><a href='#L1003'>1003</a>
<a name='L1004'></a><a href='#L1004'>1004</a>
<a name='L1005'></a><a href='#L1005'>1005</a>
<a name='L1006'></a><a href='#L1006'>1006</a>
<a name='L1007'></a><a href='#L1007'>1007</a>
<a name='L1008'></a><a href='#L1008'>1008</a>
<a name='L1009'></a><a href='#L1009'>1009</a>
<a name='L1010'></a><a href='#L1010'>1010</a>
<a name='L1011'></a><a href='#L1011'>1011</a>
<a name='L1012'></a><a href='#L1012'>1012</a>
<a name='L1013'></a><a href='#L1013'>1013</a>
<a name='L1014'></a><a href='#L1014'>1014</a>
<a name='L1015'></a><a href='#L1015'>1015</a>
<a name='L1016'></a><a href='#L1016'>1016</a>
<a name='L1017'></a><a href='#L1017'>1017</a>
<a name='L1018'></a><a href='#L1018'>1018</a>
<a name='L1019'></a><a href='#L1019'>1019</a>
<a name='L1020'></a><a href='#L1020'>1020</a>
<a name='L1021'></a><a href='#L1021'>1021</a>
<a name='L1022'></a><a href='#L1022'>1022</a>
<a name='L1023'></a><a href='#L1023'>1023</a>
<a name='L1024'></a><a href='#L1024'>1024</a>
<a name='L1025'></a><a href='#L1025'>1025</a>
<a name='L1026'></a><a href='#L1026'>1026</a>
<a name='L1027'></a><a href='#L1027'>1027</a>
<a name='L1028'></a><a href='#L1028'>1028</a>
<a name='L1029'></a><a href='#L1029'>1029</a>
<a name='L1030'></a><a href='#L1030'>1030</a>
<a name='L1031'></a><a href='#L1031'>1031</a>
<a name='L1032'></a><a href='#L1032'>1032</a>
<a name='L1033'></a><a href='#L1033'>1033</a>
<a name='L1034'></a><a href='#L1034'>1034</a>
<a name='L1035'></a><a href='#L1035'>1035</a>
<a name='L1036'></a><a href='#L1036'>1036</a>
<a name='L1037'></a><a href='#L1037'>1037</a>
<a name='L1038'></a><a href='#L1038'>1038</a>
<a name='L1039'></a><a href='#L1039'>1039</a>
<a name='L1040'></a><a href='#L1040'>1040</a>
<a name='L1041'></a><a href='#L1041'>1041</a>
<a name='L1042'></a><a href='#L1042'>1042</a>
<a name='L1043'></a><a href='#L1043'>1043</a>
<a name='L1044'></a><a href='#L1044'>1044</a>
<a name='L1045'></a><a href='#L1045'>1045</a>
<a name='L1046'></a><a href='#L1046'>1046</a>
<a name='L1047'></a><a href='#L1047'>1047</a>
<a name='L1048'></a><a href='#L1048'>1048</a>
<a name='L1049'></a><a href='#L1049'>1049</a>
<a name='L1050'></a><a href='#L1050'>1050</a>
<a name='L1051'></a><a href='#L1051'>1051</a>
<a name='L1052'></a><a href='#L1052'>1052</a>
<a name='L1053'></a><a href='#L1053'>1053</a>
<a name='L1054'></a><a href='#L1054'>1054</a>
<a name='L1055'></a><a href='#L1055'>1055</a>
<a name='L1056'></a><a href='#L1056'>1056</a>
<a name='L1057'></a><a href='#L1057'>1057</a>
<a name='L1058'></a><a href='#L1058'>1058</a>
<a name='L1059'></a><a href='#L1059'>1059</a>
<a name='L1060'></a><a href='#L1060'>1060</a>
<a name='L1061'></a><a href='#L1061'>1061</a>
<a name='L1062'></a><a href='#L1062'>1062</a>
<a name='L1063'></a><a href='#L1063'>1063</a>
<a name='L1064'></a><a href='#L1064'>1064</a>
<a name='L1065'></a><a href='#L1065'>1065</a>
<a name='L1066'></a><a href='#L1066'>1066</a>
<a name='L1067'></a><a href='#L1067'>1067</a>
<a name='L1068'></a><a href='#L1068'>1068</a>
<a name='L1069'></a><a href='#L1069'>1069</a>
<a name='L1070'></a><a href='#L1070'>1070</a>
<a name='L1071'></a><a href='#L1071'>1071</a>
<a name='L1072'></a><a href='#L1072'>1072</a>
<a name='L1073'></a><a href='#L1073'>1073</a>
<a name='L1074'></a><a href='#L1074'>1074</a>
<a name='L1075'></a><a href='#L1075'>1075</a>
<a name='L1076'></a><a href='#L1076'>1076</a>
<a name='L1077'></a><a href='#L1077'>1077</a>
<a name='L1078'></a><a href='#L1078'>1078</a>
<a name='L1079'></a><a href='#L1079'>1079</a>
<a name='L1080'></a><a href='#L1080'>1080</a>
<a name='L1081'></a><a href='#L1081'>1081</a>
<a name='L1082'></a><a href='#L1082'>1082</a>
<a name='L1083'></a><a href='#L1083'>1083</a>
<a name='L1084'></a><a href='#L1084'>1084</a>
<a name='L1085'></a><a href='#L1085'>1085</a>
<a name='L1086'></a><a href='#L1086'>1086</a>
<a name='L1087'></a><a href='#L1087'>1087</a>
<a name='L1088'></a><a href='#L1088'>1088</a>
<a name='L1089'></a><a href='#L1089'>1089</a>
<a name='L1090'></a><a href='#L1090'>1090</a>
<a name='L1091'></a><a href='#L1091'>1091</a>
<a name='L1092'></a><a href='#L1092'>1092</a>
<a name='L1093'></a><a href='#L1093'>1093</a>
<a name='L1094'></a><a href='#L1094'>1094</a>
<a name='L1095'></a><a href='#L1095'>1095</a>
<a name='L1096'></a><a href='#L1096'>1096</a>
<a name='L1097'></a><a href='#L1097'>1097</a>
<a name='L1098'></a><a href='#L1098'>1098</a>
<a name='L1099'></a><a href='#L1099'>1099</a>
<a name='L1100'></a><a href='#L1100'>1100</a>
<a name='L1101'></a><a href='#L1101'>1101</a>
<a name='L1102'></a><a href='#L1102'>1102</a>
<a name='L1103'></a><a href='#L1103'>1103</a>
<a name='L1104'></a><a href='#L1104'>1104</a>
<a name='L1105'></a><a href='#L1105'>1105</a>
<a name='L1106'></a><a href='#L1106'>1106</a>
<a name='L1107'></a><a href='#L1107'>1107</a>
<a name='L1108'></a><a href='#L1108'>1108</a>
<a name='L1109'></a><a href='#L1109'>1109</a>
<a name='L1110'></a><a href='#L1110'>1110</a>
<a name='L1111'></a><a href='#L1111'>1111</a>
<a name='L1112'></a><a href='#L1112'>1112</a>
<a name='L1113'></a><a href='#L1113'>1113</a>
<a name='L1114'></a><a href='#L1114'>1114</a>
<a name='L1115'></a><a href='#L1115'>1115</a>
<a name='L1116'></a><a href='#L1116'>1116</a>
<a name='L1117'></a><a href='#L1117'>1117</a>
<a name='L1118'></a><a href='#L1118'>1118</a>
<a name='L1119'></a><a href='#L1119'>1119</a>
<a name='L1120'></a><a href='#L1120'>1120</a>
<a name='L1121'></a><a href='#L1121'>1121</a>
<a name='L1122'></a><a href='#L1122'>1122</a>
<a name='L1123'></a><a href='#L1123'>1123</a>
<a name='L1124'></a><a href='#L1124'>1124</a>
<a name='L1125'></a><a href='#L1125'>1125</a>
<a name='L1126'></a><a href='#L1126'>1126</a>
<a name='L1127'></a><a href='#L1127'>1127</a>
<a name='L1128'></a><a href='#L1128'>1128</a>
<a name='L1129'></a><a href='#L1129'>1129</a>
<a name='L1130'></a><a href='#L1130'>1130</a>
<a name='L1131'></a><a href='#L1131'>1131</a>
<a name='L1132'></a><a href='#L1132'>1132</a>
<a name='L1133'></a><a href='#L1133'>1133</a>
<a name='L1134'></a><a href='#L1134'>1134</a>
<a name='L1135'></a><a href='#L1135'>1135</a>
<a name='L1136'></a><a href='#L1136'>1136</a>
<a name='L1137'></a><a href='#L1137'>1137</a>
<a name='L1138'></a><a href='#L1138'>1138</a>
<a name='L1139'></a><a href='#L1139'>1139</a>
<a name='L1140'></a><a href='#L1140'>1140</a>
<a name='L1141'></a><a href='#L1141'>1141</a>
<a name='L1142'></a><a href='#L1142'>1142</a>
<a name='L1143'></a><a href='#L1143'>1143</a>
<a name='L1144'></a><a href='#L1144'>1144</a>
<a name='L1145'></a><a href='#L1145'>1145</a>
<a name='L1146'></a><a href='#L1146'>1146</a>
<a name='L1147'></a><a href='#L1147'>1147</a>
<a name='L1148'></a><a href='#L1148'>1148</a>
<a name='L1149'></a><a href='#L1149'>1149</a>
<a name='L1150'></a><a href='#L1150'>1150</a>
<a name='L1151'></a><a href='#L1151'>1151</a>
<a name='L1152'></a><a href='#L1152'>1152</a>
<a name='L1153'></a><a href='#L1153'>1153</a>
<a name='L1154'></a><a href='#L1154'>1154</a>
<a name='L1155'></a><a href='#L1155'>1155</a>
<a name='L1156'></a><a href='#L1156'>1156</a>
<a name='L1157'></a><a href='#L1157'>1157</a>
<a name='L1158'></a><a href='#L1158'>1158</a>
<a name='L1159'></a><a href='#L1159'>1159</a>
<a name='L1160'></a><a href='#L1160'>1160</a>
<a name='L1161'></a><a href='#L1161'>1161</a>
<a name='L1162'></a><a href='#L1162'>1162</a>
<a name='L1163'></a><a href='#L1163'>1163</a>
<a name='L1164'></a><a href='#L1164'>1164</a>
<a name='L1165'></a><a href='#L1165'>1165</a>
<a name='L1166'></a><a href='#L1166'>1166</a>
<a name='L1167'></a><a href='#L1167'>1167</a>
<a name='L1168'></a><a href='#L1168'>1168</a>
<a name='L1169'></a><a href='#L1169'>1169</a>
<a name='L1170'></a><a href='#L1170'>1170</a>
<a name='L1171'></a><a href='#L1171'>1171</a>
<a name='L1172'></a><a href='#L1172'>1172</a>
<a name='L1173'></a><a href='#L1173'>1173</a>
<a name='L1174'></a><a href='#L1174'>1174</a>
<a name='L1175'></a><a href='#L1175'>1175</a>
<a name='L1176'></a><a href='#L1176'>1176</a>
<a name='L1177'></a><a href='#L1177'>1177</a>
<a name='L1178'></a><a href='#L1178'>1178</a>
<a name='L1179'></a><a href='#L1179'>1179</a>
<a name='L1180'></a><a href='#L1180'>1180</a>
<a name='L1181'></a><a href='#L1181'>1181</a>
<a name='L1182'></a><a href='#L1182'>1182</a>
<a name='L1183'></a><a href='#L1183'>1183</a>
<a name='L1184'></a><a href='#L1184'>1184</a>
<a name='L1185'></a><a href='#L1185'>1185</a>
<a name='L1186'></a><a href='#L1186'>1186</a>
<a name='L1187'></a><a href='#L1187'>1187</a>
<a name='L1188'></a><a href='#L1188'>1188</a>
<a name='L1189'></a><a href='#L1189'>1189</a>
<a name='L1190'></a><a href='#L1190'>1190</a>
<a name='L1191'></a><a href='#L1191'>1191</a>
<a name='L1192'></a><a href='#L1192'>1192</a>
<a name='L1193'></a><a href='#L1193'>1193</a>
<a name='L1194'></a><a href='#L1194'>1194</a>
<a name='L1195'></a><a href='#L1195'>1195</a>
<a name='L1196'></a><a href='#L1196'>1196</a>
<a name='L1197'></a><a href='#L1197'>1197</a>
<a name='L1198'></a><a href='#L1198'>1198</a>
<a name='L1199'></a><a href='#L1199'>1199</a>
<a name='L1200'></a><a href='#L1200'>1200</a>
<a name='L1201'></a><a href='#L1201'>1201</a>
<a name='L1202'></a><a href='#L1202'>1202</a>
<a name='L1203'></a><a href='#L1203'>1203</a>
<a name='L1204'></a><a href='#L1204'>1204</a>
<a name='L1205'></a><a href='#L1205'>1205</a>
<a name='L1206'></a><a href='#L1206'>1206</a>
<a name='L1207'></a><a href='#L1207'>1207</a>
<a name='L1208'></a><a href='#L1208'>1208</a>
<a name='L1209'></a><a href='#L1209'>1209</a>
<a name='L1210'></a><a href='#L1210'>1210</a>
<a name='L1211'></a><a href='#L1211'>1211</a>
<a name='L1212'></a><a href='#L1212'>1212</a>
<a name='L1213'></a><a href='#L1213'>1213</a>
<a name='L1214'></a><a href='#L1214'>1214</a>
<a name='L1215'></a><a href='#L1215'>1215</a>
<a name='L1216'></a><a href='#L1216'>1216</a>
<a name='L1217'></a><a href='#L1217'>1217</a>
<a name='L1218'></a><a href='#L1218'>1218</a>
<a name='L1219'></a><a href='#L1219'>1219</a>
<a name='L1220'></a><a href='#L1220'>1220</a>
<a name='L1221'></a><a href='#L1221'>1221</a>
<a name='L1222'></a><a href='#L1222'>1222</a>
<a name='L1223'></a><a href='#L1223'>1223</a>
<a name='L1224'></a><a href='#L1224'>1224</a>
<a name='L1225'></a><a href='#L1225'>1225</a>
<a name='L1226'></a><a href='#L1226'>1226</a>
<a name='L1227'></a><a href='#L1227'>1227</a>
<a name='L1228'></a><a href='#L1228'>1228</a>
<a name='L1229'></a><a href='#L1229'>1229</a>
<a name='L1230'></a><a href='#L1230'>1230</a>
<a name='L1231'></a><a href='#L1231'>1231</a>
<a name='L1232'></a><a href='#L1232'>1232</a>
<a name='L1233'></a><a href='#L1233'>1233</a>
<a name='L1234'></a><a href='#L1234'>1234</a>
<a name='L1235'></a><a href='#L1235'>1235</a>
<a name='L1236'></a><a href='#L1236'>1236</a>
<a name='L1237'></a><a href='#L1237'>1237</a>
<a name='L1238'></a><a href='#L1238'>1238</a>
<a name='L1239'></a><a href='#L1239'>1239</a>
<a name='L1240'></a><a href='#L1240'>1240</a>
<a name='L1241'></a><a href='#L1241'>1241</a>
<a name='L1242'></a><a href='#L1242'>1242</a>
<a name='L1243'></a><a href='#L1243'>1243</a>
<a name='L1244'></a><a href='#L1244'>1244</a>
<a name='L1245'></a><a href='#L1245'>1245</a>
<a name='L1246'></a><a href='#L1246'>1246</a>
<a name='L1247'></a><a href='#L1247'>1247</a>
<a name='L1248'></a><a href='#L1248'>1248</a>
<a name='L1249'></a><a href='#L1249'>1249</a>
<a name='L1250'></a><a href='#L1250'>1250</a>
<a name='L1251'></a><a href='#L1251'>1251</a>
<a name='L1252'></a><a href='#L1252'>1252</a>
<a name='L1253'></a><a href='#L1253'>1253</a>
<a name='L1254'></a><a href='#L1254'>1254</a>
<a name='L1255'></a><a href='#L1255'>1255</a>
<a name='L1256'></a><a href='#L1256'>1256</a>
<a name='L1257'></a><a href='#L1257'>1257</a>
<a name='L1258'></a><a href='#L1258'>1258</a>
<a name='L1259'></a><a href='#L1259'>1259</a>
<a name='L1260'></a><a href='#L1260'>1260</a>
<a name='L1261'></a><a href='#L1261'>1261</a>
<a name='L1262'></a><a href='#L1262'>1262</a>
<a name='L1263'></a><a href='#L1263'>1263</a>
<a name='L1264'></a><a href='#L1264'>1264</a>
<a name='L1265'></a><a href='#L1265'>1265</a>
<a name='L1266'></a><a href='#L1266'>1266</a>
<a name='L1267'></a><a href='#L1267'>1267</a>
<a name='L1268'></a><a href='#L1268'>1268</a>
<a name='L1269'></a><a href='#L1269'>1269</a>
<a name='L1270'></a><a href='#L1270'>1270</a>
<a name='L1271'></a><a href='#L1271'>1271</a>
<a name='L1272'></a><a href='#L1272'>1272</a>
<a name='L1273'></a><a href='#L1273'>1273</a>
<a name='L1274'></a><a href='#L1274'>1274</a>
<a name='L1275'></a><a href='#L1275'>1275</a>
<a name='L1276'></a><a href='#L1276'>1276</a>
<a name='L1277'></a><a href='#L1277'>1277</a>
<a name='L1278'></a><a href='#L1278'>1278</a>
<a name='L1279'></a><a href='#L1279'>1279</a>
<a name='L1280'></a><a href='#L1280'>1280</a>
<a name='L1281'></a><a href='#L1281'>1281</a>
<a name='L1282'></a><a href='#L1282'>1282</a>
<a name='L1283'></a><a href='#L1283'>1283</a>
<a name='L1284'></a><a href='#L1284'>1284</a>
<a name='L1285'></a><a href='#L1285'>1285</a>
<a name='L1286'></a><a href='#L1286'>1286</a>
<a name='L1287'></a><a href='#L1287'>1287</a>
<a name='L1288'></a><a href='#L1288'>1288</a>
<a name='L1289'></a><a href='#L1289'>1289</a>
<a name='L1290'></a><a href='#L1290'>1290</a>
<a name='L1291'></a><a href='#L1291'>1291</a>
<a name='L1292'></a><a href='#L1292'>1292</a>
<a name='L1293'></a><a href='#L1293'>1293</a>
<a name='L1294'></a><a href='#L1294'>1294</a>
<a name='L1295'></a><a href='#L1295'>1295</a>
<a name='L1296'></a><a href='#L1296'>1296</a>
<a name='L1297'></a><a href='#L1297'>1297</a>
<a name='L1298'></a><a href='#L1298'>1298</a>
<a name='L1299'></a><a href='#L1299'>1299</a>
<a name='L1300'></a><a href='#L1300'>1300</a>
<a name='L1301'></a><a href='#L1301'>1301</a>
<a name='L1302'></a><a href='#L1302'>1302</a>
<a name='L1303'></a><a href='#L1303'>1303</a>
<a name='L1304'></a><a href='#L1304'>1304</a>
<a name='L1305'></a><a href='#L1305'>1305</a>
<a name='L1306'></a><a href='#L1306'>1306</a>
<a name='L1307'></a><a href='#L1307'>1307</a>
<a name='L1308'></a><a href='#L1308'>1308</a>
<a name='L1309'></a><a href='#L1309'>1309</a>
<a name='L1310'></a><a href='#L1310'>1310</a>
<a name='L1311'></a><a href='#L1311'>1311</a>
<a name='L1312'></a><a href='#L1312'>1312</a>
<a name='L1313'></a><a href='#L1313'>1313</a>
<a name='L1314'></a><a href='#L1314'>1314</a>
<a name='L1315'></a><a href='#L1315'>1315</a>
<a name='L1316'></a><a href='#L1316'>1316</a>
<a name='L1317'></a><a href='#L1317'>1317</a>
<a name='L1318'></a><a href='#L1318'>1318</a>
<a name='L1319'></a><a href='#L1319'>1319</a>
<a name='L1320'></a><a href='#L1320'>1320</a>
<a name='L1321'></a><a href='#L1321'>1321</a>
<a name='L1322'></a><a href='#L1322'>1322</a>
<a name='L1323'></a><a href='#L1323'>1323</a>
<a name='L1324'></a><a href='#L1324'>1324</a>
<a name='L1325'></a><a href='#L1325'>1325</a>
<a name='L1326'></a><a href='#L1326'>1326</a>
<a name='L1327'></a><a href='#L1327'>1327</a>
<a name='L1328'></a><a href='#L1328'>1328</a>
<a name='L1329'></a><a href='#L1329'>1329</a>
<a name='L1330'></a><a href='#L1330'>1330</a>
<a name='L1331'></a><a href='#L1331'>1331</a>
<a name='L1332'></a><a href='#L1332'>1332</a>
<a name='L1333'></a><a href='#L1333'>1333</a>
<a name='L1334'></a><a href='#L1334'>1334</a>
<a name='L1335'></a><a href='#L1335'>1335</a>
<a name='L1336'></a><a href='#L1336'>1336</a>
<a name='L1337'></a><a href='#L1337'>1337</a>
<a name='L1338'></a><a href='#L1338'>1338</a>
<a name='L1339'></a><a href='#L1339'>1339</a>
<a name='L1340'></a><a href='#L1340'>1340</a>
<a name='L1341'></a><a href='#L1341'>1341</a>
<a name='L1342'></a><a href='#L1342'>1342</a>
<a name='L1343'></a><a href='#L1343'>1343</a>
<a name='L1344'></a><a href='#L1344'>1344</a>
<a name='L1345'></a><a href='#L1345'>1345</a>
<a name='L1346'></a><a href='#L1346'>1346</a>
<a name='L1347'></a><a href='#L1347'>1347</a>
<a name='L1348'></a><a href='#L1348'>1348</a>
<a name='L1349'></a><a href='#L1349'>1349</a>
<a name='L1350'></a><a href='#L1350'>1350</a>
<a name='L1351'></a><a href='#L1351'>1351</a>
<a name='L1352'></a><a href='#L1352'>1352</a>
<a name='L1353'></a><a href='#L1353'>1353</a>
<a name='L1354'></a><a href='#L1354'>1354</a>
<a name='L1355'></a><a href='#L1355'>1355</a>
<a name='L1356'></a><a href='#L1356'>1356</a>
<a name='L1357'></a><a href='#L1357'>1357</a>
<a name='L1358'></a><a href='#L1358'>1358</a>
<a name='L1359'></a><a href='#L1359'>1359</a>
<a name='L1360'></a><a href='#L1360'>1360</a>
<a name='L1361'></a><a href='#L1361'>1361</a>
<a name='L1362'></a><a href='#L1362'>1362</a>
<a name='L1363'></a><a href='#L1363'>1363</a>
<a name='L1364'></a><a href='#L1364'>1364</a>
<a name='L1365'></a><a href='#L1365'>1365</a>
<a name='L1366'></a><a href='#L1366'>1366</a>
<a name='L1367'></a><a href='#L1367'>1367</a>
<a name='L1368'></a><a href='#L1368'>1368</a>
<a name='L1369'></a><a href='#L1369'>1369</a>
<a name='L1370'></a><a href='#L1370'>1370</a>
<a name='L1371'></a><a href='#L1371'>1371</a>
<a name='L1372'></a><a href='#L1372'>1372</a>
<a name='L1373'></a><a href='#L1373'>1373</a>
<a name='L1374'></a><a href='#L1374'>1374</a>
<a name='L1375'></a><a href='#L1375'>1375</a>
<a name='L1376'></a><a href='#L1376'>1376</a>
<a name='L1377'></a><a href='#L1377'>1377</a>
<a name='L1378'></a><a href='#L1378'>1378</a>
<a name='L1379'></a><a href='#L1379'>1379</a>
<a name='L1380'></a><a href='#L1380'>1380</a>
<a name='L1381'></a><a href='#L1381'>1381</a>
<a name='L1382'></a><a href='#L1382'>1382</a>
<a name='L1383'></a><a href='#L1383'>1383</a>
<a name='L1384'></a><a href='#L1384'>1384</a>
<a name='L1385'></a><a href='#L1385'>1385</a>
<a name='L1386'></a><a href='#L1386'>1386</a>
<a name='L1387'></a><a href='#L1387'>1387</a>
<a name='L1388'></a><a href='#L1388'>1388</a>
<a name='L1389'></a><a href='#L1389'>1389</a>
<a name='L1390'></a><a href='#L1390'>1390</a>
<a name='L1391'></a><a href='#L1391'>1391</a>
<a name='L1392'></a><a href='#L1392'>1392</a>
<a name='L1393'></a><a href='#L1393'>1393</a>
<a name='L1394'></a><a href='#L1394'>1394</a>
<a name='L1395'></a><a href='#L1395'>1395</a>
<a name='L1396'></a><a href='#L1396'>1396</a>
<a name='L1397'></a><a href='#L1397'>1397</a>
<a name='L1398'></a><a href='#L1398'>1398</a>
<a name='L1399'></a><a href='#L1399'>1399</a>
<a name='L1400'></a><a href='#L1400'>1400</a>
<a name='L1401'></a><a href='#L1401'>1401</a>
<a name='L1402'></a><a href='#L1402'>1402</a>
<a name='L1403'></a><a href='#L1403'>1403</a>
<a name='L1404'></a><a href='#L1404'>1404</a>
<a name='L1405'></a><a href='#L1405'>1405</a>
<a name='L1406'></a><a href='#L1406'>1406</a>
<a name='L1407'></a><a href='#L1407'>1407</a>
<a name='L1408'></a><a href='#L1408'>1408</a>
<a name='L1409'></a><a href='#L1409'>1409</a>
<a name='L1410'></a><a href='#L1410'>1410</a>
<a name='L1411'></a><a href='#L1411'>1411</a>
<a name='L1412'></a><a href='#L1412'>1412</a>
<a name='L1413'></a><a href='#L1413'>1413</a>
<a name='L1414'></a><a href='#L1414'>1414</a>
<a name='L1415'></a><a href='#L1415'>1415</a>
<a name='L1416'></a><a href='#L1416'>1416</a>
<a name='L1417'></a><a href='#L1417'>1417</a>
<a name='L1418'></a><a href='#L1418'>1418</a>
<a name='L1419'></a><a href='#L1419'>1419</a>
<a name='L1420'></a><a href='#L1420'>1420</a>
<a name='L1421'></a><a href='#L1421'>1421</a>
<a name='L1422'></a><a href='#L1422'>1422</a>
<a name='L1423'></a><a href='#L1423'>1423</a>
<a name='L1424'></a><a href='#L1424'>1424</a>
<a name='L1425'></a><a href='#L1425'>1425</a>
<a name='L1426'></a><a href='#L1426'>1426</a>
<a name='L1427'></a><a href='#L1427'>1427</a>
<a name='L1428'></a><a href='#L1428'>1428</a>
<a name='L1429'></a><a href='#L1429'>1429</a>
<a name='L1430'></a><a href='#L1430'>1430</a>
<a name='L1431'></a><a href='#L1431'>1431</a>
<a name='L1432'></a><a href='#L1432'>1432</a>
<a name='L1433'></a><a href='#L1433'>1433</a>
<a name='L1434'></a><a href='#L1434'>1434</a>
<a name='L1435'></a><a href='#L1435'>1435</a>
<a name='L1436'></a><a href='#L1436'>1436</a>
<a name='L1437'></a><a href='#L1437'>1437</a>
<a name='L1438'></a><a href='#L1438'>1438</a>
<a name='L1439'></a><a href='#L1439'>1439</a>
<a name='L1440'></a><a href='#L1440'>1440</a>
<a name='L1441'></a><a href='#L1441'>1441</a>
<a name='L1442'></a><a href='#L1442'>1442</a>
<a name='L1443'></a><a href='#L1443'>1443</a>
<a name='L1444'></a><a href='#L1444'>1444</a>
<a name='L1445'></a><a href='#L1445'>1445</a>
<a name='L1446'></a><a href='#L1446'>1446</a>
<a name='L1447'></a><a href='#L1447'>1447</a>
<a name='L1448'></a><a href='#L1448'>1448</a>
<a name='L1449'></a><a href='#L1449'>1449</a>
<a name='L1450'></a><a href='#L1450'>1450</a>
<a name='L1451'></a><a href='#L1451'>1451</a>
<a name='L1452'></a><a href='#L1452'>1452</a>
<a name='L1453'></a><a href='#L1453'>1453</a>
<a name='L1454'></a><a href='#L1454'>1454</a>
<a name='L1455'></a><a href='#L1455'>1455</a>
<a name='L1456'></a><a href='#L1456'>1456</a>
<a name='L1457'></a><a href='#L1457'>1457</a>
<a name='L1458'></a><a href='#L1458'>1458</a>
<a name='L1459'></a><a href='#L1459'>1459</a>
<a name='L1460'></a><a href='#L1460'>1460</a>
<a name='L1461'></a><a href='#L1461'>1461</a>
<a name='L1462'></a><a href='#L1462'>1462</a>
<a name='L1463'></a><a href='#L1463'>1463</a>
<a name='L1464'></a><a href='#L1464'>1464</a>
<a name='L1465'></a><a href='#L1465'>1465</a>
<a name='L1466'></a><a href='#L1466'>1466</a>
<a name='L1467'></a><a href='#L1467'>1467</a>
<a name='L1468'></a><a href='#L1468'>1468</a>
<a name='L1469'></a><a href='#L1469'>1469</a>
<a name='L1470'></a><a href='#L1470'>1470</a>
<a name='L1471'></a><a href='#L1471'>1471</a>
<a name='L1472'></a><a href='#L1472'>1472</a>
<a name='L1473'></a><a href='#L1473'>1473</a>
<a name='L1474'></a><a href='#L1474'>1474</a>
<a name='L1475'></a><a href='#L1475'>1475</a>
<a name='L1476'></a><a href='#L1476'>1476</a>
<a name='L1477'></a><a href='#L1477'>1477</a>
<a name='L1478'></a><a href='#L1478'>1478</a>
<a name='L1479'></a><a href='#L1479'>1479</a>
<a name='L1480'></a><a href='#L1480'>1480</a>
<a name='L1481'></a><a href='#L1481'>1481</a>
<a name='L1482'></a><a href='#L1482'>1482</a>
<a name='L1483'></a><a href='#L1483'>1483</a>
<a name='L1484'></a><a href='#L1484'>1484</a>
<a name='L1485'></a><a href='#L1485'>1485</a>
<a name='L1486'></a><a href='#L1486'>1486</a>
<a name='L1487'></a><a href='#L1487'>1487</a>
<a name='L1488'></a><a href='#L1488'>1488</a>
<a name='L1489'></a><a href='#L1489'>1489</a>
<a name='L1490'></a><a href='#L1490'>1490</a>
<a name='L1491'></a><a href='#L1491'>1491</a>
<a name='L1492'></a><a href='#L1492'>1492</a>
<a name='L1493'></a><a href='#L1493'>1493</a>
<a name='L1494'></a><a href='#L1494'>1494</a>
<a name='L1495'></a><a href='#L1495'>1495</a>
<a name='L1496'></a><a href='#L1496'>1496</a>
<a name='L1497'></a><a href='#L1497'>1497</a>
<a name='L1498'></a><a href='#L1498'>1498</a>
<a name='L1499'></a><a href='#L1499'>1499</a>
<a name='L1500'></a><a href='#L1500'>1500</a>
<a name='L1501'></a><a href='#L1501'>1501</a>
<a name='L1502'></a><a href='#L1502'>1502</a>
<a name='L1503'></a><a href='#L1503'>1503</a>
<a name='L1504'></a><a href='#L1504'>1504</a>
<a name='L1505'></a><a href='#L1505'>1505</a>
<a name='L1506'></a><a href='#L1506'>1506</a>
<a name='L1507'></a><a href='#L1507'>1507</a>
<a name='L1508'></a><a href='#L1508'>1508</a>
<a name='L1509'></a><a href='#L1509'>1509</a>
<a name='L1510'></a><a href='#L1510'>1510</a>
<a name='L1511'></a><a href='#L1511'>1511</a>
<a name='L1512'></a><a href='#L1512'>1512</a>
<a name='L1513'></a><a href='#L1513'>1513</a>
<a name='L1514'></a><a href='#L1514'>1514</a>
<a name='L1515'></a><a href='#L1515'>1515</a>
<a name='L1516'></a><a href='#L1516'>1516</a>
<a name='L1517'></a><a href='#L1517'>1517</a>
<a name='L1518'></a><a href='#L1518'>1518</a>
<a name='L1519'></a><a href='#L1519'>1519</a>
<a name='L1520'></a><a href='#L1520'>1520</a>
<a name='L1521'></a><a href='#L1521'>1521</a>
<a name='L1522'></a><a href='#L1522'>1522</a>
<a name='L1523'></a><a href='#L1523'>1523</a>
<a name='L1524'></a><a href='#L1524'>1524</a>
<a name='L1525'></a><a href='#L1525'>1525</a>
<a name='L1526'></a><a href='#L1526'>1526</a>
<a name='L1527'></a><a href='#L1527'>1527</a>
<a name='L1528'></a><a href='#L1528'>1528</a>
<a name='L1529'></a><a href='#L1529'>1529</a>
<a name='L1530'></a><a href='#L1530'>1530</a>
<a name='L1531'></a><a href='#L1531'>1531</a>
<a name='L1532'></a><a href='#L1532'>1532</a>
<a name='L1533'></a><a href='#L1533'>1533</a>
<a name='L1534'></a><a href='#L1534'>1534</a>
<a name='L1535'></a><a href='#L1535'>1535</a>
<a name='L1536'></a><a href='#L1536'>1536</a>
<a name='L1537'></a><a href='#L1537'>1537</a>
<a name='L1538'></a><a href='#L1538'>1538</a>
<a name='L1539'></a><a href='#L1539'>1539</a>
<a name='L1540'></a><a href='#L1540'>1540</a>
<a name='L1541'></a><a href='#L1541'>1541</a>
<a name='L1542'></a><a href='#L1542'>1542</a>
<a name='L1543'></a><a href='#L1543'>1543</a>
<a name='L1544'></a><a href='#L1544'>1544</a>
<a name='L1545'></a><a href='#L1545'>1545</a>
<a name='L1546'></a><a href='#L1546'>1546</a>
<a name='L1547'></a><a href='#L1547'>1547</a>
<a name='L1548'></a><a href='#L1548'>1548</a>
<a name='L1549'></a><a href='#L1549'>1549</a>
<a name='L1550'></a><a href='#L1550'>1550</a>
<a name='L1551'></a><a href='#L1551'>1551</a>
<a name='L1552'></a><a href='#L1552'>1552</a>
<a name='L1553'></a><a href='#L1553'>1553</a>
<a name='L1554'></a><a href='#L1554'>1554</a>
<a name='L1555'></a><a href='#L1555'>1555</a>
<a name='L1556'></a><a href='#L1556'>1556</a>
<a name='L1557'></a><a href='#L1557'>1557</a>
<a name='L1558'></a><a href='#L1558'>1558</a>
<a name='L1559'></a><a href='#L1559'>1559</a>
<a name='L1560'></a><a href='#L1560'>1560</a>
<a name='L1561'></a><a href='#L1561'>1561</a>
<a name='L1562'></a><a href='#L1562'>1562</a>
<a name='L1563'></a><a href='#L1563'>1563</a>
<a name='L1564'></a><a href='#L1564'>1564</a>
<a name='L1565'></a><a href='#L1565'>1565</a>
<a name='L1566'></a><a href='#L1566'>1566</a>
<a name='L1567'></a><a href='#L1567'>1567</a>
<a name='L1568'></a><a href='#L1568'>1568</a>
<a name='L1569'></a><a href='#L1569'>1569</a>
<a name='L1570'></a><a href='#L1570'>1570</a>
<a name='L1571'></a><a href='#L1571'>1571</a>
<a name='L1572'></a><a href='#L1572'>1572</a>
<a name='L1573'></a><a href='#L1573'>1573</a>
<a name='L1574'></a><a href='#L1574'>1574</a>
<a name='L1575'></a><a href='#L1575'>1575</a>
<a name='L1576'></a><a href='#L1576'>1576</a>
<a name='L1577'></a><a href='#L1577'>1577</a>
<a name='L1578'></a><a href='#L1578'>1578</a>
<a name='L1579'></a><a href='#L1579'>1579</a>
<a name='L1580'></a><a href='#L1580'>1580</a>
<a name='L1581'></a><a href='#L1581'>1581</a>
<a name='L1582'></a><a href='#L1582'>1582</a>
<a name='L1583'></a><a href='#L1583'>1583</a>
<a name='L1584'></a><a href='#L1584'>1584</a>
<a name='L1585'></a><a href='#L1585'>1585</a>
<a name='L1586'></a><a href='#L1586'>1586</a>
<a name='L1587'></a><a href='#L1587'>1587</a>
<a name='L1588'></a><a href='#L1588'>1588</a>
<a name='L1589'></a><a href='#L1589'>1589</a>
<a name='L1590'></a><a href='#L1590'>1590</a>
<a name='L1591'></a><a href='#L1591'>1591</a>
<a name='L1592'></a><a href='#L1592'>1592</a>
<a name='L1593'></a><a href='#L1593'>1593</a>
<a name='L1594'></a><a href='#L1594'>1594</a>
<a name='L1595'></a><a href='#L1595'>1595</a>
<a name='L1596'></a><a href='#L1596'>1596</a>
<a name='L1597'></a><a href='#L1597'>1597</a>
<a name='L1598'></a><a href='#L1598'>1598</a>
<a name='L1599'></a><a href='#L1599'>1599</a>
<a name='L1600'></a><a href='#L1600'>1600</a>
<a name='L1601'></a><a href='#L1601'>1601</a>
<a name='L1602'></a><a href='#L1602'>1602</a>
<a name='L1603'></a><a href='#L1603'>1603</a>
<a name='L1604'></a><a href='#L1604'>1604</a>
<a name='L1605'></a><a href='#L1605'>1605</a>
<a name='L1606'></a><a href='#L1606'>1606</a>
<a name='L1607'></a><a href='#L1607'>1607</a>
<a name='L1608'></a><a href='#L1608'>1608</a>
<a name='L1609'></a><a href='#L1609'>1609</a>
<a name='L1610'></a><a href='#L1610'>1610</a>
<a name='L1611'></a><a href='#L1611'>1611</a>
<a name='L1612'></a><a href='#L1612'>1612</a>
<a name='L1613'></a><a href='#L1613'>1613</a>
<a name='L1614'></a><a href='#L1614'>1614</a>
<a name='L1615'></a><a href='#L1615'>1615</a>
<a name='L1616'></a><a href='#L1616'>1616</a>
<a name='L1617'></a><a href='#L1617'>1617</a>
<a name='L1618'></a><a href='#L1618'>1618</a>
<a name='L1619'></a><a href='#L1619'>1619</a>
<a name='L1620'></a><a href='#L1620'>1620</a>
<a name='L1621'></a><a href='#L1621'>1621</a>
<a name='L1622'></a><a href='#L1622'>1622</a>
<a name='L1623'></a><a href='#L1623'>1623</a>
<a name='L1624'></a><a href='#L1624'>1624</a>
<a name='L1625'></a><a href='#L1625'>1625</a>
<a name='L1626'></a><a href='#L1626'>1626</a>
<a name='L1627'></a><a href='#L1627'>1627</a>
<a name='L1628'></a><a href='#L1628'>1628</a>
<a name='L1629'></a><a href='#L1629'>1629</a>
<a name='L1630'></a><a href='#L1630'>1630</a>
<a name='L1631'></a><a href='#L1631'>1631</a>
<a name='L1632'></a><a href='#L1632'>1632</a>
<a name='L1633'></a><a href='#L1633'>1633</a>
<a name='L1634'></a><a href='#L1634'>1634</a>
<a name='L1635'></a><a href='#L1635'>1635</a>
<a name='L1636'></a><a href='#L1636'>1636</a>
<a name='L1637'></a><a href='#L1637'>1637</a>
<a name='L1638'></a><a href='#L1638'>1638</a>
<a name='L1639'></a><a href='#L1639'>1639</a>
<a name='L1640'></a><a href='#L1640'>1640</a>
<a name='L1641'></a><a href='#L1641'>1641</a>
<a name='L1642'></a><a href='#L1642'>1642</a>
<a name='L1643'></a><a href='#L1643'>1643</a>
<a name='L1644'></a><a href='#L1644'>1644</a>
<a name='L1645'></a><a href='#L1645'>1645</a>
<a name='L1646'></a><a href='#L1646'>1646</a>
<a name='L1647'></a><a href='#L1647'>1647</a>
<a name='L1648'></a><a href='#L1648'>1648</a>
<a name='L1649'></a><a href='#L1649'>1649</a>
<a name='L1650'></a><a href='#L1650'>1650</a>
<a name='L1651'></a><a href='#L1651'>1651</a>
<a name='L1652'></a><a href='#L1652'>1652</a>
<a name='L1653'></a><a href='#L1653'>1653</a>
<a name='L1654'></a><a href='#L1654'>1654</a>
<a name='L1655'></a><a href='#L1655'>1655</a>
<a name='L1656'></a><a href='#L1656'>1656</a>
<a name='L1657'></a><a href='#L1657'>1657</a>
<a name='L1658'></a><a href='#L1658'>1658</a>
<a name='L1659'></a><a href='#L1659'>1659</a>
<a name='L1660'></a><a href='#L1660'>1660</a>
<a name='L1661'></a><a href='#L1661'>1661</a>
<a name='L1662'></a><a href='#L1662'>1662</a>
<a name='L1663'></a><a href='#L1663'>1663</a>
<a name='L1664'></a><a href='#L1664'>1664</a>
<a name='L1665'></a><a href='#L1665'>1665</a>
<a name='L1666'></a><a href='#L1666'>1666</a>
<a name='L1667'></a><a href='#L1667'>1667</a>
<a name='L1668'></a><a href='#L1668'>1668</a>
<a name='L1669'></a><a href='#L1669'>1669</a>
<a name='L1670'></a><a href='#L1670'>1670</a>
<a name='L1671'></a><a href='#L1671'>1671</a>
<a name='L1672'></a><a href='#L1672'>1672</a>
<a name='L1673'></a><a href='#L1673'>1673</a>
<a name='L1674'></a><a href='#L1674'>1674</a>
<a name='L1675'></a><a href='#L1675'>1675</a>
<a name='L1676'></a><a href='#L1676'>1676</a>
<a name='L1677'></a><a href='#L1677'>1677</a>
<a name='L1678'></a><a href='#L1678'>1678</a>
<a name='L1679'></a><a href='#L1679'>1679</a>
<a name='L1680'></a><a href='#L1680'>1680</a>
<a name='L1681'></a><a href='#L1681'>1681</a>
<a name='L1682'></a><a href='#L1682'>1682</a>
<a name='L1683'></a><a href='#L1683'>1683</a>
<a name='L1684'></a><a href='#L1684'>1684</a>
<a name='L1685'></a><a href='#L1685'>1685</a>
<a name='L1686'></a><a href='#L1686'>1686</a>
<a name='L1687'></a><a href='#L1687'>1687</a>
<a name='L1688'></a><a href='#L1688'>1688</a>
<a name='L1689'></a><a href='#L1689'>1689</a>
<a name='L1690'></a><a href='#L1690'>1690</a>
<a name='L1691'></a><a href='#L1691'>1691</a>
<a name='L1692'></a><a href='#L1692'>1692</a>
<a name='L1693'></a><a href='#L1693'>1693</a>
<a name='L1694'></a><a href='#L1694'>1694</a>
<a name='L1695'></a><a href='#L1695'>1695</a>
<a name='L1696'></a><a href='#L1696'>1696</a>
<a name='L1697'></a><a href='#L1697'>1697</a>
<a name='L1698'></a><a href='#L1698'>1698</a>
<a name='L1699'></a><a href='#L1699'>1699</a>
<a name='L1700'></a><a href='#L1700'>1700</a>
<a name='L1701'></a><a href='#L1701'>1701</a>
<a name='L1702'></a><a href='#L1702'>1702</a>
<a name='L1703'></a><a href='#L1703'>1703</a>
<a name='L1704'></a><a href='#L1704'>1704</a>
<a name='L1705'></a><a href='#L1705'>1705</a>
<a name='L1706'></a><a href='#L1706'>1706</a>
<a name='L1707'></a><a href='#L1707'>1707</a>
<a name='L1708'></a><a href='#L1708'>1708</a>
<a name='L1709'></a><a href='#L1709'>1709</a>
<a name='L1710'></a><a href='#L1710'>1710</a>
<a name='L1711'></a><a href='#L1711'>1711</a>
<a name='L1712'></a><a href='#L1712'>1712</a>
<a name='L1713'></a><a href='#L1713'>1713</a>
<a name='L1714'></a><a href='#L1714'>1714</a>
<a name='L1715'></a><a href='#L1715'>1715</a>
<a name='L1716'></a><a href='#L1716'>1716</a>
<a name='L1717'></a><a href='#L1717'>1717</a>
<a name='L1718'></a><a href='#L1718'>1718</a>
<a name='L1719'></a><a href='#L1719'>1719</a>
<a name='L1720'></a><a href='#L1720'>1720</a>
<a name='L1721'></a><a href='#L1721'>1721</a>
<a name='L1722'></a><a href='#L1722'>1722</a>
<a name='L1723'></a><a href='#L1723'>1723</a>
<a name='L1724'></a><a href='#L1724'>1724</a>
<a name='L1725'></a><a href='#L1725'>1725</a>
<a name='L1726'></a><a href='#L1726'>1726</a>
<a name='L1727'></a><a href='#L1727'>1727</a>
<a name='L1728'></a><a href='#L1728'>1728</a>
<a name='L1729'></a><a href='#L1729'>1729</a>
<a name='L1730'></a><a href='#L1730'>1730</a>
<a name='L1731'></a><a href='#L1731'>1731</a>
<a name='L1732'></a><a href='#L1732'>1732</a>
<a name='L1733'></a><a href='#L1733'>1733</a>
<a name='L1734'></a><a href='#L1734'>1734</a>
<a name='L1735'></a><a href='#L1735'>1735</a>
<a name='L1736'></a><a href='#L1736'>1736</a>
<a name='L1737'></a><a href='#L1737'>1737</a>
<a name='L1738'></a><a href='#L1738'>1738</a>
<a name='L1739'></a><a href='#L1739'>1739</a>
<a name='L1740'></a><a href='#L1740'>1740</a>
<a name='L1741'></a><a href='#L1741'>1741</a>
<a name='L1742'></a><a href='#L1742'>1742</a>
<a name='L1743'></a><a href='#L1743'>1743</a>
<a name='L1744'></a><a href='#L1744'>1744</a>
<a name='L1745'></a><a href='#L1745'>1745</a>
<a name='L1746'></a><a href='#L1746'>1746</a>
<a name='L1747'></a><a href='#L1747'>1747</a>
<a name='L1748'></a><a href='#L1748'>1748</a>
<a name='L1749'></a><a href='#L1749'>1749</a>
<a name='L1750'></a><a href='#L1750'>1750</a>
<a name='L1751'></a><a href='#L1751'>1751</a>
<a name='L1752'></a><a href='#L1752'>1752</a>
<a name='L1753'></a><a href='#L1753'>1753</a>
<a name='L1754'></a><a href='#L1754'>1754</a>
<a name='L1755'></a><a href='#L1755'>1755</a>
<a name='L1756'></a><a href='#L1756'>1756</a>
<a name='L1757'></a><a href='#L1757'>1757</a>
<a name='L1758'></a><a href='#L1758'>1758</a>
<a name='L1759'></a><a href='#L1759'>1759</a>
<a name='L1760'></a><a href='#L1760'>1760</a>
<a name='L1761'></a><a href='#L1761'>1761</a>
<a name='L1762'></a><a href='#L1762'>1762</a>
<a name='L1763'></a><a href='#L1763'>1763</a>
<a name='L1764'></a><a href='#L1764'>1764</a>
<a name='L1765'></a><a href='#L1765'>1765</a>
<a name='L1766'></a><a href='#L1766'>1766</a>
<a name='L1767'></a><a href='#L1767'>1767</a>
<a name='L1768'></a><a href='#L1768'>1768</a>
<a name='L1769'></a><a href='#L1769'>1769</a>
<a name='L1770'></a><a href='#L1770'>1770</a>
<a name='L1771'></a><a href='#L1771'>1771</a>
<a name='L1772'></a><a href='#L1772'>1772</a>
<a name='L1773'></a><a href='#L1773'>1773</a>
<a name='L1774'></a><a href='#L1774'>1774</a>
<a name='L1775'></a><a href='#L1775'>1775</a>
<a name='L1776'></a><a href='#L1776'>1776</a>
<a name='L1777'></a><a href='#L1777'>1777</a>
<a name='L1778'></a><a href='#L1778'>1778</a>
<a name='L1779'></a><a href='#L1779'>1779</a>
<a name='L1780'></a><a href='#L1780'>1780</a>
<a name='L1781'></a><a href='#L1781'>1781</a>
<a name='L1782'></a><a href='#L1782'>1782</a>
<a name='L1783'></a><a href='#L1783'>1783</a>
<a name='L1784'></a><a href='#L1784'>1784</a>
<a name='L1785'></a><a href='#L1785'>1785</a>
<a name='L1786'></a><a href='#L1786'>1786</a>
<a name='L1787'></a><a href='#L1787'>1787</a>
<a name='L1788'></a><a href='#L1788'>1788</a>
<a name='L1789'></a><a href='#L1789'>1789</a>
<a name='L1790'></a><a href='#L1790'>1790</a>
<a name='L1791'></a><a href='#L1791'>1791</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import React, { useEffect, useState, useMemo, useRef, useCallback } from 'react';
import Drawer from '@albertsons/uds/molecule/Drawer';
import Button from '@albertsons/uds/molecule/Button';
import Radio from '@albertsons/uds/molecule/Radio';
import TextField from '@albertsons/uds/molecule/TextField';
import TextArea from '@albertsons/uds/molecule/TextArea';
import AutoComplete from '@albertsons/uds/molecule/AutoComplete';
import Alert from '@albertsons/uds/molecule/Alert';
import Link from '@albertsons/uds/molecule/Link';
import { Divider } from '@mui/material';
import { Info, TriangleAlert } from 'lucide-react';
import { useFormik, useFormikContext } from 'formik';
import './bottomdropdowm.scss';
import './editForecast.scss'
import WeekSelection from './weekSelection';
import { EditForecastAdjustmentProps, FormValues } from './types';
import { forecastAdjustmentReasons } from './constants';
import { useSelectorWrap } from '../../rtk/rtk-utilities';
import { FormFieldNames, Week, ADJ_SUB_ROW, Adjustment, SaveAdjustmentResponse } from '../../interfaces/edit-forecast-adjustments';
import SkeletonLoader from '@albertsons/uds/molecule/SkeletonLoader';
import { calculateSalesPublicPcnt, safeScrollIntoView, scrollToFirstError } from './forecastCalculations';
import { useEditForecastBffBody } from './forecastCalculations'
import { useSaveAdjustmentEditsMutation } from '../../server/Api/menfptCategoryAPI';
import { useDispatch } from 'react-redux';
import clsx from 'clsx';
import HistoryDrawer from '../../features/historyDrawer';
import { createAdjustmentApiBody, getFiscalWeekNumber, saveAdjustment } from './editForecastHelper';
import Spinner from '@albertsons/uds/molecule/Spinner';
import { Tooltip } from '@albertsons/uds/molecule/Tooltip';
import { useExtractBannerId } from '../../util/filterUtils';
&nbsp;
const DEVIATION_WARNING_MSG = <span class="cstat-no" title="statement not covered" >'+/-10% deviation to prior merchant forecast';</span>
// salesPublicPcnt: totalValue[salesPublic] / (totalBaseLineValue[salesPublic])
// bookgrossProfit calculation: (totalBaseLineValue[grossProfit] / totalBaseLineValue[salesPublic]+1) * totalValue[salesPublic]
// markdown : ( (totalBaseLineValue[marksDown] / totalBaseLineValue[salesPublic]) * totalValue[salesPublic] ) - totalBaseLineValue[marksDown]
&nbsp;
// grossProfit pcnt is: grossProfit / (salesPublic + baseLine[salesPublic])
&nbsp;
const DECIMAL_POINTS = <span class="cstat-no" title="statement not covered" >0;</span>
const DECIMAL_POINTS_PCT = <span class="cstat-no" title="statement not covered" >2;</span>
&nbsp;
const TEXT_AREA_MAX_CHAR = <span class="cstat-no" title="statement not covered" >300;</span>
&nbsp;
const numberFormatter = <span class="cstat-no" title="statement not covered" >new Intl.NumberFormat('en-US', {</span>
  style: 'decimal',
  useGrouping: true,
  minimumFractionDigits: DECIMAL_POINTS,
  maximumFractionDigits: DECIMAL_POINTS
});
&nbsp;
const pctFormatter = <span class="cstat-no" title="statement not covered" >{</span>
  format: <span class="fstat-no" title="function not covered" >(v</span>alue: number) =&gt; {
    // Truncate to 2 decimal places without rounding
    const truncated = <span class="cstat-no" title="statement not covered" >Math.floor(value * 100) / 100;</span>
<span class="cstat-no" title="statement not covered" >    return truncated.toFixed(2).replace(/\.?0+$/, '');</span>
  }
};
&nbsp;
&nbsp;
const formToActualFields = <span class="cstat-no" title="statement not covered" >{</span>
  grossProfit: 'line5BookGrossProfitNbr',
  salesPublicPct: 'line1PublicToSalesNbr',
  salesPublic: 'line1PublicToSalesNbr',
  marksDown: 'line5MarkDownsNbr',
  totalShrink: 'line5ShrinkNbr',
  suppliesPackaging: 'line6SuppliesPackagingNbr',
  allowances: 'line7RetailsAllowancesNbr',
  selling: 'line7RetailsSellingAllowancesNbr',
  nonSelling: 'line7RetailsNonSellingAllowancesNbr'
}
&nbsp;
const selectors: Record&lt;string, string&gt; = <span class="cstat-no" title="statement not covered" >{</span>
  selectedWeeks: 'div.text-left.font-bold.leading-6.block.break-words',
  metrics: '.edit-notifications .text-left.font-bold',
  adjustmentReason: '.adjustment-error', // You may need to add this attribute
  comment: 'span.block.truncate[title=""]'
};
&nbsp;
type Allowance = {
  selling: number | null;
  nonSelling: number | null;
  totalAllowances: number | null;
}
&nbsp;
const fieldOrder: string[] = <span class="cstat-no" title="statement not covered" >['selectedWeeks', 'metrics', 'adjustmentReason', 'comment'];</span>
&nbsp;
const ForecastEdit: React.FC&lt;EditForecastAdjustmentProps&gt; = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >({</span></span>
  isOpen,
  setOpen,
  selectedEditWeek,
}) =&gt; {
&nbsp;
  const [activeWeekSubmit, setActiveWeekSubmit] = <span class="cstat-no" title="statement not covered" >useState(false)</span>
  const [warnings, setWarnings] = <span class="cstat-no" title="statement not covered" >useState&lt;any&gt;({})</span>
  const [isSaveApiLoading, setIsSaveApiLoading] = <span class="cstat-no" title="statement not covered" >useState&lt;boolean&gt;(false);</span>
&nbsp;
  const [weeksCalculated, setWeeksCalculated] = <span class="cstat-no" title="statement not covered" >useState&lt;Week[]&gt;([]);</span>
&nbsp;
  const [saveAdjustmentResponseState, setSaveAdjustmentResponseState] = <span class="cstat-no" title="statement not covered" >useState&lt;any&gt;();</span>
&nbsp;
  const { data: adjustmentWorksheetDataSlice } = <span class="cstat-no" title="statement not covered" >useSelectorWrap('adjustmentWorkSheetFilter_rn') || {};</span> //{data:slice_info};
  const adjustmentWorksheetData = <span class="cstat-no" title="statement not covered" >new Map(Object.entries(adjustmentWorksheetDataSlice));</span> // deserialize to Map object
&nbsp;
  const { data: appliedFilters } = <span class="cstat-no" title="statement not covered" >useSelectorWrap('appliedFilter_rn');</span>
  const { data: userInfo } = <span class="cstat-no" title="statement not covered" >useSelectorWrap('userInfo_rn');</span>
&nbsp;
  const [isHistoryOpen, setIsHistoryOpen] = <span class="cstat-no" title="statement not covered" >useState(false);</span>
  const [historyPosition, setHistoryPosition] = <span class="cstat-no" title="statement not covered" >useState('left');</span>
&nbsp;
const [editStates, setEditStates] = <span class="cstat-no" title="statement not covered" >useState({});</span>
&nbsp;
const handleInputFocus = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(n</span>ame, value) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  setEditStates(<span class="fstat-no" title="function not covered" >(p</span>rev) =&gt; (<span class="cstat-no" title="statement not covered" >{</span></span>
    ...prev,
    [name]: {
      ...prev[name],
      previousValue: value,
      editing: true,
    },
  }));
};
&nbsp;
const handleInputBlur = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(n</span>ame) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  setEditStates(<span class="fstat-no" title="function not covered" >(p</span>rev) =&gt; (<span class="cstat-no" title="statement not covered" >{</span></span>
    ...prev,
    [name]: {
      ...prev[name],
      editing: false,
    },
  }));
};
&nbsp;
const handleRestoreValue = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(n</span>ame, setter) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  setEditStates(<span class="fstat-no" title="function not covered" >(p</span>rev) =&gt; (<span class="cstat-no" title="statement not covered" >{</span></span>
    ...prev,
    [name]: {
      ...prev[name],
      editing: true,
    },
  }));
<span class="cstat-no" title="statement not covered" >  setter({</span>
    target: {
      value: editStates[name]?.previousValue ?? '',
      id: name,
    },
  });
};
&nbsp;
&nbsp;
  // Replace historyModalOpen usage with local state:
  const openHistoryDrawer = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(o</span>pen: boolean, position: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    setIsHistoryOpen(open);</span>
<span class="cstat-no" title="statement not covered" >    setHistoryPosition(position);</span>
  };
&nbsp;
  const { setEditForecastBffBody } = <span class="cstat-no" title="statement not covered" >useEditForecastBffBody();</span>
&nbsp;
&nbsp;
  const adjustmentErrorRef = <span class="cstat-no" title="statement not covered" >useRef&lt;HTMLDivElement | null&gt;(null);</span>
&nbsp;
  const [saveAdjustmentEdits] = <span class="cstat-no" title="statement not covered" >useSaveAdjustmentEditsMutation();</span>
&nbsp;
  const [totalBaselineValue, setTotalBaselineValue] = <span class="cstat-no" title="statement not covered" >useState&lt;FormFieldNames&gt;({</span>
    'salesPublic': 0,
    'grossProfit': 0,
    'marksDown': 0,
    'totalShrink': 0,
    'suppliesPackaging': 0,
    'allowances': 0,
    'selling': 0,
    'nonSelling': 0
  });
&nbsp;
  const [totalLastYearActual, setTotalLastYearActual] = <span class="cstat-no" title="statement not covered" >useState&lt;FormFieldNames&gt;({</span>
    'salesPublic': 0,
    'grossProfit': 0,
    'marksDown': 0,
    'totalShrink': 0,
    'suppliesPackaging': 0,
    'allowances': 0,
    'selling': 0,
    'nonSelling': 0
  })
&nbsp;
  const [totalValue, setTotalValue] = <span class="cstat-no" title="statement not covered" >useState&lt;FormFieldNames&gt;({</span>
    'salesPublic': 0,
    'grossProfit': 0,
    'marksDown': 0,
    'totalShrink': 0,
    'suppliesPackaging': 0,
    'allowances': 0,
    'selling': 0,
    'nonSelling': 0
  })
&nbsp;
  const dispatch = <span class="cstat-no" title="statement not covered" >useDispatch();</span>
&nbsp;
  const bannerId = <span class="cstat-no" title="statement not covered" >useExtractBannerId();</span>
&nbsp;
  const formFields = <span class="cstat-no" title="statement not covered" >[</span>
    {
      label: (&lt;span className="text-[#5A697B] flex items-center"&gt;Line 8 &lt;Info className="text-[#5A697B] ml-3" size={16} /&gt;&lt;/span&gt;),
      name: '',
    },
    {
      label: 'DR 08 Real GP before other Rev-Sales',
      name: 'realGrossProfitNbr',
      inputType: 'readonly',
      showWarning: true
    },
    {
      label: (&lt;span className="text-[#5A697B] flex items-center"&gt;Line 1 &lt;Info className="text-[#5A697B] ml-3" size={16} /&gt;&lt;/span&gt;),
      name: '',
    },
    {
      label: 'Sales to Public',
      name: 'salesPublic',
      inputType: 'dollar',
      showWarning: true,
    },
    {
      label: (&lt;span className="text-[#5A697B] flex items-center"&gt;Line 5 sub-lines &lt;Info className="text-[#5A697B] ml-3" size={16} /&gt;&lt;/span&gt;),
      name: '',
    },
    {
      label: 'Book gross profit',
      name: 'grossProfit',
      inputType: 'text',
      showWarning: true
    },
    {
      label: 'Markdowns',
      name: 'marksDown',
      inputType: 'text',
      showWarning: true
    },
    {
      label: 'Total Shrink',
      name: 'totalShrink',
      inputType: 'text',
      showWarning: true
    },
    {
      label: (&lt;span className="text-[#5A697B] flex items-center"&gt;Line 6 &lt;Info className="text-[#5A697B] ml-3" size={16} /&gt;&lt;/span&gt;),
      name: '',
    },
    {
      label: 'Supplies Packaging',
      name: 'suppliesPackaging',
      inputType: 'noPct',
      showWarning: true
    },
    {
      label: (&lt;span className="text-[#5A697B] flex items-center"&gt;Line 7 &lt;Info className="text-[#5A697B] ml-3" size={16} /&gt;&lt;/span&gt;),
      name: '',
    },
    {
      label: 'Allowance',
      name: 'allowances',
      inputType: 'noPct',
      showWarning: true
    },
    {
      label: 'Selling',
      name: 'selling',
      inputType: 'noPct',
      subField: true
    },
    {
      label: 'Non Selling',
      name: 'nonSelling',
      inputType: 'noPct',
      subField: true
    }
  ]
  const ArrowSymbol = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(c</span>olor: string) =&gt; (</span>
<span class="cstat-no" title="statement not covered" >  &lt;svg</span>
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  &gt;
    &lt;path
      d="M5.99996 9.33073L2.66663 5.9974M2.66663 5.9974L5.99996 2.66406M2.66663 5.9974H9.66663C10.1481 5.9974 10.6249 6.09224 11.0698 6.2765C11.5147 6.46077 11.9189 6.73086 12.2594 7.07134C12.5998 7.41182 12.8699 7.81603 13.0542 8.26089C13.2385 8.70575 13.3333 9.18255 13.3333 9.66406C13.3333 10.1456 13.2385 10.6224 13.0542 11.0672C12.8699 11.5121 12.5998 11.9163 12.2594 12.2568C11.9189 12.5973 11.5147 12.8674 11.0698 13.0516C10.6249 13.2359 10.1481 13.3307 9.66663 13.3307H7.33329"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    /&gt;
  &lt;/svg&gt;
);
&nbsp;
  const transformForecastData = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(d</span>ataMap, subRow: string, projectionRow: string) =&gt; {</span>
    const forecastData = <span class="cstat-no" title="statement not covered" >{}</span>
&nbsp;
    /** Iterate through each week in the map */
    let weekIndex = <span class="cstat-no" title="statement not covered" >1;</span> //start numbering from 1
&nbsp;
<span class="cstat-no" title="statement not covered" >    dataMap &amp;&amp; dataMap.forEach(<span class="fstat-no" title="function not covered" >(w</span>eekData, weekKey) =&gt; {</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (/^Weeks\d+$/.test(weekKey)) {</span>
        /** Find the forecast entry */
        const forecastEntry = <span class="cstat-no" title="statement not covered" >weekData.find(<span class="fstat-no" title="function not covered" >en</span>try =&gt; <span class="cstat-no" title="statement not covered" >entry.subRow === subRow)</span> || weekData.find(<span class="fstat-no" title="function not covered" >en</span>try =&gt; <span class="cstat-no" title="statement not covered" >entry.subRow === projectionRow)</span>;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (forecastEntry) {</span>
<span class="cstat-no" title="statement not covered" >          forecastData[String(forecastEntry?.fiscalWeekNbr)?.slice(-2)] = {</span>
            salesPublic: forecastEntry.line1PublicToSalesNbr,
            grossProfit: forecastEntry.line5BookGrossProfitNbr,
            grossProfitPct: forecastEntry.line5BookGrossProfitPct,
            marksDown: forecastEntry.line5MarkDownsNbr,
            marksDownPct: forecastEntry.line5MarkDownsPct,
            totalShrink: forecastEntry.line5ShrinkNbr,
            totalShrinkPct: forecastEntry.line5ShrinkPct,
            suppliesPackaging: forecastEntry.line6SuppliesPackagingNbr,
            allowances: forecastEntry.line7RetailsAllowancesNbr,
            selling: forecastEntry.line7RetailsSellingAllowancesNbr,
            nonSelling: forecastEntry.line7RetailsNonSellingAllowancesNbr
          }
        }
<span class="cstat-no" title="statement not covered" >        weekIndex++; </span>//Increment numeric week values
      }
    })
<span class="cstat-no" title="statement not covered" >    return forecastData</span>
&nbsp;
  }
&nbsp;
&nbsp;
  const validate = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(v</span>alues, isSubmitting) =&gt; {</span>
    const errors: any = <span class="cstat-no" title="statement not covered" >{};</span> /** To restrict the form from submitting */
    const warnings: any = <span class="cstat-no" title="statement not covered" >{};</span> /** Displays warning message for all metric fields, saving adjustment is allowed with warnings */
&nbsp;
    const forecastData = <span class="cstat-no" title="statement not covered" >transformForecastData(adjustmentWorksheetData, ADJ_SUB_ROW.MERCH_FORECAST, ADJ_SUB_ROW.PROJECTION);</span>
&nbsp;
    // console.log(forecastData)
&nbsp;
    /**** Fields to be validated for metrics warning *****/
    const fieldsToCheck = <span class="cstat-no" title="statement not covered" >[</span>
      'salesPublic',
      'grossProfit',
      'marksDown',
      'totalShrink',
      'suppliesPackaging',
      'allowances',
      'selling',
      'nonSelling',
      'realGrossProfitNbr'
    ];
&nbsp;
    /** Select at least one week for both 'multiple week editing' or 'week wise editing' */
<span class="cstat-no" title="statement not covered" >    if (!values.selectedWeeks || values.selectedWeeks.length === 0) {</span>
<span class="cstat-no" title="statement not covered" >      errors.selectedWeeks = 'Please select at least one week'</span>
    }
&nbsp;
    /******************* Validate for respective weeks *********************************/
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (values.selectedRadio === 'singleweek') {</span>
      const weeksToValidate = <span class="cstat-no" title="statement not covered" >isSubmitting ? Object.keys({ ...values.weekData, [values.selectedWeeks[0]]: values.weekData[values.selectedWeeks[0]] || {} })</span>
        : Object.keys(values.weekData)
      // const weeksToValidate = Object.keys({...values.weekData, [values.selectedWeeks[0]]: values.weekData[values.selectedWeeks[0]] || {}})
&nbsp;
<span class="cstat-no" title="statement not covered" >      weeksToValidate.forEach(<span class="fstat-no" title="function not covered" >(w</span>eek) =&gt; {</span>
        const weekData = <span class="cstat-no" title="statement not covered" >values.weekData?.[week] || {};</span>
        const weekErrors: any = <span class="cstat-no" title="statement not covered" >{};</span>
        const weekWarnings: any = <span class="cstat-no" title="statement not covered" >{};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if(weekData?.touched?.size &gt; 0) {</span>
          /**** Validating for metrics, any of the above fields should have valid data *****/
        const anyFieldFilled = <span class="cstat-no" title="statement not covered" >fieldsToCheck.some(<span class="fstat-no" title="function not covered" >(f</span>ield) =&gt; {</span>
          const val = <span class="cstat-no" title="statement not covered" >weekData[field];</span>
<span class="cstat-no" title="statement not covered" >          return val !== undefined &amp;&amp; val !== '' &amp;&amp; val !== null;</span>
        })
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (!anyFieldFilled) {</span>
<span class="cstat-no" title="statement not covered" >          weekErrors.metrics = 'Please adjust at least one of the metrics below'</span>
        }
&nbsp;
        /**** Additional check for AdjustmentReason and Comments Fileds  *****/
<span class="cstat-no" title="statement not covered" >        if (!weekData.adjustmentReason || !Object.values(weekData.adjustmentReason).length) {</span>
<span class="cstat-no" title="statement not covered" >          weekErrors.adjustmentReason = `Please select an adjustment reason`</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (!weekData.comment || weekData.comment === '') {</span>
<span class="cstat-no" title="statement not covered" >          weekErrors.comment = `Please add comment`</span>
        } else <span class="cstat-no" title="statement not covered" >if (isCommentValid(weekData.comment, TEXT_AREA_MAX_CHAR)) {</span>
<span class="cstat-no" title="statement not covered" >          weekErrors.comment = 'Comment Exceeds Max Length'</span>
        }
&nbsp;
&nbsp;
        const totalBaseline = <span class="cstat-no" title="statement not covered" >{</span>
          'salesPublic': 0,
          'grossProfit': 0,
          'marksDown': 0,
          'totalShrink': 0,
          'suppliesPackaging': 0,
          'allowances': 0,
          'selling': 0,
          'nonSelling': 0
        }
        const updatedTotalValue = <span class="cstat-no" title="statement not covered" >{</span>
          'salesPublic': 0,
          'grossProfit': 0,
          'marksDown': 0,
          'totalShrink': 0,
          'suppliesPackaging': 0,
          'allowances': 0,
          'selling': 0,
          'nonSelling': 0
&nbsp;
        }
&nbsp;
        const baselineValues = <span class="cstat-no" title="statement not covered" >forecastData?.[week] || {};</span>
<span class="cstat-no" title="statement not covered" >        fieldsToCheck.forEach(<span class="fstat-no" title="function not covered" >(f</span>ield) =&gt; {</span>
&nbsp;
          let totalValue = <span class="cstat-no" title="statement not covered" >{}</span>
<span class="cstat-no" title="statement not covered" >          if (field === 'salesPublic') {</span>
<span class="cstat-no" title="statement not covered" >            totalValue = { ...baselineValues }</span>
<span class="cstat-no" title="statement not covered" >            totalValue[field] += Number(("" +weekData[field])?.replace(/,/g, ''))</span>
          } else <span class="cstat-no" title="statement not covered" >if(field === 'realGrossProfitNbr') {</span>
            const enteredValue = <span class="cstat-no" title="statement not covered" >getLine8(weekData);</span>
            const baseline = <span class="cstat-no" title="statement not covered" >getLine8(baselineValues);</span>
<span class="cstat-no" title="statement not covered" >            if (baseline !== undefined &amp;&amp; enteredValue !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >              totalValue[field] = enteredValue;</span>
<span class="cstat-no" title="statement not covered" >              baselineValues[field] = baseline;</span>
            }
          } else {
            const baseline = <span class="cstat-no" title="statement not covered" >forecastData?.[week]?.[field];</span>
            const enteredValue = <span class="cstat-no" title="statement not covered" >Number(("" + weekData[field])?.replace(/,/g, ''))</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (baseline !== undefined &amp;&amp; weekData[field] !== '') {</span>
<span class="cstat-no" title="statement not covered" >              totalValue[field] = enteredValue;</span>
            }
          }
&nbsp;
          const totalBaseline = <span class="cstat-no" title="statement not covered" >baselineValues[field]</span>
          const totalEnteredValue = <span class="cstat-no" title="statement not covered" >totalValue[field]</span>
&nbsp;
          /**The below check is to handle the negative number scenarios
           * For negative numbers product with 0.9 becomes more negative
           * So the upper bound and the lower bound flips
           */
          const lowerBound = <span class="cstat-no" title="statement not covered" >(totalBaseline &gt;= 0) ? totalBaseline * 0.9 : totalBaseline * 1.1;</span>
          const upperBound = <span class="cstat-no" title="statement not covered" >(totalBaseline &gt;= 0) ? totalBaseline * 1.1 : totalBaseline * 0.9;</span>
<span class="cstat-no" title="statement not covered" >          if (totalEnteredValue &lt; lowerBound || totalEnteredValue &gt; upperBound) {</span>
<span class="cstat-no" title="statement not covered" >            weekWarnings[field] = DEVIATION_WARNING_MSG</span>
          }
        })
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (Object.keys(weekErrors).length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >          if (!errors.weekData) <span class="cstat-no" title="statement not covered" >errors.weekData = {}</span></span>
<span class="cstat-no" title="statement not covered" >          errors.weekData[week] = weekErrors</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (Object.keys(weekWarnings).length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >          if (!warnings.weekData) <span class="cstat-no" title="statement not covered" >warnings.weekData = {}</span></span>
<span class="cstat-no" title="statement not covered" >          warnings.weekData[week] = weekWarnings</span>
        }
&nbsp;
        }
      })
<span class="cstat-no" title="statement not covered" >      if(isSubmitting) {</span>
        const _values =  <span class="cstat-no" title="statement not covered" >values.weekData || {};</span>
        let anyWeekEdited = <span class="cstat-no" title="statement not covered" >false;</span>
<span class="cstat-no" title="statement not covered" >        Object.keys(_values).forEach(<span class="fstat-no" title="function not covered" >(w</span>eek) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >          if(_values[week]?.touched?.size &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >            anyWeekEdited = true;</span>
          }
        });
<span class="cstat-no" title="statement not covered" >        if(!anyWeekEdited &amp;&amp;  Object.keys(errors).length &gt;0) {</span>
<span class="cstat-no" title="statement not covered" >          errors.weekData = {}</span>
<span class="cstat-no" title="statement not covered" >          errors.weekData[values.selectedWeeks[0]] = "Please adjust at least one of the metrics below";</span>
        }
      }
    }
    /******************* Validate for all selected weeks - one time validation *********************************/
    else <span class="cstat-no" title="statement not covered" >if (values.selectedRadio === 'groupweeks') {</span>
&nbsp;
      /**** Validating for metrics, any of the above fields should have valid data *****/
      const anyFieldFilled = <span class="cstat-no" title="statement not covered" >fieldsToCheck.some(<span class="fstat-no" title="function not covered" >(f</span>ield) =&gt; {</span>
        const val = <span class="cstat-no" title="statement not covered" >values[field];</span>
<span class="cstat-no" title="statement not covered" >        return val !== undefined &amp;&amp; val !== '' &amp;&amp; val !== null;</span>
      })
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (!anyFieldFilled) {</span>
<span class="cstat-no" title="statement not covered" >        errors.metrics = 'Please adjust at least one of the metrics below'</span>
      }
&nbsp;
      /**** Additional check for AdjustmentReason and Comments Fileds  *****/
<span class="cstat-no" title="statement not covered" >      if (!Object.values(values.adjustmentReason).length) {</span>
<span class="cstat-no" title="statement not covered" >        errors.adjustmentReason = 'Please select an adjustment reason'</span>
      }
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (!values.comment) {</span>
<span class="cstat-no" title="statement not covered" >        errors.comment = 'Please add comment'</span>
      } else <span class="cstat-no" title="statement not covered" >if (isCommentValid(values.comment, TEXT_AREA_MAX_CHAR)) {</span>
<span class="cstat-no" title="statement not covered" >        errors.comment = 'Comment Exceeds Max Length';</span>
      }
&nbsp;
&nbsp;
&nbsp;
<span class="cstat-no" title="statement not covered" >      fieldsToCheck.forEach(<span class="fstat-no" title="function not covered" >(f</span>ield) =&gt; {</span>
&nbsp;
        let totalValue = <span class="cstat-no" title="statement not covered" >{}</span>
<span class="cstat-no" title="statement not covered" >        if (field === 'salesPublic') {</span>
<span class="cstat-no" title="statement not covered" >          totalValue = { ...totalBaselineValue }</span>
<span class="cstat-no" title="statement not covered" >          formik.values.selectedWeeks.forEach(<span class="fstat-no" title="function not covered" >()</span> =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            totalValue[field] += Number(values[field]?.replace(/,/g, ''))</span>
          })
        } else {
<span class="cstat-no" title="statement not covered" >          values.selectedWeeks.forEach(<span class="fstat-no" title="function not covered" >(w</span>eek) =&gt; {</span>
            const baseline = <span class="cstat-no" title="statement not covered" >forecastData?.[week]?.[field];</span>
            const enteredValue = <span class="cstat-no" title="statement not covered" >Number(values[field]?.replace(/,/g, ''))</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (baseline !== undefined &amp;&amp; values[field] !== '') {</span>
<span class="cstat-no" title="statement not covered" >              totalValue[field] = enteredValue;</span>
&nbsp;
            }
          })
        }
&nbsp;
        const totalBaseline = <span class="cstat-no" title="statement not covered" >totalBaselineValue[field]</span>
        const totalEnteredValue = <span class="cstat-no" title="statement not covered" >totalValue[field]</span>
&nbsp;
        /**The below check is to handle the negative number scenarios
         * For negative numbers product with 0.9 becomes more negative
         * So the upper bound and the lower bound flips
         */
        const lowerBound = <span class="cstat-no" title="statement not covered" >(totalBaseline &gt;= 0) ? totalBaseline * 0.9 : totalBaseline * 1.1;</span>
        const upperBound = <span class="cstat-no" title="statement not covered" >(totalBaseline &gt;= 0) ? totalBaseline * 1.1 : totalBaseline * 0.9;</span>
<span class="cstat-no" title="statement not covered" >        if (totalEnteredValue &lt; lowerBound || totalEnteredValue &gt; upperBound) {</span>
<span class="cstat-no" title="statement not covered" >          warnings[field] = DEVIATION_WARNING_MSG</span>
        }
&nbsp;
      })
    }
<span class="cstat-no" title="statement not covered" >    setWarnings(warnings)</span>
<span class="cstat-no" title="statement not covered" >    return errors</span>
  }
&nbsp;
  const initialValues: FormValues = <span class="cstat-no" title="statement not covered" >{</span>
    salesPublic: '',
    salesPublicPct: '',
    grossProfit: '',
    marksDown: '',
    totalShrink: '',
    suppliesPackaging: '',
    allowances: '',
    selectedWeeks: [],
    adjustmentReason: {},
    comment: '',
    selectedRadio: 'singleweek',
    metrics: '',
    weekData: {}, /**For week-specific forms */
    errors: {},
    touchedWeeks: {},
    grossProfitPct: '',
    marksDownPct: '',
    totalShrinkPct: '',
    allowancesPct: '',
    selling: '',
    nonSelling: '',
    sellingPct: '',
    nonSellingPct: '',
    touched: new Set&lt;string&gt;(),
  }
&nbsp;
  const isWeekUpdated = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(_</span>values) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if(_values?.weekData?.[_values.selectedWeeks[0]]?.touched?.size &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >      return true;</span>
    }
  }
&nbsp;
  const formik = <span class="cstat-no" title="statement not covered" >useFormik&lt;FormValues&gt;({</span>
    initialValues,
    validate: <span class="fstat-no" title="function not covered" >(v</span>alues) =&gt; <span class="cstat-no" title="statement not covered" >validate(values, false),</span>
    onSubmit: <span class="fstat-no" title="function not covered" >(v</span>alues) =&gt; {
<span class="cstat-no" title="statement not covered" >      setIsSaveApiLoading(true);</span>
      const apiBody: Adjustment = <span class="cstat-no" title="statement not covered" >createAdjustmentApiBody(appliedFilters, userInfo, weeksCalculated, bannerId);</span>
<span class="cstat-no" title="statement not covered" >      setEditForecastBffBody(apiBody);</span>
<span class="cstat-no" title="statement not covered" >      saveAdjustmentValue(apiBody);</span>
    },
  });
&nbsp;
&nbsp;
&nbsp;
<span class="cstat-no" title="statement not covered" >  useEffect(<span class="fstat-no" title="function not covered" >()</span> =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (isOpen) {</span>
      // helps with auto scroll once you fix an error
      const selectedWeek = <span class="cstat-no" title="statement not covered" >formik.values.selectedWeeks[0]</span>
      const error = <span class="cstat-no" title="statement not covered" >formik.values.selectedRadio === 'singleweek' ? formik.errors.weekData?.[selectedWeek] || formik.errors : formik.errors</span>
<span class="cstat-no" title="statement not covered" >      if (Object.keys(error || {}).length &gt; 0) {</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        scrollToFirstError(error, selectors, fieldOrder);</span>
      }
    }
  }, [formik.errors, formik.errors.weekData, formik.errors, formik?.isSubmitting, formik.values.selectedWeeks]);
&nbsp;
<span class="cstat-no" title="statement not covered" >useEffect(<span class="fstat-no" title="function not covered" >()</span> =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  if (isOpen &amp;&amp; selectedEditWeek) {</span>
    const weekNbr = <span class="cstat-no" title="statement not covered" >Number(String(selectedEditWeek).slice(-2));</span>
<span class="cstat-no" title="statement not covered" >    formik.setFieldValue('selectedWeeks', [weekNbr]);</span>
  }
}, [isOpen, selectedEditWeek]);
&nbsp;
  const recalculateSelectedWeekPayload = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(s</span>alesPublic: any = <span class="branch-0 cbranch-no" title="branch not covered" >undefined,</span> _touched: any = <span class="branch-0 cbranch-no" title="branch not covered" >undefined)</span>: Week[] =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (formik.values.selectedRadio === 'groupweeks') {</span>
&nbsp;
      const values = <span class="cstat-no" title="statement not covered" >formik.values</span>
      const weeks: Week[] = <span class="cstat-no" title="statement not covered" >values.selectedWeeks.map(<span class="fstat-no" title="function not covered" >(w</span>eek): Week =&gt; {</span>
        // debugger;
        const formattedWeekKey = <span class="cstat-no" title="statement not covered" >`Weeks${week.toString().padStart(2, '0')}`;</span> // Format week as "Weeks01", "Weeks02", etc.
        const previousFcstData = <span class="cstat-no" title="statement not covered" >adjustmentWorksheetDataSlice[formattedWeekKey].find(<span class="fstat-no" title="function not covered" >su</span>bRows =&gt; <span class="cstat-no" title="statement not covered" >subRows.subRow === ADJ_SUB_ROW.MERCH_FORECAST)</span> ||</span>
          adjustmentWorksheetDataSlice[formattedWeekKey].find(<span class="fstat-no" title="function not covered" >su</span>bRows =&gt; <span class="cstat-no" title="statement not covered" >subRows.subRow === ADJ_SUB_ROW.PROJECTION)</span>;
        const previousLYData = <span class="cstat-no" title="statement not covered" >adjustmentWorksheetDataSlice[formattedWeekKey].find(<span class="fstat-no" title="function not covered" >su</span>bRows =&gt; <span class="cstat-no" title="statement not covered" >subRows.subRow === ADJ_SUB_ROW.LAST_YEAR_ACTUAL)</span></span>
        const touched = <span class="cstat-no" title="statement not covered" >_touched || values?.touched;</span>
        const I33 = <span class="cstat-no" title="statement not covered" >Number(salesPublic || formik.values.salesPublic);</span>
        const J2 = <span class="cstat-no" title="statement not covered" >Number(previousLYData.line1PublicToSalesNbr)</span>
        const I2 = <span class="cstat-no" title="statement not covered" >Number(previousFcstData.line1PublicToSalesNbr);</span>
        const I34 = <span class="cstat-no" title="statement not covered" >I33 + I2;</span>
        const K2 = <span class="cstat-no" title="statement not covered" >Number(previousFcstData.line5BookGrossProfitNbr || 0);</span>
        const M2 = <span class="cstat-no" title="statement not covered" >Number(previousFcstData.line5MarkDownsNbr || 0);</span>
        const O2 = <span class="cstat-no" title="statement not covered" >Number(previousFcstData.line5ShrinkNbr || 0);</span>
        const K33DeltaBgpNbr = <span class="cstat-no" title="statement not covered" >((K2 / I2) * (I2 + I33)) - K2;</span>
        const M33DeltaMrkNbr = <span class="cstat-no" title="statement not covered" >((M2 / I2) * I34) - M2;</span>
        const O33DeltaShkNbr = <span class="cstat-no" title="statement not covered" >((O2 / I2) * I34) - O2;</span>
        const K34bgpNbr = <span class="cstat-no" title="statement not covered" >touched?.has('grossProfit') ? Number(values.grossProfit?.replace(/,/g, '')) : K33DeltaBgpNbr + K2;</span>
        const M34MrkNbr = <span class="cstat-no" title="statement not covered" >touched?.has('marksDown') ? Number(values.marksDown?.replace(/,/g, '')) : M33DeltaMrkNbr + M2;</span>
        const O34ShkNbr = <span class="cstat-no" title="statement not covered" >touched?.has('totalShrink') ? Number(values.totalShrink?.replace(/,/g, '')) : O33DeltaShkNbr + O2;</span>
        // calc perc
        const J34Line1Pcnt = <span class="cstat-no" title="statement not covered" >(I34 - J2) / J2;</span>
        const L34BgpPcnt = <span class="cstat-no" title="statement not covered" >K34bgpNbr / I34;</span>
        const N34MrkPcnt = <span class="cstat-no" title="statement not covered" >M34MrkNbr / I34;</span>
        const P34ShkPcnt = <span class="cstat-no" title="statement not covered" >O34ShkNbr / I34;</span>
        const allowancesValue: Allowance = <span class="cstat-no" title="statement not covered" >getAllowances(values.selling, values.nonSelling, previousFcstData);</span>
        const suppliesPackaging = <span class="cstat-no" title="statement not covered" >getSuppliesPackaging(values.suppliesPackaging);</span>
        const weekField: Week =
<span class="cstat-no" title="statement not covered" >        {</span>
          fiscalWeekNbr: Number(previousFcstData['fiscalWeekNbr']),
          editedColumns: Array.from(values.touched).join("|"),
          previousAggregatedData: {
            line1PublicToSalesNbr: previousFcstData?.line1PublicToSalesNbr || 0,
            line1PublicToSalesPct: previousFcstData?.line1PublicToSalesPct || 0,
            line5BookGrossProfitNbr: previousFcstData?.line5BookGrossProfitNbr || 0,
            line5BookGrossProfitPct: previousFcstData?.line5BookGrossProfitPct || 0,
            line5MarkDownsNbr: previousFcstData?.line5MarkDownsNbr || 0,
            line5MarkDownsPct: previousFcstData?.line5MarkDownsPct || 0,
            line5ShrinkNbr: previousFcstData?.line5ShrinkNbr || 0,
            line5ShrinkPct: previousFcstData?.line5ShrinkPct || 0,
            line6SuppliesPackagingNbr: previousFcstData?.line6SuppliesPackagingNbr || 0,
            line7RetailsAllowancesNbr: previousFcstData?.line7RetailsAllowancesNbr || 0,
            line7RetailsSellingAllowancesNbr: previousFcstData?.line7RetailsSellingAllowancesNbr || 0,
            line7RetailsNonSellingAllowancesNbr: previousFcstData?.line7RetailsNonSellingAllowancesNbr || 0
          },
          newAggregatedData: {
            //TODO : need to separate the api creation from field pop
            line1PublicToSalesNbr: I34,
            line1PublicToSalesPct: J34Line1Pcnt,
            line5BookGrossProfitNbr: K34bgpNbr, //Number(previousAdjData?.line5BookGrossProfitNbr || 0) + Number(values.grossProfit || 0),
            line5BookGrossProfitPct: L34BgpPcnt,
            line5MarkDownsNbr: M34MrkNbr,//Number(previousAdjData?.line5MarkDownsNbr || 0) + Number(values.marksDown || 0),
            line5MarkDownsPct: N34MrkPcnt,
            line5ShrinkNbr: O34ShkNbr,//Number(previousAdjData?.line5ShrinkNbr) + Number(values.totalShrink || 0),
            line5ShrinkPct: P34ShkPcnt,
            line6SuppliesPackagingNbr: suppliesPackaging,
            line7RetailsAllowancesNbr: allowancesValue.totalAllowances,
            line7RetailsSellingAllowancesNbr: allowancesValue.selling,
            line7RetailsNonSellingAllowancesNbr: allowancesValue.nonSelling
          },
          reason: values.adjustmentReason?.name || '',
          comment: values.comment || '',
        }
<span class="cstat-no" title="statement not covered" >        return weekField</span>
      });
<span class="cstat-no" title="statement not covered" >      return weeks.sort(<span class="fstat-no" title="function not covered" >(a</span>, b) =&gt; <span class="cstat-no" title="statement not covered" >Number(a.fiscalWeekNbr) - Number(b.fiscalWeekNbr))</span>;</span>
    }
    else {
      const values = <span class="cstat-no" title="statement not covered" >formik.values</span>
      const weeks: Week[] = <span class="cstat-no" title="statement not covered" >values.selectedWeeks.map(<span class="fstat-no" title="function not covered" >(w</span>eek): Week =&gt; {</span>
        // debugger;
        const formattedWeekKey = <span class="cstat-no" title="statement not covered" >`Weeks${week.toString().padStart(2, '0')}`;</span> // Format week as "Weeks01", "Weeks02", etc.
        const previousFcstData = <span class="cstat-no" title="statement not covered" >adjustmentWorksheetDataSlice[formattedWeekKey].find(<span class="fstat-no" title="function not covered" >su</span>bRows =&gt; <span class="cstat-no" title="statement not covered" >subRows.subRow === ADJ_SUB_ROW.MERCH_FORECAST)</span> ||</span>
          adjustmentWorksheetDataSlice[formattedWeekKey].find(<span class="fstat-no" title="function not covered" >su</span>bRows =&gt; <span class="cstat-no" title="statement not covered" >subRows.subRow === ADJ_SUB_ROW.PROJECTION)</span>;
        const previousLYData = <span class="cstat-no" title="statement not covered" >adjustmentWorksheetDataSlice[formattedWeekKey].find(<span class="fstat-no" title="function not covered" >su</span>bRows =&gt; <span class="cstat-no" title="statement not covered" >subRows.subRow === ADJ_SUB_ROW.LAST_YEAR_ACTUAL)</span></span>
        const touched = <span class="cstat-no" title="statement not covered" >_touched || values?.weekData?.[week]?.touched;</span>
        const I33 = <span class="cstat-no" title="statement not covered" >Number(salesPublic ? salesPublic : formik?.values?.weekData?.[week]?.salesPublic?.replace(/,/g, '')) || 0;</span>
        const J2 = <span class="cstat-no" title="statement not covered" >Number(previousLYData?.line1PublicToSalesNbr || 0);</span>
        const I2 = <span class="cstat-no" title="statement not covered" >Number(previousFcstData?.line1PublicToSalesNbr || 0);</span>
        const I34 = <span class="cstat-no" title="statement not covered" >I33 + I2;</span>
        const K2 = <span class="cstat-no" title="statement not covered" >Number(previousFcstData?.line5BookGrossProfitNbr || 0);</span>
        const M2 = <span class="cstat-no" title="statement not covered" >Number(previousFcstData?.line5MarkDownsNbr || 0);</span>
        const O2 = <span class="cstat-no" title="statement not covered" >Number(previousFcstData?.line5ShrinkNbr || 0);</span>
        const K33DeltaBgpNbr = <span class="cstat-no" title="statement not covered" >I2 ? ((K2 / I2) * I34) - K2 : 0;</span>
        const M33DeltaMrkNbr = <span class="cstat-no" title="statement not covered" >I2 ? ((M2 / I2) * I34) - M2 : 0;</span>
        const O33DeltaShkNbr = <span class="cstat-no" title="statement not covered" >I2 ? ((O2 / I2) * I34) - O2 : 0;</span>
        const K34bgpNbr = <span class="cstat-no" title="statement not covered" >touched?.has('grossProfit') ? Number(formik?.values?.weekData?.[week]?.grossProfit?.replace(/,/g, '')) : K33DeltaBgpNbr + K2;</span>
        const M34MrkNbr = <span class="cstat-no" title="statement not covered" >touched?.has('marksDown') ? Number(formik?.values?.weekData?.[week]?.marksDown?.replace(/,/g, '')) : M33DeltaMrkNbr + M2;</span>
        const O34ShkNbr = <span class="cstat-no" title="statement not covered" >touched?.has('totalShrink') ? Number(formik?.values?.weekData?.[week]?.totalShrink?.replace(/,/g, '')) : O33DeltaShkNbr + O2;</span>
        // calc perc
        const J34Line1Pcnt = <span class="cstat-no" title="statement not covered" >J2 ? (I34 - J2) / J2 : 0;</span>
        const L34BgpPcnt = <span class="cstat-no" title="statement not covered" >I34 ? K34bgpNbr / I34 : 0;</span>
        const N34MrkPcnt = <span class="cstat-no" title="statement not covered" >I34 ? M34MrkNbr / I34 : 0;</span>
        const P34ShkPcnt = <span class="cstat-no" title="statement not covered" >I34 ? O34ShkNbr / I34 : 0;</span>
        const allowancesValue: Allowance = <span class="cstat-no" title="statement not covered" >getAllowances(values?.weekData?.[week]?.selling, values?.weekData?.[week]?.nonSelling, previousFcstData);</span>
        const suppliesPackaging = <span class="cstat-no" title="statement not covered" >getSuppliesPackaging(values?.weekData?.[week]?.suppliesPackaging);</span>
        const weekField: Week =
<span class="cstat-no" title="statement not covered" >        {</span>
          fiscalWeekNbr: getFiscalWeekNumber(previousFcstData, previousLYData),
          editedColumns: Array.from(formik?.values?.weekData?.[week]?.touched || new Set&lt;string&gt;())
            .filter(<span class="fstat-no" title="function not covered" >(f</span>ield) =&gt; <span class="cstat-no" title="statement not covered" >field !== 'adjustmentReason' &amp;&amp; field !== 'comment')</span>
            .join('|'),
          previousAggregatedData: {
            line1PublicToSalesNbr: previousFcstData?.line1PublicToSalesNbr || 0,
            line1PublicToSalesPct: previousFcstData?.line1PublicToSalesPct || 0,
            line5BookGrossProfitNbr: previousFcstData?.line5BookGrossProfitNbr || 0,
            line5BookGrossProfitPct: previousFcstData?.line5BookGrossProfitPct || 0,
            line5MarkDownsNbr: previousFcstData?.line5MarkDownsNbr || 0,
            line5MarkDownsPct: previousFcstData?.line5MarkDownsPct || 0,
            line5ShrinkNbr: previousFcstData?.line5ShrinkNbr || 0,
            line5ShrinkPct: previousFcstData?.line5ShrinkPct || 0,
            line6SuppliesPackagingNbr: previousFcstData?.line6SuppliesPackagingNbr || 0,
            line7RetailsAllowancesNbr: previousFcstData?.line7RetailsAllowancesNbr || 0,
            line7RetailsSellingAllowancesNbr: previousFcstData?.line7RetailsSellingAllowancesNbr || 0,
            line7RetailsNonSellingAllowancesNbr: previousFcstData?.line7RetailsNonSellingAllowancesNbr || 0
          },
          newAggregatedData: {
            //TODO : need to separate the api creation from field pop
            line1PublicToSalesNbr: I34,
            line1PublicToSalesPct: J34Line1Pcnt,
            line5BookGrossProfitNbr: K34bgpNbr, //Number(previousAdjData?.line5BookGrossProfitNbr || 0) + Number(values.grossProfit || 0),
            line5BookGrossProfitPct: L34BgpPcnt,
            line5MarkDownsNbr: M34MrkNbr,//Number(previousAdjData?.line5MarkDownsNbr || 0) + Number(values.marksDown || 0),
            line5MarkDownsPct: N34MrkPcnt,
            line5ShrinkNbr: O34ShkNbr,//Number(previousAdjData?.line5ShrinkNbr) + Number(values.totalShrink || 0),
            line5ShrinkPct: P34ShkPcnt,
            line6SuppliesPackagingNbr: suppliesPackaging,
            line7RetailsAllowancesNbr: allowancesValue.totalAllowances,
            line7RetailsSellingAllowancesNbr: allowancesValue.selling,
            line7RetailsNonSellingAllowancesNbr: allowancesValue.nonSelling
          },
          reason: formik?.values?.weekData?.[week]?.adjustmentReason?.name || '',
          comment: formik?.values?.weekData?.[week]?.comment || '',
        }
<span class="cstat-no" title="statement not covered" >        return weekField</span>
      });
<span class="cstat-no" title="statement not covered" >      return weeks.sort(<span class="fstat-no" title="function not covered" >(a</span>, b) =&gt; <span class="cstat-no" title="statement not covered" >Number(a.fiscalWeekNbr) - Number(b.fiscalWeekNbr))</span>;</span>
    }
  }
&nbsp;
<span class="cstat-no" title="statement not covered" >  useEffect(<span class="fstat-no" title="function not covered" >()</span> =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (isOpen &amp;&amp; isWeekUpdated(formik.values)) {</span>
      const updatedWeeks = <span class="cstat-no" title="statement not covered" >recalculateSelectedWeekPayload();</span>
<span class="cstat-no" title="statement not covered" >      if (formik.values.selectedRadio === 'singleweek') {</span>
<span class="cstat-no" title="statement not covered" >        setWeeksCalculated(<span class="fstat-no" title="function not covered" >(p</span>revWeeks) =&gt; {</span>
          const updatedWeekMap = <span class="cstat-no" title="statement not covered" >new Map(prevWeeks.map(<span class="fstat-no" title="function not covered" >(w</span>eek) =&gt; <span class="cstat-no" title="statement not covered" >[week.fiscalWeekNbr, week])</span>);</span>
<span class="cstat-no" title="statement not covered" >          updatedWeeks.forEach(<span class="fstat-no" title="function not covered" >(w</span>eek) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            updatedWeekMap.set(week.fiscalWeekNbr, week);</span>
          });
<span class="cstat-no" title="statement not covered" >          return Array.from(updatedWeekMap.values());</span>
        });
      } else {
<span class="cstat-no" title="statement not covered" >        setWeeksCalculated([...updatedWeeks]);</span>
      }
    }
  }, [formik.values.selectedWeeks, formik.values]);
&nbsp;
  const weeksInQuarter = <span class="cstat-no" title="statement not covered" >useMemo&lt;any&gt;(<span class="fstat-no" title="function not covered" >()</span> =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    return Object.keys(adjustmentWorksheetDataSlice).filter(<span class="fstat-no" title="function not covered" >ke</span>y =&gt; <span class="cstat-no" title="statement not covered" >key.includes("Week"))</span>.length;</span>
  }, [adjustmentWorksheetDataSlice]);
&nbsp;
  /** Calculate Total baseline values on load for selected weeks */
<span class="cstat-no" title="statement not covered" >  useEffect(<span class="fstat-no" title="function not covered" >()</span> =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if(formik.values.selectedWeeks.length &gt; 0) {</span>
      const newTotalBaseline = <span class="cstat-no" title="statement not covered" >{</span>
        'salesPublic': 0,
        'grossProfit': 0,
        'grossProfitPct': 0,
        'marksDown': 0,
        'marksDownPct': 0,
        'totalShrink': 0,
        'totalShrinkPct': 0,
        'suppliesPackaging': 0,
        'allowances': 0,
        'selling': 0,
        'nonSelling': 0,
      }
      const updatedTotalValue = <span class="cstat-no" title="statement not covered" >{</span>
        'salesPublic': 0,
        'grossProfit': 0,
        'marksDown': 0,
        'totalShrink': 0,
        'suppliesPackaging': 0,
        'allowances': 0,
        'selling': 0,
        'nonSelling': 0,
      }
      const lastYearActual: FormFieldNames = <span class="cstat-no" title="statement not covered" >{</span>
        salesPublic: 0,
        grossProfit: 0,
        marksDown: 0,
        totalShrink: 0,
        suppliesPackaging: 0,
        allowances: 0,
        selling: 0,
        nonSelling: 0
      }
<span class="cstat-no" title="statement not covered" >      formik.values.selectedWeeks.forEach(<span class="fstat-no" title="function not covered" >(w</span>eek) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        Object.keys(newTotalBaseline).forEach(<span class="fstat-no" title="function not covered" >(f</span>ield) =&gt; {</span>
          const baselineValue = <span class="cstat-no" title="statement not covered" >transformForecastData(adjustmentWorksheetData, ADJ_SUB_ROW.MERCH_FORECAST, ADJ_SUB_ROW.PROJECTION)?.[week]?.[field] || 0;</span>
<span class="cstat-no" title="statement not covered" >          newTotalBaseline[field] += baselineValue;</span>
&nbsp;
          let fieldValue = <span class="cstat-no" title="statement not covered" >0;</span>
<span class="cstat-no" title="statement not covered" >          if (formik.values.selectedRadio === 'singleweek') {</span>
<span class="cstat-no" title="statement not covered" >            fieldValue = formik?.values?.weekData?.[week]?.[field] || '';</span>
          }
&nbsp;
          const totalInputValue = <span class="cstat-no" title="statement not covered" >Number(fieldValue) * formik.values.selectedWeeks.length</span>
<span class="cstat-no" title="statement not covered" >          updatedTotalValue[field] = totalInputValue + newTotalBaseline[field]</span>
&nbsp;
          const baseLineLastYear = <span class="cstat-no" title="statement not covered" >transformForecastData(adjustmentWorksheetData, ADJ_SUB_ROW.LAST_YEAR_ACTUAL, '')?.[week]?.[field] || 0</span>
<span class="cstat-no" title="statement not covered" >          lastYearActual[field] += baseLineLastYear</span>
        })
&nbsp;
      })
&nbsp;
      const touchedFields = <span class="cstat-no" title="statement not covered" >formik?.values?.weekData?.[formik.values.selectedWeeks[0]]?.touched;</span>
      const isNewWeek = <span class="cstat-no" title="statement not covered" >formik.values.selectedRadio === 'singleweek' &amp;&amp; (!touchedFields || touchedFields.size &lt;= 0);</span>
<span class="cstat-no" title="statement not covered" >      if (formik.values.selectedWeeks.length &lt;= 0 || isNewWeek || formik.values.selectedRadio === 'groupweeks') {</span>
<span class="cstat-no" title="statement not covered" >        formik.setFieldValue('marksDown', '');</span>
<span class="cstat-no" title="statement not covered" >        formik.setFieldValue('grossProfit', '');</span>
<span class="cstat-no" title="statement not covered" >        formik.setFieldValue('totalShrink', '');</span>
<span class="cstat-no" title="statement not covered" >        formik.setFieldValue('salesPublic', '');</span>
<span class="cstat-no" title="statement not covered" >        formik.setFieldValue('suppliesPackaging', '');</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        formik.setFieldValue('salesPublicPct', '');</span>
<span class="cstat-no" title="statement not covered" >        formik.setFieldValue('marksDownPct', '');</span>
<span class="cstat-no" title="statement not covered" >        formik.setFieldValue('grossProfitPct', '');</span>
<span class="cstat-no" title="statement not covered" >        formik.setFieldValue('totalShrinkPct', '');</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        formik.setFieldValue('allowances', '');</span>
<span class="cstat-no" title="statement not covered" >        formik.setFieldValue('selling', '');</span>
<span class="cstat-no" title="statement not covered" >        formik.setFieldValue('nonSelling', '');</span>
      }
      const weekNumber = <span class="cstat-no" title="statement not covered" >formik.values.selectedWeeks[0]</span>
      const baseValues = <span class="cstat-no" title="statement not covered" >getBaseValues(weekNumber);</span>
<span class="cstat-no" title="statement not covered" >      formik.values.weekData = {</span>
        [weekNumber] : {...baseValues},
        ...formik.values.weekData
      }
<span class="cstat-no" title="statement not covered" >      setTotalLastYearActual(lastYearActual)</span>
<span class="cstat-no" title="statement not covered" >      setTotalBaselineValue(newTotalBaseline)</span>
<span class="cstat-no" title="statement not covered" >      setTotalValue(updatedTotalValue)</span>
    }
&nbsp;
  }, [formik.values.selectedWeeks])
&nbsp;
  const getBaseValues = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(w</span>eek) =&gt; {</span>
    const formattedWeekKey = <span class="cstat-no" title="statement not covered" >`Weeks${week.toString().padStart(2, '0')}`;</span> // Format week as "Weeks01", "Weeks02", etc.
    const previousFcstData = <span class="cstat-no" title="statement not covered" >adjustmentWorksheetDataSlice[formattedWeekKey].find(<span class="fstat-no" title="function not covered" >su</span>bRows =&gt; <span class="cstat-no" title="statement not covered" >subRows.subRow === ADJ_SUB_ROW.MERCH_FORECAST)</span> ||</span>
      adjustmentWorksheetDataSlice[formattedWeekKey].find(<span class="fstat-no" title="function not covered" >su</span>bRows =&gt; <span class="cstat-no" title="statement not covered" >subRows.subRow === ADJ_SUB_ROW.PROJECTION)</span>;
    const newTotalBaseline: any = <span class="cstat-no" title="statement not covered" >{</span>
      'grossProfit': 0,
      'grossProfitPct': 0,
      'marksDown': 0,
      'marksDownPct': 0,
      'totalShrink': 0,
      'totalShrinkPct': 0,
      'suppliesPackaging': 0,
      'allowances': 0,
      // 'selling': 0,
      // 'nonSelling': 0,
    }
<span class="cstat-no" title="statement not covered" >    if(previousFcstData) {</span>
<span class="cstat-no" title="statement not covered" >      newTotalBaseline['grossProfit'] = numberFormatter.format(previousFcstData.line5BookGrossProfitNbr);</span>
<span class="cstat-no" title="statement not covered" >      newTotalBaseline['grossProfitPct'] = pctFormatter.format(previousFcstData.line5BookGrossProfitPct);</span>
<span class="cstat-no" title="statement not covered" >      newTotalBaseline['marksDown'] = numberFormatter.format(previousFcstData.line5MarkDownsNbr);</span>
<span class="cstat-no" title="statement not covered" >      newTotalBaseline['marksDownPct'] = pctFormatter.format(previousFcstData.line5MarkDownsPct);</span>
<span class="cstat-no" title="statement not covered" >      newTotalBaseline['totalShrink'] = numberFormatter.format(previousFcstData.line5ShrinkNbr);</span>
<span class="cstat-no" title="statement not covered" >      newTotalBaseline['totalShrinkPct'] = pctFormatter.format(previousFcstData.line5ShrinkPct);</span>
<span class="cstat-no" title="statement not covered" >      newTotalBaseline['allowances'] = numberFormatter.format(previousFcstData.line7RetailsAllowancesNbr);</span>
<span class="cstat-no" title="statement not covered" >      newTotalBaseline['suppliesPackaging'] = numberFormatter.format(previousFcstData.line6SuppliesPackagingNbr);</span>
<span class="cstat-no" title="statement not covered" >      if(previousFcstData.line7RetailsSellingAllowancesNbr !== 0)</span>
<span class="cstat-no" title="statement not covered" >      newTotalBaseline['selling'] = numberFormatter.format(previousFcstData.line7RetailsSellingAllowancesNbr);</span>
<span class="cstat-no" title="statement not covered" >      if(previousFcstData.line7RetailsNonSellingAllowancesNbr !== 0)</span>
<span class="cstat-no" title="statement not covered" >      newTotalBaseline['nonSelling'] = numberFormatter.format(previousFcstData.line7RetailsNonSellingAllowancesNbr);</span>
    }
<span class="cstat-no" title="statement not covered" >    return newTotalBaseline;</span>
  }
&nbsp;
  const handleSubmit = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(e</span>v) =&gt; {</span>
    const errors = <span class="cstat-no" title="statement not covered" >validate(formik.values, true)</span>
    const selectedWeek = <span class="cstat-no" title="statement not covered" >formik.values.selectedWeeks[0]</span>
    const error = <span class="cstat-no" title="statement not covered" >formik.values.selectedRadio === 'singleweek' ? formik.errors.weekData?.[selectedWeek] || formik.errors : formik.errors</span>
<span class="cstat-no" title="statement not covered" >    if (Object.keys(error || {}).length &gt; 0) {</span>
      // this is needed because on singleWeek we are doing short circuiting
      // whereas in group selection we are letting handlesubmit to go thru
<span class="cstat-no" title="statement not covered" >      scrollToFirstError(error, selectors, fieldOrder);</span>
    }
<span class="cstat-no" title="statement not covered" >    if (formik.values.selectedRadio === 'singleweek') {</span>
<span class="cstat-no" title="statement not covered" >      if (Object.keys(errors).length &gt; 0 &amp;&amp; Object.keys(errors.weekData || {}).length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >        formik.setErrors(errors);</span>
<span class="cstat-no" title="statement not covered" >        setActiveWeekSubmit(true)</span>
      }
      else {
<span class="cstat-no" title="statement not covered" >        formik.handleSubmit()</span>
      }
    }
    else {
      // setIsSaveApiLoading(true);
<span class="cstat-no" title="statement not covered" >      formik.handleSubmit()</span>
    }
&nbsp;
  }
&nbsp;
  const updateTotalValue = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(n</span>ame, value) =&gt; {</span>
    const updatedTotalValue = <span class="cstat-no" title="statement not covered" >{ ...totalValue }</span>
<span class="cstat-no" title="statement not covered" >    if (!isNaN(Number(value))) {</span>
<span class="cstat-no" title="statement not covered" >      updatedTotalValue[name] = totalBaselineValue[name] + (Number(value) * formik.values.selectedWeeks.length);</span>
    } 
    else {
<span class="cstat-no" title="statement not covered" >       updatedTotalValue[name] = totalBaselineValue[name];</span>
    }
<span class="cstat-no" title="statement not covered" >    setTotalValue(updatedTotalValue)</span>
<span class="cstat-no" title="statement not covered" >    return updatedTotalValue;</span>
  }
&nbsp;
  const isCommentValid = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(c</span>omment: string, maxLength) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    return comment.length &gt; maxLength;</span>
  }
&nbsp;
  const renderInputs = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >({</span> state, setter, inputType, name, stateWarnings = <span class="branch-0 cbranch-no" title="branch not covered" >{},</span> touched }) =&gt; {</span>
    const editState = <span class="cstat-no" title="statement not covered" >editStates[name] || {};</span>
    const newOnChange = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(e</span>) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      setter(e);</span>
    };
    const getPctValue = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(f</span>ieldName) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (formik.values.selectedWeeks.length &lt;= 0) <span class="cstat-no" title="statement not covered" >return '';</span></span>
<span class="cstat-no" title="statement not covered" >      if (formik.values.selectedRadio === 'groupweeks') <span class="cstat-no" title="statement not covered" >return formik.values[`${fieldName}Pct`];</span></span>
      const specificWeekData = <span class="cstat-no" title="statement not covered" >formik.values.weekData?.[formik.values.selectedWeeks[0]];</span>
<span class="cstat-no" title="statement not covered" >      if (!specificWeekData) <span class="cstat-no" title="statement not covered" >return '';</span></span>
<span class="cstat-no" title="statement not covered" >      return specificWeekData[`${fieldName}Pct`] || '';</span>
    };
    const getSubLabelLine1 = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(_</span>value, _name) =&gt; {</span>
      const _totalValue = <span class="cstat-no" title="statement not covered" >numberFormatter.format(Number(totalValue?.[_name]))</span>
<span class="cstat-no" title="statement not covered" >      if(Number(_value)) {</span>
        const _totalBaseLineValue = <span class="cstat-no" title="statement not covered" >numberFormatter.format(Number(totalBaselineValue[_name]))</span>
<span class="cstat-no" title="statement not covered" >        return `$${_totalBaseLineValue} ➔ $${_totalValue}`;</span>
      }
<span class="cstat-no" title="statement not covered" >      return `Current Sales: $${_totalValue}`;</span>
    };
    const getSubLabelLineItems = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(_</span>value, _name, _isPct = <span class="branch-0 cbranch-no" title="branch not covered" >false)</span> =&gt; {</span>
      const formatter = <span class="cstat-no" title="statement not covered" >_isPct ? pctFormatter : numberFormatter;</span>
      const _baseLineValue = <span class="cstat-no" title="statement not covered" >formatter.format(Number(removeCommas(totalBaselineValue[_name] || "0")));</span>
      const _inputValue = <span class="cstat-no" title="statement not covered" >formatter.format(Number(removeCommas(_value)));</span>
<span class="cstat-no" title="statement not covered" >      if(_inputValue !== _baseLineValue) {</span>
<span class="cstat-no" title="statement not covered" >        return _isPct ? `was: ${pctFormatter.format(Number(_baseLineValue))}%` : `was: $${_baseLineValue}`;</span>
      }
<span class="cstat-no" title="statement not covered" >      return ``;</span>
    };
&nbsp;
    const getSubLabelforLine8 = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >()</span> =&gt; {</span>
      const _inputValue = <span class="cstat-no" title="statement not covered" >numberFormatter.format(</span>
        Number(removeCommas(getLine8(formik.values.weekData?.[formik.values.selectedWeeks[0]]))))
      const _baseLineValue = <span class="cstat-no" title="statement not covered" >numberFormatter.format(Number(removeCommas(getLine8(totalBaselineValue))))</span>
<span class="cstat-no" title="statement not covered" >      if(_inputValue !== _baseLineValue) {</span>
<span class="cstat-no" title="statement not covered" >        return `was: $${_baseLineValue}`;</span>
      }
<span class="cstat-no" title="statement not covered" >      return ``;</span>
    }
&nbsp;
    const getSubLabelforLine8Pct = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >()</span> =&gt; {</span>
      const _inputValue = <span class="cstat-no" title="statement not covered" >numberFormatter.format(</span>
        Number(removeCommas(getLine8(formik.values.weekData?.[formik.values.selectedWeeks[0]]))))
      const line1Base = <span class="cstat-no" title="statement not covered" >totalBaselineValue['salesPublic'] || 0;</span>
      const _baseLineValue = <span class="cstat-no" title="statement not covered" >numberFormatter.format(Number(removeCommas(getLine8(totalBaselineValue))))</span>
<span class="cstat-no" title="statement not covered" >      if(_inputValue !== _baseLineValue) {</span>
<span class="cstat-no" title="statement not covered" >        return line1Base ? `was: ${pctFormatter.format((Number(removeCommas(_baseLineValue)) / line1Base) * 100)}%` : ``;</span>
      }
<span class="cstat-no" title="statement not covered" >      return ``;</span>
    }
&nbsp;
    const error = <span class="cstat-no" title="statement not covered" >stateWarnings[name] === DEVIATION_WARNING_MSG;</span>
    const isAllowanceField = <span class="cstat-no" title="statement not covered" >name === 'allowances';</span>
    // Always show restore button, enable only if value changed from previousValue
    const canRestore = <span class="cstat-no" title="statement not covered" >state !== editState.previousValue &amp;&amp; editState.editing;</span>
    const arrowColor = <span class="cstat-no" title="statement not covered" >canRestore ? '#1B6EBB' : '#B9C0D4';</span>
    const line8Value = <span class="cstat-no" title="statement not covered" >getLine8(formik.values.weekData?.[formik.values.selectedWeeks[0]]);</span>
    const line8Pct = <span class="cstat-no" title="statement not covered" >totalValue['salesPublic'] ? (line8Value / totalValue['salesPublic']) * 100 : 0;</span>
    const baseLine8Value = <span class="cstat-no" title="statement not covered" >getSubLabelforLine8();</span>
    const baseLine8Pct = <span class="cstat-no" title="statement not covered" >getSubLabelforLine8Pct();</span>
<span class="cstat-no" title="statement not covered" >    switch (inputType) {</span>
      case 'dollar':
<span class="cstat-no" title="statement not covered" >        return (</span>
          &lt;&gt;
            &lt;TextField
              className="w-[150px] gap-[4px] rounded border-[1px]"
              value={state}
              title={state}
              prefix="$"
              subLabel={`${getSubLabelLine1(state, name)}`}
              placeholder="±0"
              onChange={newOnChange}
              error={error}
              id={name}
              size="sm"
              data-testid={`${name}-input`}
            /&gt;
            &lt;TextField
              className={`w-[100px] rounded border-[1px]`}
              value={getPctValue(name)}
              placeholder="± 0"
              suffix="%"
              onChange={newOnChange}
              id={`${name}Pct`}
              size="sm"
              data-testid={`${name}Pct-input`}
              name={`Input for ${name}Pct in percentage`}
            /&gt;
          &lt;/&gt;
        );
      case 'noPct':
<span class="cstat-no" title="statement not covered" >        return (</span>
          &lt;&gt;
            {isAllowanceField ? (
              &lt;div data-testid={`${name}-input`} className="w-fit h-fit inline-flex flex-col items-center"&gt;
                &lt;div className="font-semibold pl-3 self-start min-w-[135px] min-h-[32px] content-center"&gt;{`$${state || 0}`}&lt;/div&gt;
              &lt;/div&gt;
            ) : (
              &lt;div className="flex flex-col items-start w-fit"&gt;
                &lt;TextField
                  className="w-[273px] gap-[4px] rounded border-[1px]"
                  value={state}
                  prefix="$"
                  onChange={newOnChange}
                  subLabel={(name !== 'selling' &amp;&amp; name !== 'nonSelling') ? `${getSubLabelLineItems(state, name)}` : ``}
                  id={name}
                  size="sm"
                  data-testid={`${name}-input`}
                  onFocus={<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >handleInputFocus(name, state)}</span>
                  onBlur={<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >handleInputBlur(name)}</span>
                /&gt;
                {(name === 'selling' || name === 'nonSelling') &amp;&amp; (
                  &lt;div className="flex justify-between w-[273px] mt-[4px] text-sm text-[#5a697b] items-center"&gt;
                    {(Number(removeCommas(state)) !== Number(removeCommas(totalBaselineValue[name])) &amp;&amp; state !== '') ? (
                      &lt;div&gt;
                        {`${getSubLabelLineItems(state, name)}`}
                      &lt;/div&gt;
                    ) : (
                      &lt;div /&gt;
                    )}
                    &lt;button
                      type="button"
                      className="ml-auto flex items-center gap-1 text-[#1b6ebb] underline disabled:text-gray-400"
                      onMouseDown={<span class="fstat-no" title="function not covered" >e </span>=&gt; <span class="cstat-no" title="statement not covered" >e.preventDefault()}</span>
                      onClick={<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >handleRestoreValue(name, setter)}</span>
                      disabled={!canRestore}
                    &gt;
                      {ArrowSymbol(arrowColor)}
                      Restore value
                    &lt;/button&gt;
                  &lt;/div&gt;
                )}
              &lt;/div&gt;
            )}
          &lt;/&gt;
        );
      case 'text':
<span class="cstat-no" title="statement not covered" >        return (</span>
          &lt;&gt;
            &lt;TextField
              className="w-[150px] gap-[4px] rounded border-[1px]"
              value={state}
              prefix="$"
              onChange={newOnChange}
              error={error}
              subLabel={`${getSubLabelLineItems(state, name)}`}
              id={name}
              size="sm"
              data-testid={`${name}-input`}
&nbsp;
            /&gt;
            &lt;TextField
              className="w-[100px] rounded border-[1px]"
              value={(getPctValue(name))}
              suffix="%"
              onChange={newOnChange}
              subLabel={`${getSubLabelLineItems(getPctValue(name), `${name}Pct`, true)}`}
              id={`${name}Pct`}
              size="sm"
              data-testid={`${name}Pct-input`}
              aria-describedby={`${name}Pct-description`}
            /&gt;
          &lt;/&gt;
        );
      case 'readonly':
<span class="cstat-no" title="statement not covered" >        return (</span>
          &lt;div data-testid={`${name}-input`} className="inline-flex flex-row items-center gap-6"&gt;
            &lt;div className="gap-2 min-w-[130px] self-end items-end min-h-[32px] flex flex-col"&gt;
              &lt;span className='font-normal text-base leading-5 tracking-normal align-middle'&gt; {`$${numberFormatter.format(line8Value) || 0}`}&lt;/span&gt;
              &lt;span className='text-gray-500 font-normal text-sm leading-4 tracking-normal align-middle'&gt; {baseLine8Value}&lt;/span&gt;
&nbsp;
            &lt;/div&gt;
            &lt;div className="gap-2 min-w-[100px] justify-center self-start items-start flex flex-col"&gt;
              &lt;span className='font-normal text-base leading-5 tracking-normal align-middle'&gt; {`${pctFormatter.format(line8Pct) || 0}%`}&lt;/span&gt;
              &lt;span className='text-gray-500 font-normal text-sm leading-4 tracking-normal align-middle'&gt; {baseLine8Pct}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        );
      default:
<span class="cstat-no" title="statement not covered" >        return null;</span>
    }
  }
&nbsp;
<span class="cstat-no" title="statement not covered" >  useEffect(<span class="fstat-no" title="function not covered" >()</span> =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (!isOpen) {</span>
<span class="cstat-no" title="statement not covered" >      clearFormFields()</span>
<span class="cstat-no" title="statement not covered" >      setIsSaveApiLoading(false);</span>
    }
  }, [isOpen])
&nbsp;
  const addSign = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(v</span>alue, name) =&gt; {</span>
&nbsp;
    let targetvalue = <span class="cstat-no" title="statement not covered" >value.replace(/[^0-9.\-+]/g, '');</span>
<span class="cstat-no" title="statement not covered" >    if (targetvalue.length &gt; 1) {</span>
<span class="cstat-no" title="statement not covered" >      targetvalue = targetvalue.replace(/(?&lt;!^)[+-]/g, '')</span>
    }
<span class="cstat-no" title="statement not covered" >    if (name === 'salesPublic') {</span>
<span class="cstat-no" title="statement not covered" >      if (targetvalue &amp;&amp; !targetvalue.startsWith('+') &amp;&amp; !targetvalue.startsWith('-')) {</span>
<span class="cstat-no" title="statement not covered" >        targetvalue = `+${targetvalue}`</span>
      }
    }
<span class="cstat-no" title="statement not covered" >    return targetvalue;</span>
  }
&nbsp;
  const removeCommas = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(v</span>alue) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      return (value &amp;&amp; typeof value === 'string') ? value.replace(/,/g, '') : value;</span>
  }
&nbsp;
  const getLine8 = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(n</span>ewValues: any) =&gt; {</span>
    const line5a = <span class="cstat-no" title="statement not covered" >newValues?.grossProfit || '0';</span>
    const line5b = <span class="cstat-no" title="statement not covered" >newValues?.marksDown || '0';</span>
    const line5c = <span class="cstat-no" title="statement not covered" >newValues?.totalShrink || '0';</span>
    const line6 = <span class="cstat-no" title="statement not covered" >newValues?.suppliesPackaging || '0';</span>
    const line7 = <span class="cstat-no" title="statement not covered" >newValues?.allowances || '0';</span>
    const line5 = <span class="cstat-no" title="statement not covered" >Number(removeCommas(line5a)) + Number(removeCommas(line5b)) + Number(removeCommas(line5c));</span>
    const line8 = <span class="cstat-no" title="statement not covered" >line5 + Number(removeCommas(line6)) + Number(removeCommas(line7));</span>
<span class="cstat-no" title="statement not covered" >    return line8;</span>
  }
&nbsp;
  const isValidTargetValue = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(v</span>alue) =&gt; {</span>
    const parsed = <span class="cstat-no" title="statement not covered" >Number(value);</span>
<span class="cstat-no" title="statement not covered" >    return !Number.isNaN(parsed) &amp;&amp; parsed !== 0;</span>
  }
  /** This method is to ensure all the form fields in the single week selection is validated properly by appropriately adding and deleting the entries from weekdata
   * Once user types values - capture the data weekwise
   * Once the user deletes the value of the field, delete the entry from weekdata to determine the week is in error state / valid state
   * Validation should happen only when there is any change in the particular week, which is been captured in weekdata, so its important to keep the weekdata updated
   */
  const singleWeekHandleChange = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(t</span>argetName, e, week) =&gt; {</span>
&nbsp;
    const targetId: string = <span class="cstat-no" title="statement not covered" >e.target.id;</span>
    let targetvalue = <span class="cstat-no" title="statement not covered" >e.target.value;</span>
    let isPct = <span class="cstat-no" title="statement not covered" >false;</span>
    let actualTargetName = <span class="cstat-no" title="statement not covered" >targetName;</span>
<span class="cstat-no" title="statement not covered" >    if (targetId.includes('Pct')) {</span>
<span class="cstat-no" title="statement not covered" >      isPct = true;</span>
<span class="cstat-no" title="statement not covered" >      actualTargetName = `${targetName}Pct`</span>
    } else {
<span class="cstat-no" title="statement not covered" >      targetvalue = targetvalue &amp;&amp; addSign(targetvalue, targetName)</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    formik.setValues(<span class="fstat-no" title="function not covered" >(p</span>revValues) =&gt; {</span>
      const newValues = <span class="cstat-no" title="statement not covered" >{ ...prevValues }</span>
      /***Ensure weekdata exists */
<span class="cstat-no" title="statement not covered" >      if (!newValues.weekData) {</span>
<span class="cstat-no" title="statement not covered" >        newValues.weekData = {}</span>
      }
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (!targetvalue) {</span>
        /** Remove the specified field */
<span class="cstat-no" title="statement not covered" >        if (newValues.weekData[week]) {</span>
<span class="cstat-no" title="statement not covered" >          delete newValues.weekData[week][targetName];</span>
<span class="cstat-no" title="statement not covered" >          delete newValues.weekData[week][`${targetName}Pct`];</span>
&nbsp;
          /** if  weekData[week] is now an empty object, remove it*/
<span class="cstat-no" title="statement not covered" >          if (Object.keys(newValues.weekData[week]).length === 0) {</span>
<span class="cstat-no" title="statement not covered" >            delete newValues.weekData[week]</span>
          }
        }
&nbsp;
        /** If weekdata itself is empty after deleting, remove it completely */
<span class="cstat-no" title="statement not covered" >        if (Object.keys(newValues.weekData).length === 0) {</span>
          // delete newValues.weekData;
<span class="cstat-no" title="statement not covered" >          newValues.weekData = {};</span>
&nbsp;
        }
      } else {
        /** Set the specified field */
        const touched = <span class="cstat-no" title="statement not covered" >newValues.weekData?.[week]?.touched || new Set&lt;string&gt;();</span>
<span class="cstat-no" title="statement not covered" >        touched.add(targetName);</span>
<span class="cstat-no" title="statement not covered" >        newValues.weekData = {</span>
          ...newValues.weekData,
          [week]: {
            ...newValues.weekData?.[week],
            [actualTargetName]: targetvalue,
            touched
          }
        }
      }
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (targetName === 'salesPublic') {</span>
<span class="cstat-no" title="statement not covered" >        newValues.weekData[week].touched = resetTouched(newValues.weekData[week].touched);</span>
<span class="cstat-no" title="statement not covered" >        if (isValidTargetValue(targetvalue)) {</span>
<span class="cstat-no" title="statement not covered" >          if (isPct) {</span>
            const adjustedSP = <span class="cstat-no" title="statement not covered" >totalBaselineValue['salesPublic'];</span>
<span class="cstat-no" title="statement not covered" >            targetvalue = adjustedSP * (Number(e.target.value) / 100);</span>
<span class="cstat-no" title="statement not covered" >            newValues.weekData[week].salesPublic = addSign(numberFormatter.format(targetvalue), targetName).replace(/,/g, '');</span>
          }
          const calculatedWeek = <span class="cstat-no" title="statement not covered" >recalculateSelectedWeekPayload(targetvalue, newValues.weekData[week].touched).find(<span class="fstat-no" title="function not covered" >(w</span>) =&gt; <span class="cstat-no" title="statement not covered" >Number(String(w.fiscalWeekNbr).slice(-2)) === week)</span>;</span>
          const prevSalesPublic = <span class="cstat-no" title="statement not covered" >calculatedWeek?.previousAggregatedData?.line1PublicToSalesNbr || 0;</span>
          const bgpVal = <span class="cstat-no" title="statement not covered" >calculatedWeek?.newAggregatedData.line5BookGrossProfitNbr</span>
          const shrVal = <span class="cstat-no" title="statement not covered" >calculatedWeek?.newAggregatedData.line5ShrinkNbr;</span>
          const mrkVal = <span class="cstat-no" title="statement not covered" >calculatedWeek?.newAggregatedData.line5MarkDownsNbr;</span>
          const spPct = <span class="cstat-no" title="statement not covered" >prevSalesPublic ? calculateSalesPublicPcnt(targetvalue, prevSalesPublic) : 0;</span>
          const bgpPct = <span class="cstat-no" title="statement not covered" >calculatedWeek?.newAggregatedData.line5BookGrossProfitPct || 0;</span>
          const mrkPct = <span class="cstat-no" title="statement not covered" >calculatedWeek?.newAggregatedData.line5MarkDownsPct || 0;</span>
          const shrPct = <span class="cstat-no" title="statement not covered" >calculatedWeek?.newAggregatedData.line5ShrinkPct || 0;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          newValues.weekData[week].grossProfit = bgpVal ? numberFormatter.format(bgpVal) : '';</span>
<span class="cstat-no" title="statement not covered" >          newValues.weekData[week].totalShrink = shrVal ? numberFormatter.format(shrVal) : '';</span>
<span class="cstat-no" title="statement not covered" >          newValues.weekData[week].marksDown = mrkVal ? numberFormatter.format(mrkVal) : '';</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          newValues.weekData[week].grossProfitPct = pctFormatter.format(bgpPct * 100);</span>
<span class="cstat-no" title="statement not covered" >          newValues.weekData[week].totalShrinkPct = pctFormatter.format(shrPct * 100)</span>
<span class="cstat-no" title="statement not covered" >          newValues.weekData[week].marksDownPct = pctFormatter.format(mrkPct * 100)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (isPct === false) {</span>
<span class="cstat-no" title="statement not covered" >            newValues.weekData[week].salesPublicPct = pctFormatter.format(spPct * 100) || '';</span>
          }
&nbsp;
        } else <span class="cstat-no" title="statement not covered" >if(targetvalue !== '+' &amp;&amp; targetvalue !== '-') {</span>
          const baseValues = <span class="cstat-no" title="statement not covered" >getBaseValues(week);</span>
<span class="cstat-no" title="statement not covered" >          newValues.weekData[week].salesPublic = baseValues.salesPublic || '';</span>
<span class="cstat-no" title="statement not covered" >          newValues.weekData[week].salesPublicPct = baseValues.salesPublicPct || '';</span>
<span class="cstat-no" title="statement not covered" >          newValues.weekData[week].grossProfit = baseValues.grossProfit || '';</span>
<span class="cstat-no" title="statement not covered" >          newValues.weekData[week].grossProfitPct = baseValues.grossProfitPct || '';</span>
<span class="cstat-no" title="statement not covered" >          newValues.weekData[week].marksDown = baseValues.marksDown || '';</span>
<span class="cstat-no" title="statement not covered" >          newValues.weekData[week].marksDownPct = baseValues.marksDownPct || '';</span>
<span class="cstat-no" title="statement not covered" >          newValues.weekData[week].totalShrink = baseValues.totalShrink || '';</span>
<span class="cstat-no" title="statement not covered" >          newValues.weekData[week].totalShrinkPct = baseValues.totalShrinkPct || '';</span>
        }
<span class="cstat-no" title="statement not covered" >        updateTotalValue(targetName, targetvalue);</span>
      }
      else <span class="cstat-no" title="statement not covered" >if (newValues.weekData[week] &amp;&amp; (targetName === 'selling' || targetName === 'nonSelling')) {</span>
<span class="cstat-no" title="statement not covered" >        if(!newValues.weekData[week].selling &amp;&amp; !newValues.weekData[week].nonSelling ) {</span>
          const baseValues = <span class="cstat-no" title="statement not covered" >getBaseValues(week);</span>
<span class="cstat-no" title="statement not covered" >          newValues.weekData[week].allowances = baseValues.allowances || '';</span>
        } else {
          const sellingValue = <span class="cstat-no" title="statement not covered" >Number(removeCommas(newValues.weekData[week].selling) || 0);</span>
          const nonSellingValue = <span class="cstat-no" title="statement not covered" >Number(removeCommas(newValues.weekData[week].nonSelling) || 0);</span>
          const allowancesValue = <span class="cstat-no" title="statement not covered" >getAllowancesValue(sellingValue, nonSellingValue);</span>
<span class="cstat-no" title="statement not covered" >          newValues.weekData[week].allowances = numberFormatter.format(allowancesValue);</span>
        }
      }
      else <span class="cstat-no" title="statement not covered" >if (newValues.weekData[week] &amp;&amp; (targetName === 'grossProfit' || targetName === 'marksDown' || targetName === 'totalShrink')) {</span>
<span class="cstat-no" title="statement not covered" >        if (isValidTargetValue(targetvalue)) {</span>
          const adjustedSalesPublic = <span class="cstat-no" title="statement not covered" >totalValue['salesPublic'];</span>
<span class="cstat-no" title="statement not covered" >          if (isPct) {</span>
<span class="cstat-no" title="statement not covered" >            targetvalue = (adjustedSalesPublic * Number(e.target.value) / 100);</span>
<span class="cstat-no" title="statement not covered" >            newValues.weekData[week][targetName] = numberFormatter.format(targetvalue);</span>
          } else {
            const pctChange = <span class="cstat-no" title="statement not covered" >adjustedSalesPublic ? (Number(targetvalue)) / adjustedSalesPublic : 0;</span>
<span class="cstat-no" title="statement not covered" >            newValues.weekData[week][`${targetName}Pct`] = pctFormatter.format(pctChange * 100);</span>
          }
        }
      }
<span class="cstat-no" title="statement not covered" >      return newValues;</span>
    })
&nbsp;
  }
&nbsp;
  const resetTouched = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(t</span>ouched: Set&lt;string&gt;) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    touched.delete('grossProfit');</span>
<span class="cstat-no" title="statement not covered" >    touched.delete('marksDown');</span>
<span class="cstat-no" title="statement not covered" >    touched.delete('totalShrink');</span>
<span class="cstat-no" title="statement not covered" >    return touched;</span>
  }
&nbsp;
  const getAllowancesValue = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(s</span>ellingValue, nonSellingValue) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    return isNaN(sellingValue) ? 0 : sellingValue + (isNaN(nonSellingValue) ? 0 : nonSellingValue);</span>
  }
&nbsp;
  const getAllowances = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(s</span>elling, nonSelling, previousFcstData) =&gt; {</span>
    let allowances: Allowance = <span class="cstat-no" title="statement not covered" >{</span>
      selling: previousFcstData?.line7RetailsSellingAllowancesNbr || 0,
      nonSelling: previousFcstData?.line7RetailsNonSellingAllowancesNbr || 0,
      totalAllowances: previousFcstData?.line7RetailsAllowancesNbr || 0
    };
<span class="cstat-no" title="statement not covered" >    if (selling || nonSelling) {</span>
<span class="cstat-no" title="statement not covered" >      allowances.selling = Number(removeCommas(selling)) || 0;</span>
<span class="cstat-no" title="statement not covered" >      allowances.nonSelling = Number(removeCommas(nonSelling)) || 0;</span>
<span class="cstat-no" title="statement not covered" >      allowances.totalAllowances = getAllowancesValue(allowances.selling, allowances.nonSelling);</span>
    }
<span class="cstat-no" title="statement not covered" >    return allowances;</span>
  }
&nbsp;
  const getSuppliesPackaging = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(v</span>alue: string | undefined) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (value) {</span>
<span class="cstat-no" title="statement not covered" >      return Number(removeCommas(value));</span>
    }
<span class="cstat-no" title="statement not covered" >    return null;</span>
  }
&nbsp;
  const multiWeekHandleChange = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(t</span>argetName, e) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    formik.setFieldValue('touched', {</span>
      ...formik.values.touched,
      [targetName]: true
    });
&nbsp;
    const targetId: string = <span class="cstat-no" title="statement not covered" >e.target.id;</span>
    let targetvalue = <span class="cstat-no" title="statement not covered" >e.target.value;</span>
    let isPct = <span class="cstat-no" title="statement not covered" >false;</span>
    let actualTargetName = <span class="cstat-no" title="statement not covered" >targetName;</span>
<span class="cstat-no" title="statement not covered" >    if (targetId.includes('Pct')) {</span>
<span class="cstat-no" title="statement not covered" >      isPct = true;</span>
<span class="cstat-no" title="statement not covered" >      actualTargetName = `${targetName}Pct`</span>
    } else {
<span class="cstat-no" title="statement not covered" >      targetvalue = targetvalue &amp;&amp; addSign(targetvalue, targetName)</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    formik.setValues(<span class="fstat-no" title="function not covered" >(p</span>revValues) =&gt; {</span>
      const newValues = <span class="cstat-no" title="statement not covered" >{ ...prevValues };</span>
<span class="cstat-no" title="statement not covered" >      if (!targetvalue) {</span>
<span class="cstat-no" title="statement not covered" >        newValues[targetName] = "";</span>
<span class="cstat-no" title="statement not covered" >        newValues[`${targetName}Pct`] = "";</span>
      } else {
<span class="cstat-no" title="statement not covered" >        newValues.touched.add(targetName);</span>
      }
<span class="cstat-no" title="statement not covered" >      newValues[actualTargetName] = targetvalue;</span>
<span class="cstat-no" title="statement not covered" >      updateTotalValue(targetName, targetvalue);</span>
<span class="cstat-no" title="statement not covered" >      if (targetName === 'salesPublic') {</span>
<span class="cstat-no" title="statement not covered" >        newValues.touched = resetTouched(newValues.touched);</span>
<span class="cstat-no" title="statement not covered" >        if (isValidTargetValue(targetvalue)) {</span>
<span class="cstat-no" title="statement not covered" >          if (isPct) {</span>
            const adjustedSP = <span class="cstat-no" title="statement not covered" >totalBaselineValue['salesPublic'];</span>
<span class="cstat-no" title="statement not covered" >            targetvalue = adjustedSP * (Number(e.target.value) / 100);</span>
<span class="cstat-no" title="statement not covered" >            newValues.salesPublic = addSign(numberFormatter.format(targetvalue), targetName).replace(/,/g, '');</span>
          }
          const calculatedWeeks = <span class="cstat-no" title="statement not covered" >recalculateSelectedWeekPayload(targetvalue, newValues.touched);</span>
          const totals = <span class="cstat-no" title="statement not covered" >{</span>
            bgpVal: 0,
            shrVal: 0,
            mrkVal: 0,
            spVal: 0,
            prvSpVal: 0
          };
<span class="cstat-no" title="statement not covered" >          calculatedWeeks.forEach(<span class="fstat-no" title="function not covered" >we</span>ek =&gt; {</span>
            const bgpVal = <span class="cstat-no" title="statement not covered" >week?.newAggregatedData.line5BookGrossProfitNbr</span>
            const shrVal = <span class="cstat-no" title="statement not covered" >week?.newAggregatedData.line5ShrinkNbr;</span>
            const mrkVal = <span class="cstat-no" title="statement not covered" >week?.newAggregatedData.line5MarkDownsNbr;</span>
            const spVal = <span class="cstat-no" title="statement not covered" >week?.newAggregatedData.line1PublicToSalesNbr;</span>
            const prvSpVal = <span class="cstat-no" title="statement not covered" >week?.previousAggregatedData.line1PublicToSalesNbr;</span>
<span class="cstat-no" title="statement not covered" >            totals.bgpVal = totals.bgpVal += bgpVal;</span>
<span class="cstat-no" title="statement not covered" >            totals.shrVal = totals.shrVal += shrVal;</span>
<span class="cstat-no" title="statement not covered" >            totals.mrkVal = totals.mrkVal += mrkVal;</span>
<span class="cstat-no" title="statement not covered" >            totals.spVal = totals.spVal += spVal;</span>
<span class="cstat-no" title="statement not covered" >            totals.prvSpVal = totals.prvSpVal += prvSpVal;</span>
          });
          const spPct = <span class="cstat-no" title="statement not covered" >calculateSalesPublicPcnt(Number(targetvalue), totals.prvSpVal) || 0;</span>
          const bgpPct = <span class="cstat-no" title="statement not covered" >totals.bgpVal / totals.spVal;</span>
          const mrkPct = <span class="cstat-no" title="statement not covered" >totals.mrkVal / totals.spVal;</span>
          const shrPct = <span class="cstat-no" title="statement not covered" >totals.shrVal / totals.spVal;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          newValues.grossProfit = numberFormatter.format(totals.bgpVal);</span>
<span class="cstat-no" title="statement not covered" >          newValues.marksDown = numberFormatter.format(totals.mrkVal);</span>
<span class="cstat-no" title="statement not covered" >          newValues.totalShrink = numberFormatter.format(totals.shrVal);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (isPct === false) {</span>
<span class="cstat-no" title="statement not covered" >            newValues.salesPublicPct = numberFormatter.format(spPct * 100);</span>
          }
<span class="cstat-no" title="statement not covered" >          newValues.grossProfitPct = numberFormatter.format(bgpPct * 100);</span>
<span class="cstat-no" title="statement not covered" >          newValues.marksDownPct = numberFormatter.format(mrkPct * 100);</span>
<span class="cstat-no" title="statement not covered" >          newValues.totalShrinkPct = numberFormatter.format(shrPct * 100);</span>
        } else {
<span class="cstat-no" title="statement not covered" >          newValues.grossProfit = String('');</span>
<span class="cstat-no" title="statement not covered" >          newValues.totalShrink = String('');</span>
<span class="cstat-no" title="statement not covered" >          newValues.marksDown = String('');</span>
<span class="cstat-no" title="statement not covered" >          newValues.grossProfitPct = String('');</span>
<span class="cstat-no" title="statement not covered" >          newValues.totalShrinkPct = String('');</span>
<span class="cstat-no" title="statement not covered" >          newValues.marksDownPct = String('');</span>
        }
<span class="cstat-no" title="statement not covered" >        updateTotalValue(targetName, targetvalue);</span>
      }
      else <span class="cstat-no" title="statement not covered" >if (targetName === 'selling' || targetName === 'nonSelling') {</span>
        const sellingValue = <span class="cstat-no" title="statement not covered" >Number(newValues.selling || 0);</span>
        const nonSellingValue = <span class="cstat-no" title="statement not covered" >Number(newValues.nonSelling || 0);</span>
        const allowancesValue = <span class="cstat-no" title="statement not covered" >getAllowancesValue(sellingValue, nonSellingValue);</span>
<span class="cstat-no" title="statement not covered" >        newValues.allowances = numberFormatter.format(allowancesValue);</span>
      }
      else <span class="cstat-no" title="statement not covered" >if (targetName === 'grossProfit' || targetName === 'marksDown' || targetName === 'totalShrink') {</span>
<span class="cstat-no" title="statement not covered" >        if (isValidTargetValue(targetvalue)) {</span>
          const calculatedWeeks = <span class="cstat-no" title="statement not covered" >recalculateSelectedWeekPayload(formik.values.salesPublic);</span>
<span class="cstat-no" title="statement not covered" >          if (isPct) {</span>
            let totals = <span class="cstat-no" title="statement not covered" >0;</span>
<span class="cstat-no" title="statement not covered" >            calculatedWeeks.forEach(<span class="fstat-no" title="function not covered" >we</span>ek =&gt; {</span>
<span class="cstat-no" title="statement not covered" >              totals = totals + (week.newAggregatedData.line1PublicToSalesNbr * Number(e.target.value) / 100);</span>
            });
<span class="cstat-no" title="statement not covered" >            newValues[targetName] = numberFormatter.format(totals);</span>
          } else {
            const adjustedSalesPublic = <span class="cstat-no" title="statement not covered" >totalValue['salesPublic'];</span>
            const pctChange = <span class="cstat-no" title="statement not covered" >(Number(targetvalue)) / adjustedSalesPublic;</span>
<span class="cstat-no" title="statement not covered" >            newValues[`${targetName}Pct`] = numberFormatter.format(pctChange * 100);</span>
          }
        }
      }
<span class="cstat-no" title="statement not covered" >      return newValues;</span>
    });
  }
  /** This is a temporary method for the error banner since the icon is not proper on using the uds syntax
   * Below are the code used in uds directly
   */
  const renderErrorAlert = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(e</span>rror) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    return (</span>
      &lt;div role="alert" className="flex items-start rounded-lg p-4 border min-h-fit cursor-default bg-red-50 text-compliant-red border-red-300 w-[682px] edit-notifications" style={{ zIndex: 30, top: '76px' }}&gt;
&nbsp;
        &lt;svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="#9D2210" stroke="#FFF6F5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" className="min-w-[24px]"&gt;
          &lt;polygon points="7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2"&gt;&lt;/polygon&gt;
          &lt;line x1="12" y1="8" x2="12" y2="12"&gt;&lt;/line&gt;
          &lt;line x1="12" y1="16" x2="12.01" y2="16"&gt;&lt;/line&gt;
        &lt;/svg&gt;
&nbsp;
        &lt;div className="w-[calc(100%-80px)] mx-4"&gt;
          &lt;div className="text-left font-bold leading-6 block break-words"&gt;{error}&lt;/div&gt;
        &lt;/div&gt;
&nbsp;
      &lt;/div&gt;)
  }
&nbsp;
  const getWarningComponent = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(n</span>ame, stateWarnings) =&gt; {</span>
    const error = <span class="cstat-no" title="statement not covered" >stateWarnings?.[name] === DEVIATION_WARNING_MSG;</span>
    const subLabel = <span class="cstat-no" title="statement not covered" >error ? stateWarnings[name] : '';</span>
<span class="cstat-no" title="statement not covered" >    if (!error) <span class="cstat-no" title="statement not covered" >return null;</span></span>
<span class="cstat-no" title="statement not covered" >    return (</span>
      &lt;div&gt;
        &lt;Tooltip
          zIndex={2}
          anchor='top'
          variant='dark'
          label={`${subLabel}`}&gt;
            &lt;TriangleAlert color="#F0A92C" /&gt;
        &lt;/Tooltip&gt;
      &lt;/div&gt;
    )
  }
&nbsp;
  /** Resetting the form on Radio change / On Submitting / On closing the Edit forecast modal */
  const clearFormFields = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(r</span>adioSelection = <span class="branch-0 cbranch-no" title="branch not covered" >'singleweek')</span> =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    setTotalBaselineValue({</span>
      'salesPublic': 0,
      'grossProfit': 0,
      'marksDown': 0,
      'totalShrink': 0,
      'suppliesPackaging': 0,
      'allowances': 0,
      'selling': 0,
      'nonSelling': 0
    })
<span class="cstat-no" title="statement not covered" >    setTotalValue({</span>
      'salesPublic': 0,
      'grossProfit': 0,
      'marksDown': 0,
      'totalShrink': 0,
      'suppliesPackaging': 0,
      'allowances': 0,
      'selling': 0,
      'nonSelling': 0
    })
<span class="cstat-no" title="statement not covered" >    setWarnings({})</span>
<span class="cstat-no" title="statement not covered" >    formik.resetForm()</span>
<span class="cstat-no" title="statement not covered" >    formik.setFieldValue('salesPublicPct', '');</span>
<span class="cstat-no" title="statement not covered" >    formik.setFieldValue('marksDownPct', '');</span>
<span class="cstat-no" title="statement not covered" >    formik.setFieldValue('grossProfitPct', '');</span>
<span class="cstat-no" title="statement not covered" >    formik.setFieldValue('totalShrinkPct', '');</span>
    /** Making the radio selection back to Group weeks */
<span class="cstat-no" title="statement not covered" >    formik.setFieldValue('selectedRadio', radioSelection);</span>
<span class="cstat-no" title="statement not covered" >    setWeeksCalculated([]);</span>
  }
&nbsp;
  const saveAdjustmentValue = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >as</span>ync (postBody: Adjustment | undefined) =&gt; {</span>
    const isSaveAdjustmentSuccess = <span class="cstat-no" title="statement not covered" >await saveAdjustment(postBody, dispatch, saveAdjustmentEdits);</span>
<span class="cstat-no" title="statement not covered" >    if (isSaveAdjustmentSuccess) {</span>
<span class="cstat-no" title="statement not covered" >      setIsSaveApiLoading(false);</span>
<span class="cstat-no" title="statement not covered" >      setOpen(false);</span>
    } else {
<span class="cstat-no" title="statement not covered" >      setIsSaveApiLoading(false);</span>
    }
  }
&nbsp;
  const EditAdjustmentButtons = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(s</span>aveLoader: boolean) =&gt; (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="w-[421px] fixed bottom-0  bg-white col-span-2 flex justify-between gap-2  py-3  z-50 "&gt;</span>
        &lt;Button data-testid="save-adjustment-button" type="button" onClick={handleSubmit} className='save-button-width' disabled={saveLoader}&gt;
        {saveLoader ? &lt;Spinner id='save-spinner' variant='solid' size='xs' /&gt; : "Save adjustment"}
      &lt;/Button&gt;
        &lt;Link onClick={<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >openHistoryDrawer(true,"left")}</span> className="link-decoration" &gt; Audit History &lt;/Link&gt;
       &lt;HistoryDrawer
        isOpen={isHistoryOpen}
        setOpen={setIsHistoryOpen}
        position={historyPosition}
      /&gt;
     &lt;/div&gt;
  );
<span class="cstat-no" title="statement not covered" >  return (</span>
    &lt;Drawer
      anchor="right"
      isOpen={isOpen}
      setOpen={setOpen}
      hideBackdrop={false}
      header={
        &lt;div className="text-lg font-semibold overflow-hidden"&gt;
          Edit Adjustment
        &lt;/div&gt;
      }
    &gt;
      &lt;form className="edit-forecast"&gt;
        &lt;div className='grid grid-cols-2 gap-x-6 gap-y-2 '&gt;
&nbsp;
          &lt;div className='col-span-2'&gt;
            &lt;Radio.Group
              onChange={<span class="fstat-no" title="function not covered" >(v</span>al) =&gt; <span class="cstat-no" title="statement not covered" >clearFormFields(val)}</span>
              error={formik.errors.selectedRadio}
              value={formik.values.selectedRadio}
            &gt;
              &lt;Radio label='Enter for each week separately' value='singleweek' /&gt;
              &lt;Radio disabled label='Enter same values for selected weeks' value='groupweeks' /&gt;
            &lt;/Radio.Group&gt;
          &lt;/div&gt;
&nbsp;
          &lt;Divider orientation="horizontal" /&gt;
&nbsp;
          &lt;div className='col-span-2'&gt;
&nbsp;
            {/* Below line has to be replaced with the commented out uds code, once the uds component works on its own syntax */}
&nbsp;
            {((formik.values.selectedRadio === 'groupweeks' &amp;&amp; formik.submitCount &gt; 0) || (formik.values.selectedRadio === 'singleweek')) &amp;&amp; formik.errors.selectedWeeks &amp;&amp; renderErrorAlert(formik.errors.selectedWeeks)}
&nbsp;
&nbsp;
&nbsp;
            &lt;span className='text-sm font-bold whitespace-nowrap select-none truncate'&gt;
              Applied Weeks
            &lt;/span&gt;
&nbsp;
            &lt;WeekSelection
              selectedWeeks={formik.values.selectedWeeks}
              touchedWeeks={(Object.keys(formik.values.touchedWeeks || {}))}
              errorWeeks={Object.keys(formik.errors.weekData || {})}
              weekData={formik.values.weekData}
              onWeekSelect={<span class="fstat-no" title="function not covered" >(w</span>eeks) =&gt; {
                const prevWeek = <span class="cstat-no" title="statement not covered" >formik.values.selectedWeeks?.[0];</span>
                const nextWeek = <span class="cstat-no" title="statement not covered" >weeks?.[0];</span>
<span class="cstat-no" title="statement not covered" >                setActiveWeekSubmit(false)</span>
<span class="cstat-no" title="statement not covered" >                if (formik.values.selectedRadio === 'singleweek' &amp;&amp; prevWeek !== null &amp;&amp; prevWeek !== nextWeek) {</span>
<span class="cstat-no" title="statement not covered" >                  formik.validateForm();</span>
                  const weekData = <span class="cstat-no" title="statement not covered" >formik.values?.weekData?.[prevWeek];</span>
<span class="cstat-no" title="statement not covered" >                  if (weekData &amp;&amp; weekData?.touched?.size &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >                    formik.setFieldValue('touchedWeeks', {</span>
                      ...formik.values.touchedWeeks,
                      [prevWeek]: true
                    })
                  }
                }
<span class="cstat-no" title="statement not covered" >                if (JSON.stringify(formik.values.selectedWeeks) !== JSON.stringify(weeks)) {</span>
<span class="cstat-no" title="statement not covered" >                  formik.setFieldValue('selectedWeeks', weeks);</span>
                }
              }}
              isSingleWeekSelect={formik.values.selectedRadio === 'singleweek'}
              weeksInQuarter={weeksInQuarter}
              firstWeekNbr={Number(Object.keys(adjustmentWorksheetDataSlice)?.filter(<span class="fstat-no" title="function not covered" >ke</span>y =&gt; <span class="cstat-no" title="statement not covered" >key.includes("Week"))</span>[0]?.replace('Weeks', ''))}
            /&gt;
&nbsp;
          &lt;/div&gt;
&nbsp;
          {/********************* Group Weeks Form ****************************/}
          {formik.values.selectedRadio === 'groupweeks' &amp;&amp; &lt;&gt;
            &lt;div className='col-span-2'&gt;
&nbsp;
              &lt;Alert isOpen={true} sticky={false} variant='informational' className="edit-notifications" size="medium"&gt;
                &lt;div className="notification-banner-text"&gt;Your adjustment will be proportionally applied to each SMIC&lt;/div&gt;
              &lt;/Alert&gt;
&nbsp;
&nbsp;
              {/* Below line has to be replaced with the commented out uds code, once the uds component works on its own syntax */}
&nbsp;
              {formik.submitCount &gt; 0 &amp;&amp; formik.errors.metrics &amp;&amp; renderErrorAlert(formik.errors.metrics)}
&nbsp;
              {/* {formik.submitCount&gt;0 &amp;&amp; formik.errors.metrics &amp;&amp; &lt;Alert isOpen={true} sticky={false} variant='error' className="edit-notifications" size="medium"&gt;
                  &lt;div&gt; {formik.errors.metrics}&lt;/div&gt;
                &lt;/Alert&gt;} */}
&nbsp;
              &lt;div className="grid gap-3 mt-2 mb-6 text-black text-md"&gt;
                {formFields.map(<span class="fstat-no" title="function not covered" >({</span> label, name, inputType, subField }, index) =&gt; {
<span class="cstat-no" title="statement not covered" >                  return (</span>
                    &lt;React.Fragment key={index}&gt;
                      &lt;div
                        className={`grid grid-cols-3 items-center metrics ${subField ? 'ml-9 -mt-2' : ''
                          }`}
                      &gt;
                        &lt;div className="flex gap-2"&gt;
                          &lt;div className="w-fit h-fit inline-flex flex-col"&gt;
                            &lt;div className={`flex items-center font-nunito text-xs font-semibold leading-4 tracking-normal ${subField ? 'w-[100px]' : 'w-[135px]'} h-[32px] cursor-pointer`}&gt;
                              {label}
                            &lt;/div&gt;
                          &lt;/div&gt;
                          {renderInputs({
                            state: formik.values[name],
                            setter: <span class="fstat-no" title="function not covered" >(e</span>) =&gt; {
<span class="cstat-no" title="statement not covered" >                              multiWeekHandleChange(name, e);</span>
                            },
                            inputType,
                            name,
                            stateWarnings: warnings,
                            touched: formik.values.touched,
                          })}
                        &lt;/div&gt;
                      &lt;/div&gt;
                      {label === 'salesPublic' &amp;&amp; &lt;Divider orientation="horizontal" /&gt;}
                      {label === 'Total Shrink' &amp;&amp; &lt;Divider orientation="horizontal" /&gt;}
                      {label === 'Supplies Packaging' &amp;&amp; &lt;Divider orientation="horizontal" /&gt;}
                      {label === 'Non Selling' &amp;&amp; &lt;Divider orientation="horizontal" /&gt;}
                    &lt;/React.Fragment&gt;
                  );
                })}
              &lt;/div&gt;
            &lt;/div&gt;
&nbsp;
&nbsp;
            &lt;div className='col-span-2 relative'&gt;
              &lt;span className='text-sm font-bold whitespace-nowrap select-none truncate'&gt;
                Adjustment reason&lt;span className="text-red-500"&gt;*&lt;/span&gt;
              &lt;/span&gt;
              &lt;AutoComplete
                items={forecastAdjustmentReasons}
                itemKey="id"
                itemText="name"
                placeholder="Make a selection"
                width={410}
                size="md"
                menuHeight={250}
                error={formik.submitCount &gt; 0 &amp;&amp; formik.errors.adjustmentReason ? true : false}
                onChange={<span class="fstat-no" title="function not covered" >(v</span>al) =&gt; {
<span class="cstat-no" title="statement not covered" >                  formik.setFieldValue('adjustmentReason', val);</span>
                }}
                zIndex={100}
              /&gt;
              {formik.submitCount &gt; 0 &amp;&amp; formik.errors.adjustmentReason &amp;&amp; &lt;div ref={adjustmentErrorRef} className="edit-notifications-tex adjustment-error"&gt; {`Please enter an adjustment reason`}&lt;/div&gt;}
            &lt;/div&gt;
&nbsp;
            &lt;div className='col-span-2 pb-[70px]'&gt;
              &lt;span className='text-sm font-bold whitespace-nowrap select-none truncate'&gt;
                Comment&lt;span className="text-red-500"&gt;*&lt;/span&gt;
              &lt;/span&gt;
              &lt;TextArea
                name="comment"
                isRequired
                resize="resize-y"
                error={formik.submitCount &gt; 0 &amp;&amp; formik.errors.comment}
                onChange={<span class="fstat-no" title="function not covered" >(e</span>) =&gt; {
<span class="cstat-no" title="statement not covered" >                  formik.setFieldValue('comment', (e.target as HTMLInputElement).value);</span>
                }}
                maxCharacters={TEXT_AREA_MAX_CHAR}
                value={formik.values.comment}
                className={`w-full ${formik.submitCount &gt; 0 &amp;&amp; formik.errors.comment ? 'border-red-600' : ''}`}
                data-testid="Group comment"
                aria-label={'Group comment'}
                aria-describedby={'Group-weeks-comment-description'}
              /&gt;
            &lt;/div&gt;
            {EditAdjustmentButtons(isSaveApiLoading)}
          &lt;/&gt;
          }
&nbsp;
&nbsp;
          {/********************* Single Week Form ****************************/}
          {formik.values.selectedRadio === 'singleweek' &amp;&amp;
            formik.values.selectedWeeks.map(<span class="fstat-no" title="function not covered" >(w</span>eek) =&gt; (
<span class="cstat-no" title="statement not covered" >              &lt;div key={week} className='col-span-2'&gt;</span>
                &lt;div className='col-span-2'&gt;
&nbsp;
                  &lt;Alert isOpen={true} sticky={false} variant='informational' className="edit-notifications" size="medium"&gt;
                    &lt;div className="notification-banner-text"&gt;Your adjustment will be proportionally applied to each SMIC&lt;/div&gt;
                  &lt;/Alert&gt;
&nbsp;
&nbsp;
                  {(Object.keys(formik.values.touchedWeeks || {}).includes(String(week)) || activeWeekSubmit) &amp;&amp; (formik.errors.weekData as Record&lt;number, any&gt;)?.[week]?.metrics &amp;&amp; renderErrorAlert((formik.errors.weekData as Record&lt;number, any&gt;)?.[week]?.metrics)}
&nbsp;
                  {/* {(Object.keys(formik.values.touchedWeeks || {}).includes(String(week)) || activeWeekSubmit) &amp;&amp; (formik.errors.weekData as Record&lt;number, any&gt;)?.[week]?.metrics &amp;&amp; &lt;Alert isOpen={true} sticky={false} variant='error' className="edit-notifications" size="medium"&gt;
                        &lt;div&gt; {(formik.errors.weekData as Record&lt;number, any&gt;)?.[week]?.metrics}&lt;/div&gt;
                      &lt;/Alert&gt;} */}
&nbsp;
                  &lt;div className="grid gap-3 mt-2 mb-6 text-black text-md"&gt;
                    {formFields.map(<span class="fstat-no" title="function not covered" >({</span> label, name, inputType, subField, showWarning }, index) =&gt; {
<span class="cstat-no" title="statement not covered" >                      return (</span>
                        &lt;React.Fragment key={index}&gt;
                          &lt;div
                            className={`grid grid-cols-3 items-center metrics ${subField ? 'ml-9 -mt-2' : ''
                              }`}
                          &gt;
                            &lt;div className="flex gap-2"&gt;
                              &lt;div className={`${subField ? 'w-[100px]' : 'w-[140px]'} flex-none justify-between h-fit inline-flex flex-row items-center`}&gt;
                                &lt;div className={`flex items-center font-nunito text-xs font-semibold leading-4 tracking-normal h-[32px] cursor-pointer`}&gt;
                                  {label}
                                &lt;/div&gt;
                                {showWarning &amp;&amp; getWarningComponent(name, warnings.weekData?.[week])}
                              &lt;/div&gt;
                              {renderInputs({
                                state: formik.values.weekData?.[week]?.[name] || '',
                                setter: <span class="fstat-no" title="function not covered" >(e</span>) =&gt; { <span class="cstat-no" title="statement not covered" >singleWeekHandleChange(name, e, week) </span>},
                                inputType,
                                name,
                                stateWarnings: warnings.weekData?.[week],
                                touched: Object.keys(formik.values.touchedWeeks || {}).includes(String(week))
                              })}
                            &lt;/div&gt;
                          &lt;/div&gt;
                          {name === 'realGrossProfitNbr' &amp;&amp; &lt;Divider orientation="horizontal" /&gt;}
                          {label === 'Sales to Public' &amp;&amp; &lt;Divider orientation="horizontal" /&gt;}
                          {label === 'Total Shrink' &amp;&amp; &lt;Divider orientation="horizontal" /&gt;}
                          {label === 'Non Selling' &amp;&amp; &lt;Divider orientation="horizontal" /&gt;}
                          {label === 'Supplies Packaging' &amp;&amp; &lt;Divider orientation="horizontal" /&gt;}
                        &lt;/React.Fragment&gt;
                      );
                    })}
                  &lt;/div&gt;
                &lt;/div&gt;
&nbsp;
&nbsp;
                &lt;div className='col-span-2 relative'&gt;
                  &lt;span className='text-sm font-bold whitespace-nowrap select-none truncate'&gt;
                    Adjustment reason&lt;span className="text-red-500"&gt;*&lt;/span&gt;
                  &lt;/span&gt;
                  &lt;AutoComplete
                    items={forecastAdjustmentReasons}
                    itemKey="id"
                    size="md"
                    itemText="name"
                    placeholder="Make a selection"
                    width={410}
                    menuHeight={250}
                    error={(Object.keys(formik.values.touchedWeeks || {}).includes(String(week)) || activeWeekSubmit) &amp;&amp; (formik.errors.weekData as Record&lt;number, any&gt;)?.[week]?.adjustmentReason ? true : false}
                    onChange={<span class="fstat-no" title="function not covered" >(v</span>al) =&gt; { <span class="cstat-no" title="statement not covered" >formik.setFieldValue(`weekData.${week}.adjustmentReason`, val) </span>}}
                    value={formik.values.weekData?.[week]?.adjustmentReason}
                    zIndex={100}
                  /&gt;
                  {(Object.keys(formik.values.touchedWeeks || {}).includes(String(week)) || activeWeekSubmit) &amp;&amp; (formik.errors.weekData as Record&lt;number, any&gt;)?.[week]?.adjustmentReason &amp;&amp; &lt;div ref={adjustmentErrorRef} className="edit-notifications-text adjustment-error"&gt; {`Please enter an adjustment reason`}&lt;/div&gt;}
                &lt;/div&gt;
&nbsp;
                &lt;div className='col-span-2 pb-[70px]'&gt;
                  &lt;span className='text-sm font-bold whitespace-nowrap select-none truncate'&gt;
                    Comment&lt;span className="text-red-500"&gt;*&lt;/span&gt;
                  &lt;/span&gt;
                  &lt;TextArea
                    name={`comment-${week}`}
                    isRequired
                    resize="resize-y"
                    error={(Object.keys(formik.values.touchedWeeks || {}).includes(String(week)) || activeWeekSubmit) &amp;&amp; (formik.errors.weekData as Record&lt;number, any&gt;)?.[week]?.comment}
                    onChange={<span class="fstat-no" title="function not covered" >e </span>=&gt; {
<span class="cstat-no" title="statement not covered" >                      formik.setFieldValue(`weekData.${week}.comment`, (e.target as HTMLInputElement).value)</span>
                    }}
                    maxCharacters={TEXT_AREA_MAX_CHAR}
                    value={formik.values.weekData?.[week]?.comment}
                    className={`w-full h-[298] ${(Object.keys(formik.values.touchedWeeks || {}).includes(String(week)) || activeWeekSubmit) &amp;&amp; (formik.errors.weekData as Record&lt;number, any&gt;)?.[week]?.comment ? 'border-red-600' : ''}`}
                    data-testid={`Comment-${week}`}
                    aria-label={`Comment-${week}`}
                    aria-describedby={`Comment-${week}-description`}
                  /&gt;
                &lt;/div&gt;
                {EditAdjustmentButtons(isSaveApiLoading)}
              &lt;/div&gt;
            ))
          }
        &lt;/div&gt;
        &lt;HistoryDrawer
          isOpen={isHistoryOpen}
          setOpen={setIsHistoryOpen}
          position={historyPosition}
        /&gt;
      &lt;/form&gt;
    &lt;/Drawer&gt;
  );
};
&nbsp;
export default ForecastEdit;
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-17T06:34:15.200Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    