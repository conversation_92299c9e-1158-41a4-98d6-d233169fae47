
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/features</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/features</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">85.78% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>169/197</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">61.88% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>125/202</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">86.27% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>44/51</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">86.48% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>160/185</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="EPBCSSyncMonitor.tsx"><a href="EPBCSSyncMonitor.tsx.html">EPBCSSyncMonitor.tsx</a></td>
	<td data-value="95.91" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 95%"></div><div class="cover-empty" style="width: 5%"></div></div>
	</td>
	<td data-value="95.91" class="pct high">95.91%</td>
	<td data-value="49" class="abs high">47/49</td>
	<td data-value="95.23" class="pct high">95.23%</td>
	<td data-value="42" class="abs high">40/42</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="15" class="abs high">15/15</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="42" class="abs high">42/42</td>
	</tr>

<tr>
	<td class="file high" data-value="WorksheetHeaderControls.tsx"><a href="WorksheetHeaderControls.tsx.html">WorksheetHeaderControls.tsx</a></td>
	<td data-value="82.53" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 82%"></div><div class="cover-empty" style="width: 18%"></div></div>
	</td>
	<td data-value="82.53" class="pct high">82.53%</td>
	<td data-value="63" class="abs high">52/63</td>
	<td data-value="68.18" class="pct medium">68.18%</td>
	<td data-value="44" class="abs medium">30/44</td>
	<td data-value="83.33" class="pct high">83.33%</td>
	<td data-value="12" class="abs high">10/12</td>
	<td data-value="81.66" class="pct high">81.66%</td>
	<td data-value="60" class="abs high">49/60</td>
	</tr>

<tr>
	<td class="file low" data-value="calendarServiceUtils.ts"><a href="calendarServiceUtils.ts.html">calendarServiceUtils.ts</a></td>
	<td data-value="33.33" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 33%"></div><div class="cover-empty" style="width: 67%"></div></div>
	</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="6" class="abs low">2/6</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="3" class="abs low">1/3</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="6" class="abs low">2/6</td>
	</tr>

<tr>
	<td class="file high" data-value="historyDrawer.tsx"><a href="historyDrawer.tsx.html">historyDrawer.tsx</a></td>
	<td data-value="85.71" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 85%"></div><div class="cover-empty" style="width: 15%"></div></div>
	</td>
	<td data-value="85.71" class="pct high">85.71%</td>
	<td data-value="28" class="abs high">24/28</td>
	<td data-value="85" class="pct high">85%</td>
	<td data-value="20" class="abs high">17/20</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="85.71" class="pct high">85.71%</td>
	<td data-value="28" class="abs high">24/28</td>
	</tr>

<tr>
	<td class="file high" data-value="historyTimeline.tsx"><a href="historyTimeline.tsx.html">historyTimeline.tsx</a></td>
	<td data-value="86.27" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 86%"></div><div class="cover-empty" style="width: 14%"></div></div>
	</td>
	<td data-value="86.27" class="pct high">86.27%</td>
	<td data-value="51" class="abs high">44/51</td>
	<td data-value="39.58" class="pct low">39.58%</td>
	<td data-value="96" class="abs low">38/96</td>
	<td data-value="82.35" class="pct high">82.35%</td>
	<td data-value="17" class="abs high">14/17</td>
	<td data-value="87.75" class="pct high">87.75%</td>
	<td data-value="49" class="abs high">43/49</td>
	</tr>

<tr>
	<td class="file empty" data-value="menfpt-category.tsx"><a href="menfpt-category.tsx.html">menfpt-category.tsx</a></td>
	<td data-value="0" class="pic empty">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct empty">0%</td>
	<td data-value="0" class="abs empty">0/0</td>
	<td data-value="0" class="pct empty">0%</td>
	<td data-value="0" class="abs empty">0/0</td>
	<td data-value="0" class="pct empty">0%</td>
	<td data-value="0" class="abs empty">0/0</td>
	<td data-value="0" class="pct empty">0%</td>
	<td data-value="0" class="abs empty">0/0</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-17T06:07:00.681Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    