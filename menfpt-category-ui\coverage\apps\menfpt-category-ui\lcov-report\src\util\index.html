
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/util</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/util</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">62.3% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>81/130</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">64.28% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>54/84</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">54.16% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>13/24</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">62.4% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>78/125</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="apiEndpoints.ts"><a href="apiEndpoints.ts.html">apiEndpoints.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	</tr>

<tr>
	<td class="file low" data-value="authProvider.ts"><a href="authProvider.ts.html">authProvider.ts</a></td>
	<td data-value="20.83" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 20%"></div><div class="cover-empty" style="width: 80%"></div></div>
	</td>
	<td data-value="20.83" class="pct low">20.83%</td>
	<td data-value="24" class="abs low">5/24</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="20" class="pct low">20%</td>
	<td data-value="5" class="abs low">1/5</td>
	<td data-value="17.39" class="pct low">17.39%</td>
	<td data-value="23" class="abs low">4/23</td>
	</tr>

<tr>
	<td class="file low" data-value="dateUtils.ts"><a href="dateUtils.ts.html">dateUtils.ts</a></td>
	<td data-value="12.5" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 12%"></div><div class="cover-empty" style="width: 88%"></div></div>
	</td>
	<td data-value="12.5" class="pct low">12.5%</td>
	<td data-value="8" class="abs low">1/8</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="2" class="abs medium">1/2</td>
	<td data-value="12.5" class="pct low">12.5%</td>
	<td data-value="8" class="abs low">1/8</td>
	</tr>

<tr>
	<td class="file medium" data-value="envVarsManager.ts"><a href="envVarsManager.ts.html">envVarsManager.ts</a></td>
	<td data-value="54.54" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 54%"></div><div class="cover-empty" style="width: 46%"></div></div>
	</td>
	<td data-value="54.54" class="pct medium">54.54%</td>
	<td data-value="11" class="abs medium">6/11</td>
	<td data-value="35.71" class="pct low">35.71%</td>
	<td data-value="14" class="abs low">5/14</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="2" class="abs medium">1/2</td>
	<td data-value="54.54" class="pct medium">54.54%</td>
	<td data-value="11" class="abs medium">6/11</td>
	</tr>

<tr>
	<td class="file medium" data-value="filterUtils.ts"><a href="filterUtils.ts.html">filterUtils.ts</a></td>
	<td data-value="71.42" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 71%"></div><div class="cover-empty" style="width: 29%"></div></div>
	</td>
	<td data-value="71.42" class="pct medium">71.42%</td>
	<td data-value="21" class="abs medium">15/21</td>
	<td data-value="60.86" class="pct medium">60.86%</td>
	<td data-value="23" class="abs medium">14/23</td>
	<td data-value="42.85" class="pct low">42.85%</td>
	<td data-value="7" class="abs low">3/7</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="20" class="abs medium">15/20</td>
	</tr>

<tr>
	<td class="file low" data-value="getFiscalWeekNumber.ts"><a href="getFiscalWeekNumber.ts.html">getFiscalWeekNumber.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	</tr>

<tr>
	<td class="file high" data-value="routeConstants.ts"><a href="routeConstants.ts.html">routeConstants.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	</tr>

<tr>
	<td class="file high" data-value="timeUtils.ts"><a href="timeUtils.ts.html">timeUtils.ts</a></td>
	<td data-value="90" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 90%"></div><div class="cover-empty" style="width: 10%"></div></div>
	</td>
	<td data-value="90" class="pct high">90%</td>
	<td data-value="40" class="abs high">36/40</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="31" class="abs high">31/31</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="89.47" class="pct high">89.47%</td>
	<td data-value="38" class="abs high">34/38</td>
	</tr>

<tr>
	<td class="file high" data-value="useCleanupOnRouteChange.ts"><a href="useCleanupOnRouteChange.ts.html">useCleanupOnRouteChange.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">9/9</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">9/9</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-17T06:07:00.681Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    