
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/features/worksheetFilter/hooks</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> src/features/worksheetFilter/hooks</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">60% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>81/135</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">56.86% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>29/51</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">46.42% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>13/28</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">60.3% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>79/131</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="usePersistentSearchQuery.ts"><a href="usePersistentSearchQuery.ts.html">usePersistentSearchQuery.ts</a></td>
	<td data-value="41.66" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 41%"></div><div class="cover-empty" style="width: 59%"></div></div>
	</td>
	<td data-value="41.66" class="pct low">41.66%</td>
	<td data-value="12" class="abs low">5/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="5" class="abs low">0/5</td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="4" class="abs low">1/4</td>
	<td data-value="45.45" class="pct low">45.45%</td>
	<td data-value="11" class="abs low">5/11</td>
	</tr>

<tr>
	<td class="file medium" data-value="useWorksheetFilterState.ts"><a href="useWorksheetFilterState.ts.html">useWorksheetFilterState.ts</a></td>
	<td data-value="61.78" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 61%"></div><div class="cover-empty" style="width: 39%"></div></div>
	</td>
	<td data-value="61.78" class="pct medium">61.78%</td>
	<td data-value="123" class="abs medium">76/123</td>
	<td data-value="63.04" class="pct medium">63.04%</td>
	<td data-value="46" class="abs medium">29/46</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="24" class="abs medium">12/24</td>
	<td data-value="61.66" class="pct medium">61.66%</td>
	<td data-value="120" class="abs medium">74/120</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-17T06:07:00.681Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    