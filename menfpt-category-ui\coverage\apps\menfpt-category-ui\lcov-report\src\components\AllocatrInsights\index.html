
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/AllocatrInsights</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> src/components/AllocatrInsights</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">66.04% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>214/324</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">51.02% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>150/294</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">38.04% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>35/92</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">69.12% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>206/298</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="ActualUsedIndicator.tsx"><a href="ActualUsedIndicator.tsx.html">ActualUsedIndicator.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	</tr>

<tr>
	<td class="file low" data-value="AllocatrBannerRow.tsx"><a href="AllocatrBannerRow.tsx.html">AllocatrBannerRow.tsx</a></td>
	<td data-value="25" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 25%"></div><div class="cover-empty" style="width: 75%"></div></div>
	</td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="4" class="abs low">1/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="4" class="abs low">1/4</td>
	</tr>

<tr>
	<td class="file medium" data-value="AllocatrDepartmentRow.tsx"><a href="AllocatrDepartmentRow.tsx.html">AllocatrDepartmentRow.tsx</a></td>
	<td data-value="66.66" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 66%"></div><div class="cover-empty" style="width: 34%"></div></div>
	</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="3" class="abs medium">2/3</td>
	<td data-value="65" class="pct medium">65%</td>
	<td data-value="20" class="abs medium">13/20</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="2" class="abs medium">1/2</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="3" class="abs medium">2/3</td>
	</tr>

<tr>
	<td class="file medium" data-value="AllocatrDivisionRow.tsx"><a href="AllocatrDivisionRow.tsx.html">AllocatrDivisionRow.tsx</a></td>
	<td data-value="50" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 50%"></div><div class="cover-empty" style="width: 50%"></div></div>
	</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="4" class="abs medium">2/4</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="4" class="abs medium">2/4</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="3" class="abs low">1/3</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="4" class="abs medium">2/4</td>
	</tr>

<tr>
	<td class="file high" data-value="AllocatrInsights.tsx"><a href="AllocatrInsights.tsx.html">AllocatrInsights.tsx</a></td>
	<td data-value="89.43" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 89%"></div><div class="cover-empty" style="width: 11%"></div></div>
	</td>
	<td data-value="89.43" class="pct high">89.43%</td>
	<td data-value="123" class="abs high">110/123</td>
	<td data-value="63" class="pct medium">63%</td>
	<td data-value="100" class="abs medium">63/100</td>
	<td data-value="95.23" class="pct high">95.23%</td>
	<td data-value="21" class="abs high">20/21</td>
	<td data-value="91.3" class="pct high">91.3%</td>
	<td data-value="115" class="abs high">105/115</td>
	</tr>

<tr>
	<td class="file medium" data-value="AllocatrInsightsHelper.ts"><a href="AllocatrInsightsHelper.ts.html">AllocatrInsightsHelper.ts</a></td>
	<td data-value="66.66" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 66%"></div><div class="cover-empty" style="width: 34%"></div></div>
	</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="27" class="abs medium">18/27</td>
	<td data-value="88.88" class="pct high">88.88%</td>
	<td data-value="18" class="abs high">16/18</td>
	<td data-value="14.28" class="pct low">14.28%</td>
	<td data-value="7" class="abs low">1/7</td>
	<td data-value="65.21" class="pct medium">65.21%</td>
	<td data-value="23" class="abs medium">15/23</td>
	</tr>

<tr>
	<td class="file low" data-value="AllocatrInsightsTable.tsx"><a href="AllocatrInsightsTable.tsx.html">AllocatrInsightsTable.tsx</a></td>
	<td data-value="35.71" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 35%"></div><div class="cover-empty" style="width: 65%"></div></div>
	</td>
	<td data-value="35.71" class="pct low">35.71%</td>
	<td data-value="112" class="abs low">40/112</td>
	<td data-value="19.75" class="pct low">19.75%</td>
	<td data-value="81" class="abs low">16/81</td>
	<td data-value="6.66" class="pct low">6.66%</td>
	<td data-value="45" class="abs low">3/45</td>
	<td data-value="40.4" class="pct low">40.4%</td>
	<td data-value="99" class="abs low">40/99</td>
	</tr>

<tr>
	<td class="file high" data-value="AllocatrPeriodRow.tsx"><a href="AllocatrPeriodRow.tsx.html">AllocatrPeriodRow.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="61.53" class="pct medium">61.53%</td>
	<td data-value="13" class="abs medium">8/13</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	</tr>

<tr>
	<td class="file high" data-value="AllocatrTotalRow.tsx"><a href="AllocatrTotalRow.tsx.html">AllocatrTotalRow.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="4" class="abs medium">2/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	</tr>

<tr>
	<td class="file low" data-value="AllocatrWeekRow.tsx"><a href="AllocatrWeekRow.tsx.html">AllocatrWeekRow.tsx</a></td>
	<td data-value="33.33" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 33%"></div><div class="cover-empty" style="width: 67%"></div></div>
	</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="6" class="abs low">2/6</td>
	<td data-value="18.18" class="pct low">18.18%</td>
	<td data-value="11" class="abs low">2/11</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="3" class="abs low">1/3</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="6" class="abs low">2/6</td>
	</tr>

<tr>
	<td class="file high" data-value="Forecastvariance.tsx"><a href="Forecastvariance.tsx.html">Forecastvariance.tsx</a></td>
	<td data-value="83.78" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 83%"></div><div class="cover-empty" style="width: 17%"></div></div>
	</td>
	<td data-value="83.78" class="pct high">83.78%</td>
	<td data-value="37" class="abs high">31/37</td>
	<td data-value="71.05" class="pct medium">71.05%</td>
	<td data-value="38" class="abs medium">27/38</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="5" class="abs high">5/5</td>
	<td data-value="86.11" class="pct high">86.11%</td>
	<td data-value="36" class="abs high">31/36</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-17T06:07:00.681Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    