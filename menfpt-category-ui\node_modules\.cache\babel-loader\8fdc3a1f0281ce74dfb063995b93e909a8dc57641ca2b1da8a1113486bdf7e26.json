{"ast": null, "code": "import ExcelJS from 'exceljs';\nimport { saveAs } from 'file-saver';\nimport { toTitleCase } from '@ui/utils';\nimport { getParentHeaderRow, COMMON_HEADERS, VS_PROJECTION_HEADERS, VS_PROJECTION_DOLLAR_HEADERS, mapRow } from './DashboardDownloadExcelHelper';\nimport { applyPrintSettings } from './DashboardDownloadExcelPrint';\nexport const formatCurrency = value => {\n  if (value === null || value === undefined || value === '') return '';\n  const num = Number(value);\n  return isNaN(num) ? value : `$${num.toLocaleString('en-US', {\n    maximumFractionDigits: 0\n  })}`;\n};\nexport const getDeptName = (smicData, deptId, fallback) => {\n  const found = smicData.find(item => String(item.deptId).trim() === String(deptId).trim());\n  return toTitleCase((found == null ? void 0 : found.deptName) || fallback || '');\n};\nexport const getDivisionName = (smicData, divisionId, fallback) => {\n  const found = smicData.find(item => String(item.divisionId) === String(divisionId));\n  return found ? toTitleCase(found.divisionName || '') : toTitleCase(fallback || divisionId);\n};\nexport const getBannerName = (smicData, divisionId, bannerId, fallback) => {\n  const found = smicData.find(item => String(item.divisionId) === String(divisionId) && String(item.bannerId) === String(bannerId));\n  return found ? toTitleCase(found.bannerName || '') : toTitleCase(fallback || bannerId);\n};\nconst addDepartmentRows = (rows, dept, smicData, useWeekId = false) => {\n  var _dept$name, _dept$periods;\n  const quarter = dept.quarter || {};\n  const deptName = getDeptName(smicData, dept.id, (_dept$name = dept == null ? void 0 : dept.name) != null ? _dept$name : '');\n  const isTotal = dept.id === 'Total';\n  const baseRow = {\n    departmentName: isTotal ? dept.name || 'Total' : `${dept.id} - ${deptName}`\n  };\n  rows.push(mapRow(baseRow, quarter, formatCurrency, 'Quarter'));\n  const weeksByPeriod = {};\n  (dept.weeks || []).forEach(week => {\n    var _ref, _week$periodNumber;\n    const periodNum = (_ref = (_week$periodNumber = week.periodNumber) != null ? _week$periodNumber : week.periodNbr) != null ? _ref : '';\n    if (!weeksByPeriod[periodNum]) weeksByPeriod[periodNum] = [];\n    weeksByPeriod[periodNum].push(week);\n  });\n  (_dept$periods = dept.periods) == null || _dept$periods.forEach(period => {\n    var _ref2, _period$periodNumber;\n    const periodNum = (_ref2 = (_period$periodNumber = period.periodNumber) != null ? _period$periodNumber : period.periodNbr) != null ? _ref2 : '';\n    rows.push(mapRow(Object.assign({}, baseRow, {\n      departmentName: periodNum ? `Period ${parseInt(String(periodNum).slice(-2), 10)}` : 'Period'\n    }), period, formatCurrency, 'Period', periodNum));\n    const weeks = weeksByPeriod[periodNum] || [];\n    const sortedWeeks = weeks.slice().sort((a, b) => {\n      const aNum = typeof a.weekNumber === 'number' ? a.weekNumber : parseInt((a.weekNumber || '').slice(-2), 10);\n      const bNum = typeof b.weekNumber === 'number' ? b.weekNumber : parseInt((b.weekNumber || '').slice(-2), 10);\n      return aNum - bNum;\n    });\n    sortedWeeks.forEach(week => {\n      let weekNum = '--';\n      if (useWeekId && typeof week.id === 'string' && week.id.startsWith('Week-')) {\n        weekNum = String(parseInt(week.id.slice(-2), 10));\n      } else if (!useWeekId && typeof week.weekNumber === 'number') {\n        weekNum = String(week.weekNumber % 100);\n      } else if (!useWeekId && typeof week.weekNumber === 'string') {\n        weekNum = String(parseInt(week.weekNumber.slice(-2), 10));\n      }\n      rows.push(mapRow(Object.assign({}, baseRow, {\n        departmentName: `Week ${weekNum} (fiscal wk ${weekNum})`\n      }), week, formatCurrency, 'Week', '', String(weekNum)));\n    });\n  });\n};\nconst addRows = (rows, data, smicData, useWeekId = false) => {\n  // Handle the new structure with divisions and banners\n  if (data.divisions && Array.isArray(data.divisions)) {\n    // First, add the Total row data if it exists at the root level\n    if (data.id === 'Total' || data.name === 'Total' || data.quarter || data.periods || data.weeks) {\n      const divisionsCount = data.divisions ? data.divisions.length : 0;\n      const totalData = {\n        id: 'Total',\n        name: `Total of ${divisionsCount} Divisions${divisionsCount !== 1 ? 's' : ''}`,\n        quarter: data.quarter,\n        periods: data.periods || [],\n        weeks: data.weeks || []\n      };\n      addDepartmentRows(rows, totalData, smicData, useWeekId);\n    }\n\n    // Then, process divisions and banners\n    data.divisions.forEach(division => {\n      const divisionName = getDivisionName(smicData, division.id, division.name);\n      const divisionBaseRow = {\n        departmentName: `${division.id} - ${divisionName}`\n      };\n\n      // Add division quarter row\n      if (division.quarter) {\n        rows.push(mapRow(divisionBaseRow, division.quarter, formatCurrency, 'Quarter'));\n      }\n\n      // Process division-level periods and weeks if they exist\n      if (division.periods && Array.isArray(division.periods)) {\n        division.periods.forEach(period => {\n          var _ref3, _period$periodNumber2;\n          const periodNum = (_ref3 = (_period$periodNumber2 = period.periodNumber) != null ? _period$periodNumber2 : period.periodNbr) != null ? _ref3 : '';\n          rows.push(mapRow({\n            departmentName: periodNum ? `Period ${parseInt(String(periodNum).slice(-2), 10)}` : 'Period'\n          }, period, formatCurrency, 'Period', periodNum));\n        });\n      }\n      if (division.weeks && Array.isArray(division.weeks)) {\n        const sortedWeeks = division.weeks.slice().sort((a, b) => {\n          const aNum = typeof a.weekNumber === 'number' ? a.weekNumber : parseInt((a.weekNumber || '').slice(-2), 10);\n          const bNum = typeof b.weekNumber === 'number' ? b.weekNumber : parseInt((b.weekNumber || '').slice(-2), 10);\n          return aNum - bNum;\n        });\n        sortedWeeks.forEach(week => {\n          let weekNum = '--';\n          if (useWeekId && typeof week.id === 'string' && week.id.startsWith('Week-')) {\n            weekNum = String(parseInt(week.id.slice(-2), 10));\n          } else if (!useWeekId && typeof week.weekNumber === 'number') {\n            weekNum = String(week.weekNumber % 100);\n          } else if (!useWeekId && typeof week.weekNumber === 'string') {\n            weekNum = String(parseInt(week.weekNumber.slice(-2), 10));\n          }\n          rows.push(mapRow({\n            departmentName: `Week ${weekNum} (fiscal wk ${weekNum})`\n          }, week, formatCurrency, 'Week', '', String(weekNum)));\n        });\n      }\n\n      // Process banners within division\n      if (division.banners && Array.isArray(division.banners) && division.banners.length > 0) {\n        division.banners.forEach(banner => {\n          // Skip \"Default\" banners entirely - we don't want them in the Excel export\n          const isDefaultBanner = banner.id === '00' || banner.id === 'default' || banner.name && banner.name.toLowerCase().includes('default');\n          if (isDefaultBanner) {\n            // For default banners, process their departments directly at division level\n            // without showing the banner row itself\n            if (banner.departments && Array.isArray(banner.departments)) {\n              banner.departments.forEach(dept => {\n                addDepartmentRows(rows, dept, smicData, useWeekId);\n              });\n            }\n            return; // Skip the banner row but process its departments\n          }\n\n          // Check if non-default banner has valid data before displaying\n          const hasValidBannerData = banner.quarter && Object.keys(banner.quarter).length > 0 && Object.values(banner.quarter).some(val => val !== null && val !== undefined && val !== '') || banner.departments && banner.departments.length > 0 || banner.periods && banner.periods.length > 0 || banner.weeks && banner.weeks.length > 0;\n\n          // For non-default banners, process normally if they have valid data\n          if (hasValidBannerData) {\n            const bannerName = getBannerName(smicData, division.id, banner.id, banner.name);\n            const bannerBaseRow = {\n              departmentName: `  ${bannerName}`\n            };\n\n            // Add banner quarter row\n            if (banner.quarter) {\n              rows.push(mapRow(bannerBaseRow, banner.quarter, formatCurrency, 'Quarter'));\n            }\n\n            // Process departments within banner\n            if (banner.departments && Array.isArray(banner.departments)) {\n              banner.departments.forEach(dept => {\n                addDepartmentRows(rows, dept, smicData, useWeekId);\n              });\n            }\n          }\n        });\n      }\n    });\n  } else {\n    // Fallback to old structure for backward compatibility\n    addDepartmentRows(rows, data, smicData, useWeekId);\n  }\n};\nexport const styleWorksheet = worksheet => {\n  worksheet.getRow(2).eachCell((cell, colNumber) => {\n    if (colNumber !== 1) {\n      var _cell$value;\n      worksheet.getColumn(colNumber).width = Math.max(String((_cell$value = cell.value) != null ? _cell$value : '').length + 1, 16);\n    }\n  });\n  let maxA = 0;\n  worksheet.eachRow((row, rowNumber) => {\n    var _row$getCell$value;\n    const cellValue = String((_row$getCell$value = row.getCell(1).value) != null ? _row$getCell$value : '');\n    if (cellValue.length > maxA) maxA = cellValue.length;\n  });\n  worksheet.getColumn(1).width = Math.max(maxA + 2, 10);\n  const thinLightBlack = {\n    style: 'thin',\n    color: {\n      argb: 'FF222222'\n    }\n  };\n  const thinLightBlackBorder = {\n    top: thinLightBlack,\n    left: thinLightBlack,\n    bottom: thinLightBlack,\n    right: thinLightBlack\n  };\n  worksheet.eachRow(row => {\n    row.eachCell(cell => {\n      cell.border = thinLightBlackBorder;\n    });\n  });\n  const lightGrayFill = {\n    type: \"pattern\",\n    pattern: 'solid',\n    fgColor: {\n      argb: 'FFD3D3D3'\n    }\n  };\n  worksheet.getRow(1).eachCell(cell => {\n    cell.fill = lightGrayFill;\n    cell.font = {\n      bold: true\n    };\n    cell.alignment = {\n      vertical: 'middle',\n      horizontal: 'center'\n    };\n  });\n  worksheet.getRow(2).eachCell(cell => {\n    cell.font = {\n      bold: true\n    };\n    cell.alignment = {\n      vertical: 'middle',\n      horizontal: 'center'\n    };\n  });\n  worksheet.getCell('A2').fill = lightGrayFill;\n  worksheet.getCell('A2').font = {\n    bold: true\n  };\n  const lightBlueFill = {\n    type: 'pattern',\n    pattern: 'solid',\n    fgColor: {\n      argb: 'FFA8F1FF'\n    }\n  };\n  const highlightBlueFill = {\n    type: 'pattern',\n    pattern: 'solid',\n    fgColor: {\n      argb: 'FF6FE6FC'\n    }\n  };\n  worksheet.eachRow((row, rowNumber) => {\n    if (rowNumber >= 3) {\n      const firstCell = row.getCell(1).value;\n      if (typeof firstCell === 'string') {\n        const cellText = firstCell.trim();\n        if (cellText === 'Total' || /^[0-9]+ - /.test(cellText)) {\n          // Division and Department rows (including Total)\n          row.eachCell(cell => {\n            cell.fill = highlightBlueFill;\n          });\n        } else if (/^  [0-9]+ - /.test(cellText)) {\n          // Banner rows (indented with 2 spaces)\n          row.eachCell(cell => {\n            cell.fill = lightBlueFill;\n          });\n        } else if (/^Period\\b/.test(cellText)) {\n          // Period rows\n          row.eachCell(cell => {\n            cell.fill = lightBlueFill;\n          });\n        }\n      }\n    }\n  });\n  worksheet.getCell('A1').alignment = {\n    vertical: 'middle',\n    horizontal: 'center'\n  };\n  ['A1', 'A2'].forEach(cell => {\n    worksheet.getCell(cell).alignment = {\n      vertical: 'middle',\n      horizontal: 'center'\n    };\n  });\n  worksheet.getCell('A3').font = Object.assign({}, worksheet.getCell('A3').font, {\n    bold: true\n  });\n};\nexport const styleVsProjection = worksheet => {\n  const vsProjectionColIndices = [];\n  worksheet.getRow(2).eachCell((cell, colNumber) => {\n    if (VS_PROJECTION_HEADERS.includes(String(cell.value).trim())) vsProjectionColIndices.push(colNumber);\n  });\n  worksheet.eachRow((row, rowNumber) => {\n    if (rowNumber >= 3) {\n      vsProjectionColIndices.forEach(colIdx => {\n        const cell = row.getCell(colIdx);\n        let raw = typeof cell.value === 'string' ? cell.value.replace(/[\\$, %\\(\\)]/g, '').trim() : cell.value;\n        const num = Number(raw);\n        if (!isNaN(num) && raw !== '') {\n          cell.font = Object.assign({}, cell.font, {\n            color: {\n              argb: num < 0 ? 'FFFF0000' : 'FF008000'\n            }\n          });\n          const header = worksheet.getRow(2).getCell(colIdx).value;\n          if (VS_PROJECTION_DOLLAR_HEADERS.includes(String(header).trim())) {\n            if (num < 0) {\n              cell.value = `($${Math.abs(num).toLocaleString('en-US', {\n                maximumFractionDigits: 0\n              })})`;\n            } else {\n              cell.value = `$${num.toLocaleString('en-US', {\n                maximumFractionDigits: 0\n              })}`;\n            }\n          }\n        }\n      });\n    }\n  });\n};\nexport const handleDownloadExcel = async (dashboardData, smicData = [], appliedFilters, fileName = 'Dashboard Excel Download.xlsx') => {\n  var _dashboardData$divisi, _appliedFilters$timef, _appliedFilters$timef2;\n  // Handle both old array format and new object format\n  const hasData = Array.isArray(dashboardData) ? dashboardData.length > 0 : dashboardData && (((_dashboardData$divisi = dashboardData.divisions) == null ? void 0 : _dashboardData$divisi.length) > 0 || dashboardData.id);\n  if (!hasData) return alert('No dashboard data to export!');\n  let quarterNumber = appliedFilters == null || (_appliedFilters$timef = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef.quarter;\n  if (!quarterNumber) {\n    var _dashboardData$quarte, _dashboardData$divisi2, _dashboardData$divisi3;\n    if (Array.isArray(dashboardData) && dashboardData.length) {\n      var _dashboardData$, _dashboardData$2;\n      quarterNumber = ((_dashboardData$ = dashboardData[0]) == null || (_dashboardData$ = _dashboardData$.quarter) == null ? void 0 : _dashboardData$.quarterNumber) || ((_dashboardData$2 = dashboardData[0]) == null ? void 0 : _dashboardData$2.quarterNumber) || '';\n    } else if (dashboardData != null && (_dashboardData$quarte = dashboardData.quarter) != null && _dashboardData$quarte.quarterNumber) {\n      quarterNumber = dashboardData.quarter.quarterNumber;\n    } else if (dashboardData != null && (_dashboardData$divisi2 = dashboardData.divisions) != null && _dashboardData$divisi2.length && (_dashboardData$divisi3 = dashboardData.divisions[0]) != null && (_dashboardData$divisi3 = _dashboardData$divisi3.quarter) != null && _dashboardData$divisi3.quarterNumber) {\n      quarterNumber = dashboardData.divisions[0].quarter.quarterNumber;\n    }\n    if (typeof quarterNumber === 'number' || typeof quarterNumber === 'string') {\n      const qStr = String(quarterNumber);\n      if (qStr.length === 6) quarterNumber = Number(qStr.slice(4, 6));\n    }\n  }\n  const fiscalYear = (appliedFilters == null || (_appliedFilters$timef2 = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef2.fiscalYear) || '';\n  const quarterDisplay = `Q${quarterNumber} ${fiscalYear}`;\n  const parentHeaderRow = getParentHeaderRow(quarterDisplay);\n  const isVariance = fileName.toLowerCase().includes('variance');\n  const rows = [];\n\n  // Handle both old array format and new object format\n  if (Array.isArray(dashboardData)) {\n    dashboardData.forEach(dept => addRows(rows, dept, smicData, isVariance));\n  } else {\n    addRows(rows, dashboardData, smicData, isVariance);\n  }\n  const workbook = new ExcelJS.Workbook();\n  const worksheet = workbook.addWorksheet('Dashboard');\n  worksheet.addRow(parentHeaderRow);\n  worksheet.addRow(COMMON_HEADERS);\n  rows.forEach(row => worksheet.addRow(Object.values(row)));\n  const mergeRanges = ['A1:A2', 'B1:G1', 'H1:L1', 'M1:Q1', 'R1:V1', 'W1:AB1', 'AC1:AE1', 'AF1:AH1', 'AI1:AN1'];\n  mergeRanges.forEach(range => worksheet.mergeCells(range));\n  styleWorksheet(worksheet);\n  styleVsProjection(worksheet);\n  applyPrintSettings(worksheet);\n  let ySplit = 2;\n  const totalRows = worksheet.rowCount;\n  for (let i = 3; i <= totalRows; i++) {\n    var _worksheet$getRow$get;\n    const cellValue = String((_worksheet$getRow$get = worksheet.getRow(i).getCell(1).value) != null ? _worksheet$getRow$get : '');\n    if (/^\\d+ - /.test(cellValue.trim()) && i !== 3) {\n      ySplit = i - 1;\n      break;\n    }\n    if (i === totalRows) {\n      ySplit = totalRows;\n    }\n  }\n  worksheet.views = [{\n    state: 'frozen',\n    ySplit,\n    xSplit: 1\n  }];\n  const styledBuffer = await workbook.xlsx.writeBuffer();\n  saveAs(new Blob([styledBuffer]), fileName);\n};\nexport const handleDownloadBothExcel = async (performanceSummaryData, forecastVarianceData, smicData = [], appliedFilters) => {\n  var _performanceSummaryDa, _forecastVarianceData;\n  // Handle both old array format and new object format\n  const hasPerformanceData = Array.isArray(performanceSummaryData) ? performanceSummaryData.length > 0 : performanceSummaryData && (((_performanceSummaryDa = performanceSummaryData.divisions) == null ? void 0 : _performanceSummaryDa.length) > 0 || performanceSummaryData.id);\n  const hasForecastData = Array.isArray(forecastVarianceData) ? forecastVarianceData.length > 0 : forecastVarianceData && (((_forecastVarianceData = forecastVarianceData.divisions) == null ? void 0 : _forecastVarianceData.length) > 0 || forecastVarianceData.id);\n\n  // if (!hasPerformanceData && !hasForecastData) {\n  //   return alert('No dashboard data to export!');\n  // }\n\n  const workbook = new ExcelJS.Workbook();\n  if (hasPerformanceData) {\n    var _appliedFilters$timef3, _appliedFilters$timef4;\n    const worksheet1 = workbook.addWorksheet('Performance Summary');\n    const quarterNumber = (appliedFilters == null || (_appliedFilters$timef3 = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef3.quarter) || '';\n    const fiscalYear = (appliedFilters == null || (_appliedFilters$timef4 = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef4.fiscalYear) || '';\n    const quarterDisplay = `Q${quarterNumber} ${fiscalYear}`;\n    worksheet1.addRow(getParentHeaderRow(quarterDisplay));\n    worksheet1.addRow(COMMON_HEADERS);\n    const rows = [];\n    if (Array.isArray(performanceSummaryData)) {\n      performanceSummaryData.forEach(dept => addRows(rows, dept, smicData));\n    } else {\n      addRows(rows, performanceSummaryData, smicData);\n    }\n    rows.forEach(row => worksheet1.addRow(Object.values(row)));\n    styleWorksheet(worksheet1);\n    styleVsProjection(worksheet1);\n    applyPrintSettings(worksheet1);\n  }\n  if (hasForecastData) {\n    var _appliedFilters$timef5, _appliedFilters$timef6;\n    const worksheet2 = workbook.addWorksheet('Forecast Variance');\n    const quarterNumber = (appliedFilters == null || (_appliedFilters$timef5 = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef5.quarter) || '';\n    const fiscalYear = (appliedFilters == null || (_appliedFilters$timef6 = appliedFilters.timeframe) == null ? void 0 : _appliedFilters$timef6.fiscalYear) || '';\n    const quarterDisplay = `Q${quarterNumber} ${fiscalYear}`;\n    worksheet2.addRow(getParentHeaderRow(quarterDisplay));\n    worksheet2.addRow(COMMON_HEADERS);\n    const rows = [];\n    if (Array.isArray(forecastVarianceData)) {\n      forecastVarianceData.forEach(dept => addRows(rows, dept, smicData, true));\n    } else {\n      addRows(rows, forecastVarianceData, smicData, true);\n    }\n    rows.forEach(row => worksheet2.addRow(Object.values(row)));\n    styleWorksheet(worksheet2);\n    styleVsProjection(worksheet2);\n    applyPrintSettings(worksheet2);\n  }\n  const styledBuffer = await workbook.xlsx.writeBuffer();\n  saveAs(new Blob([styledBuffer]), 'Dashboard Excel Download.xlsx');\n};", "map": {"version": 3, "names": ["ExcelJS", "saveAs", "toTitleCase", "getParentHeaderRow", "COMMON_HEADERS", "VS_PROJECTION_HEADERS", "VS_PROJECTION_DOLLAR_HEADERS", "mapRow", "applyPrintSettings", "formatCurrency", "value", "undefined", "num", "Number", "isNaN", "toLocaleString", "maximumFractionDigits", "getDeptName", "smicData", "deptId", "fallback", "found", "find", "item", "String", "trim", "deptName", "getDivisionName", "divisionId", "divisionName", "getBannerName", "bannerId", "bannerName", "addDepartmentRows", "rows", "dept", "useWeekId", "_dept$name", "_dept$periods", "quarter", "id", "name", "isTotal", "baseRow", "departmentName", "push", "weeksByPeriod", "weeks", "for<PERSON>ach", "week", "_ref", "_week$periodNumber", "periodNum", "periodNumber", "periodNbr", "periods", "period", "_ref2", "_period$periodNumber", "Object", "assign", "parseInt", "slice", "sortedWeeks", "sort", "a", "b", "aNum", "weekNumber", "bNum", "weekNum", "startsWith", "addRows", "data", "divisions", "Array", "isArray", "divisionsCount", "length", "totalData", "division", "divisionBaseRow", "_ref3", "_period$periodNumber2", "banners", "banner", "isDefaultBanner", "toLowerCase", "includes", "departments", "hasValidBannerData", "keys", "values", "some", "val", "bannerBaseRow", "styleWorksheet", "worksheet", "getRow", "eachCell", "cell", "colNumber", "_cell$value", "getColumn", "width", "Math", "max", "maxA", "eachRow", "row", "rowNumber", "_row$getCell$value", "cellValue", "getCell", "thinLightBlack", "style", "color", "argb", "thinLightBlackBorder", "top", "left", "bottom", "right", "border", "lightGrayFill", "type", "pattern", "fgColor", "fill", "font", "bold", "alignment", "vertical", "horizontal", "lightBlueFill", "highlightBlueFill", "firstCell", "cellText", "test", "styleVsProjection", "vsProjectionColIndices", "colIdx", "raw", "replace", "header", "abs", "handleDownloadExcel", "dashboardData", "appliedFilters", "fileName", "_dashboardData$divisi", "_appliedFilters$timef", "_appliedFilters$timef2", "hasData", "alert", "quarterNumber", "timeframe", "_dashboardData$quarte", "_dashboardData$divisi2", "_dashboardData$divisi3", "_dashboardData$", "_dashboardData$2", "qStr", "fiscalYear", "quarterDisplay", "parentHeaderRow", "is<PERSON><PERSON>ce", "workbook", "Workbook", "addWorksheet", "addRow", "mergeRanges", "range", "mergeCells", "ySplit", "totalRows", "rowCount", "i", "_worksheet$getRow$get", "views", "state", "xSplit", "styledBuffer", "xlsx", "writeBuffer", "Blob", "handleDownloadBothExcel", "performanceSummaryData", "forecastVarianceData", "_performanceSummaryDa", "_forecastVarianceData", "hasPerformanceData", "hasForecastData", "_appliedFilters$timef3", "_appliedFilters$timef4", "worksheet1", "_appliedFilters$timef5", "_appliedFilters$timef6", "worksheet2"], "sources": ["C:/Users/<USER>/Desktop/NFPT/menfpt-category-ui/apps/menfpt-category-ui/src/components/DashboardDownloadExcel/DashboardDownloadExcel.tsx"], "sourcesContent": ["import ExcelJS from 'exceljs';\r\nimport { saveAs } from 'file-saver';\r\nimport { toTitleCase } from '@ui/utils';\r\nimport { getParentHeaderRow, COMMON_HEADERS, VS_PROJECTION_HEADERS, VS_PROJECTION_DOLLAR_HEADERS, mapRow } from './DashboardDownloadExcelHelper';\r\nimport { applyPrintSettings } from './DashboardDownloadExcelPrint';\r\n\r\nexport const formatCurrency = (value: any) => {\r\n  if (value === null || value === undefined || value === '') return '';\r\n  const num = Number(value);\r\n  return isNaN(num) ? value : `$${num.toLocaleString('en-US', { maximumFractionDigits: 0 })}`;\r\n};\r\nexport const getDeptName = (smicData: any[], deptId: string, fallback: string) => {\r\n  const found = smicData.find((item: any) => String(item.deptId).trim() === String(deptId).trim());\r\n  return toTitleCase(found?.deptName || fallback || '');\r\n};\r\n\r\nexport const getDivisionName = (smicData: any[], divisionId: string, fallback: string) => {\r\n  const found = smicData.find((item: any) => String(item.divisionId) === String(divisionId));\r\n  return found ? toTitleCase(found.divisionName || '') : toTitleCase(fallback || divisionId);\r\n};\r\n\r\nexport const getBannerName = (smicData: any[], divisionId: string, bannerId: string, fallback: string) => {\r\n  const found = smicData.find((item: any) => String(item.divisionId) === String(divisionId) && String(item.bannerId) === String(bannerId));\r\n  return found ? toTitleCase(found.bannerName || '') : toTitleCase(fallback || bannerId);\r\n};\r\nconst addDepartmentRows = (rows: any[], dept: any, smicData: any[], useWeekId: boolean = false) => {\r\n  const quarter = dept.quarter || {};\r\n  const deptName = getDeptName(smicData, dept.id, dept?.name ?? '');\r\n  const isTotal = dept.id === 'Total';\r\n  const baseRow = { departmentName: isTotal ? (dept.name || 'Total') : `${dept.id} - ${deptName}` };\r\n  rows.push(mapRow(baseRow, quarter, formatCurrency, 'Quarter'));\r\n\r\n  const weeksByPeriod: Record<string, any[]> = {};\r\n  (dept.weeks || []).forEach((week: any) => {\r\n    const periodNum = week.periodNumber ?? week.periodNbr ?? '';\r\n    if (!weeksByPeriod[periodNum]) weeksByPeriod[periodNum] = [];\r\n    weeksByPeriod[periodNum].push(week);\r\n  });\r\n\r\n  dept.periods?.forEach((period: any) => {\r\n    const periodNum = period.periodNumber ?? period.periodNbr ?? '';\r\n    rows.push(\r\n      mapRow(\r\n        { ...baseRow, departmentName: periodNum ? `Period ${parseInt(String(periodNum).slice(-2), 10)}` : 'Period' },\r\n        period,\r\n        formatCurrency,\r\n        'Period',\r\n        periodNum\r\n      )\r\n    );\r\n\r\n    const weeks = weeksByPeriod[periodNum] || [];\r\n    const sortedWeeks = weeks.slice().sort((a, b) => {\r\n      const aNum = typeof a.weekNumber === 'number' ? a.weekNumber : parseInt((a.weekNumber || '').slice(-2), 10);\r\n      const bNum = typeof b.weekNumber === 'number' ? b.weekNumber : parseInt((b.weekNumber || '').slice(-2), 10);\r\n      return aNum - bNum;\r\n    });\r\n    sortedWeeks.forEach((week: any) => {\r\n      let weekNum = '--';\r\n      if (useWeekId && typeof week.id === 'string' && week.id.startsWith('Week-')) {\r\n        weekNum = String(parseInt(week.id.slice(-2), 10));\r\n      } else if (!useWeekId && typeof week.weekNumber === 'number') {\r\n        weekNum = String(week.weekNumber % 100);\r\n      } else if (!useWeekId && typeof week.weekNumber === 'string') {\r\n        weekNum = String(parseInt(week.weekNumber.slice(-2), 10));\r\n      }\r\n      rows.push(\r\n        mapRow(\r\n          { ...baseRow, departmentName: `Week ${weekNum} (fiscal wk ${weekNum})` },\r\n          week,\r\n          formatCurrency,\r\n          'Week',\r\n          '',\r\n          String(weekNum)\r\n        )\r\n      );\r\n    });\r\n  });\r\n};\r\n\r\nconst addRows = (rows: any[], data: any, smicData: any[], useWeekId: boolean = false) => {\r\n  // Handle the new structure with divisions and banners\r\n  if (data.divisions && Array.isArray(data.divisions)) {\r\n    // First, add the Total row data if it exists at the root level\r\n    if (data.id === 'Total' || data.name === 'Total' || data.quarter || data.periods || data.weeks) {\r\n      const divisionsCount = data.divisions ? data.divisions.length : 0;\r\n      const totalData = {\r\n        id: 'Total',\r\n        name: `Total of ${divisionsCount} Divisions${divisionsCount !== 1 ? 's' : ''}`,\r\n        quarter: data.quarter,\r\n        periods: data.periods || [],\r\n        weeks: data.weeks || []\r\n      };\r\n      addDepartmentRows(rows, totalData, smicData, useWeekId);\r\n    }\r\n\r\n    // Then, process divisions and banners\r\n    data.divisions.forEach((division: any) => {\r\n      const divisionName = getDivisionName(smicData, division.id, division.name);\r\n      const divisionBaseRow = { departmentName: `${division.id} - ${divisionName}` };\r\n\r\n      // Add division quarter row\r\n      if (division.quarter) {\r\n        rows.push(mapRow(divisionBaseRow, division.quarter, formatCurrency, 'Quarter'));\r\n      }\r\n\r\n      // Process division-level periods and weeks if they exist\r\n      if (division.periods && Array.isArray(division.periods)) {\r\n        division.periods.forEach((period: any) => {\r\n          const periodNum = period.periodNumber ?? period.periodNbr ?? '';\r\n          rows.push(\r\n            mapRow(\r\n              { departmentName: periodNum ? `Period ${parseInt(String(periodNum).slice(-2), 10)}` : 'Period' },\r\n              period,\r\n              formatCurrency,\r\n              'Period',\r\n              periodNum\r\n            )\r\n          );\r\n        });\r\n      }\r\n\r\n      if (division.weeks && Array.isArray(division.weeks)) {\r\n        const sortedWeeks = division.weeks.slice().sort((a, b) => {\r\n          const aNum = typeof a.weekNumber === 'number' ? a.weekNumber : parseInt((a.weekNumber || '').slice(-2), 10);\r\n          const bNum = typeof b.weekNumber === 'number' ? b.weekNumber : parseInt((b.weekNumber || '').slice(-2), 10);\r\n          return aNum - bNum;\r\n        });\r\n        sortedWeeks.forEach((week: any) => {\r\n          let weekNum = '--';\r\n          if (useWeekId && typeof week.id === 'string' && week.id.startsWith('Week-')) {\r\n            weekNum = String(parseInt(week.id.slice(-2), 10));\r\n          } else if (!useWeekId && typeof week.weekNumber === 'number') {\r\n            weekNum = String(week.weekNumber % 100);\r\n          } else if (!useWeekId && typeof week.weekNumber === 'string') {\r\n            weekNum = String(parseInt(week.weekNumber.slice(-2), 10));\r\n          }\r\n          rows.push(\r\n            mapRow(\r\n              { departmentName: `Week ${weekNum} (fiscal wk ${weekNum})` },\r\n              week,\r\n              formatCurrency,\r\n              'Week',\r\n              '',\r\n              String(weekNum)\r\n            )\r\n          );\r\n        });\r\n      }\r\n\r\n      // Process banners within division\r\n      if (division.banners && Array.isArray(division.banners) && division.banners.length > 0) {\r\n        division.banners.forEach((banner: any) => {\r\n          // Skip \"Default\" banners entirely - we don't want them in the Excel export\r\n          const isDefaultBanner = banner.id === '00' ||\r\n                                 banner.id === 'default' ||\r\n                                 (banner.name && banner.name.toLowerCase().includes('default'));\r\n\r\n          if (isDefaultBanner) {\r\n            // For default banners, process their departments directly at division level\r\n            // without showing the banner row itself\r\n            if (banner.departments && Array.isArray(banner.departments)) {\r\n              banner.departments.forEach((dept: any) => {\r\n                addDepartmentRows(rows, dept, smicData, useWeekId);\r\n              });\r\n            }\r\n            return; // Skip the banner row but process its departments\r\n          }\r\n\r\n          // Check if non-default banner has valid data before displaying\r\n          const hasValidBannerData = (banner.quarter && Object.keys(banner.quarter).length > 0 &&\r\n                                     Object.values(banner.quarter).some(val => val !== null && val !== undefined && val !== '')) ||\r\n                                   (banner.departments && banner.departments.length > 0) ||\r\n                                   (banner.periods && banner.periods.length > 0) ||\r\n                                   (banner.weeks && banner.weeks.length > 0);\r\n\r\n          // For non-default banners, process normally if they have valid data\r\n          if (hasValidBannerData) {\r\n            const bannerName = getBannerName(smicData, division.id, banner.id, banner.name);\r\n            const bannerBaseRow = { departmentName: `  ${bannerName}` };\r\n\r\n            // Add banner quarter row\r\n            if (banner.quarter) {\r\n              rows.push(mapRow(bannerBaseRow, banner.quarter, formatCurrency, 'Quarter'));\r\n            }\r\n\r\n            // Process departments within banner\r\n            if (banner.departments && Array.isArray(banner.departments)) {\r\n              banner.departments.forEach((dept: any) => {\r\n                addDepartmentRows(rows, dept, smicData, useWeekId);\r\n              });\r\n            }\r\n          }\r\n        });\r\n      }\r\n    });\r\n  } else {\r\n    // Fallback to old structure for backward compatibility\r\n    addDepartmentRows(rows, data, smicData, useWeekId);\r\n  }\r\n};\r\nexport const styleWorksheet = worksheet => {\r\n  worksheet.getRow(2).eachCell((cell, colNumber) => {\r\n    if (colNumber !== 1) {\r\n      worksheet.getColumn(colNumber).width = Math.max(String(cell.value ?? '').length + 1, 16);\r\n    }\r\n  });\r\n  let maxA = 0;\r\n  worksheet.eachRow((row, rowNumber) => {\r\n    const cellValue = String(row.getCell(1).value ?? '');\r\n    if (cellValue.length > maxA) maxA = cellValue.length;\r\n  });\r\n  worksheet.getColumn(1).width = Math.max(maxA + 2, 10);\r\n\r\n  const thinLightBlack = { style: 'thin', color: { argb: 'FF222222' } };\r\n  const thinLightBlackBorder = {\r\n  top: thinLightBlack, left: thinLightBlack,bottom: thinLightBlack,right: thinLightBlack\r\n  };\r\n   worksheet.eachRow(row => {\r\n    row.eachCell(cell => {\r\n      cell.border = thinLightBlackBorder;\r\n    });\r\n  });\r\n  const lightGrayFill = { type: \"pattern\", pattern: 'solid', fgColor: { argb: 'FFD3D3D3' } };\r\n  worksheet.getRow(1).eachCell(cell => { \r\n    cell.fill = lightGrayFill; \r\n    cell.font = { bold: true }; \r\n    cell.alignment = { vertical: 'middle', horizontal: 'center' };\r\n  });\r\n  worksheet.getRow(2).eachCell(cell => { \r\n    cell.font = { bold: true }; \r\n    cell.alignment = { vertical: 'middle', horizontal: 'center' }; \r\n  });\r\n  worksheet.getCell('A2').fill = lightGrayFill; worksheet.getCell('A2').font = { bold: true };\r\n  const lightBlueFill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFA8F1FF' } };\r\n  const highlightBlueFill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF6FE6FC' } }; \r\n  worksheet.eachRow((row, rowNumber) => {\r\n    if (rowNumber >= 3) {\r\n      const firstCell = row.getCell(1).value;\r\n      if (typeof firstCell === 'string') {\r\n        const cellText = firstCell.trim();\r\n        if (cellText === 'Total' || /^[0-9]+ - /.test(cellText)) {\r\n          // Division and Department rows (including Total)\r\n          row.eachCell(cell => { cell.fill = highlightBlueFill; });\r\n        } else if (/^  [0-9]+ - /.test(cellText)) {\r\n          // Banner rows (indented with 2 spaces)\r\n          row.eachCell(cell => { cell.fill = lightBlueFill; });\r\n        } else if (/^Period\\b/.test(cellText)) {\r\n          // Period rows\r\n          row.eachCell(cell => { cell.fill = lightBlueFill; });\r\n        }\r\n      }\r\n    }\r\n  });\r\n  worksheet.getCell('A1').alignment = { vertical: 'middle', horizontal: 'center' };\r\n  ['A1', 'A2'].forEach(cell => {\r\n    worksheet.getCell(cell).alignment = { vertical: 'middle', horizontal: 'center' };\r\n  });worksheet.getCell('A3').font = { ...worksheet.getCell('A3').font, bold: true };\r\n}\r\nexport const styleVsProjection = (worksheet: ExcelJS.Worksheet) => {\r\n  const vsProjectionColIndices: number[] = [];\r\n  worksheet.getRow(2).eachCell((cell, colNumber) => {\r\n    if (VS_PROJECTION_HEADERS.includes(String(cell.value).trim())) vsProjectionColIndices.push(colNumber);\r\n  });\r\n  worksheet.eachRow((row, rowNumber) => {\r\n    if (rowNumber >= 3) {\r\n      vsProjectionColIndices.forEach(colIdx => {\r\n        const cell = row.getCell(colIdx);\r\n        let raw = typeof cell.value === 'string' ? cell.value.replace(/[\\$, %\\(\\)]/g, '').trim() : cell.value;\r\n        const num = Number(raw);\r\n        if (!isNaN(num) && raw !== '') {\r\n          cell.font = { ...cell.font, color: { argb: num < 0 ? 'FFFF0000' : 'FF008000' } };\r\n          const header = worksheet.getRow(2).getCell(colIdx).value;\r\n          if (VS_PROJECTION_DOLLAR_HEADERS.includes(String(header).trim())) {\r\n            if (num < 0) {\r\n              cell.value = `($${Math.abs(num).toLocaleString('en-US', { maximumFractionDigits: 0 })})`;\r\n            } else {\r\n              cell.value = `$${num.toLocaleString('en-US', { maximumFractionDigits: 0 })}`;\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n  });\r\n};\r\nexport const handleDownloadExcel = async (\r\n  dashboardData: any,\r\n  smicData: any[] = [],\r\n  appliedFilters?: any,\r\n  fileName: string = 'Dashboard Excel Download.xlsx'\r\n) => {\r\n  // Handle both old array format and new object format\r\n  const hasData = Array.isArray(dashboardData)\r\n    ? dashboardData.length > 0\r\n    : dashboardData && (dashboardData.divisions?.length > 0 || dashboardData.id);\r\n\r\n  if (!hasData) return alert('No dashboard data to export!');\r\n\r\n  let quarterNumber = appliedFilters?.timeframe?.quarter;\r\n  if (!quarterNumber) {\r\n    if (Array.isArray(dashboardData) && dashboardData.length) {\r\n      quarterNumber = dashboardData[0]?.quarter?.quarterNumber || dashboardData[0]?.quarterNumber || '';\r\n    } else if (dashboardData?.quarter?.quarterNumber) {\r\n      quarterNumber = dashboardData.quarter.quarterNumber;\r\n    } else if (dashboardData?.divisions?.length && dashboardData.divisions[0]?.quarter?.quarterNumber) {\r\n      quarterNumber = dashboardData.divisions[0].quarter.quarterNumber;\r\n    }\r\n    if (typeof quarterNumber === 'number' || typeof quarterNumber === 'string') {\r\n      const qStr = String(quarterNumber); if (qStr.length === 6) quarterNumber = Number(qStr.slice(4, 6));\r\n    }\r\n  }\r\n  const fiscalYear = appliedFilters?.timeframe?.fiscalYear || '';\r\n  const quarterDisplay = `Q${quarterNumber} ${fiscalYear}`;\r\n  const parentHeaderRow = getParentHeaderRow(quarterDisplay);\r\n  const isVariance = fileName.toLowerCase().includes('variance');\r\n  const rows: any[] = [];\r\n\r\n  // Handle both old array format and new object format\r\n  if (Array.isArray(dashboardData)) {\r\n    dashboardData.forEach(dept => addRows(rows, dept, smicData, isVariance));\r\n  } else {\r\n    addRows(rows, dashboardData, smicData, isVariance);\r\n  }\r\n\r\n  const workbook = new ExcelJS.Workbook();\r\n  const worksheet = workbook.addWorksheet('Dashboard');\r\n  worksheet.addRow(parentHeaderRow);\r\n  worksheet.addRow(COMMON_HEADERS);\r\n  rows.forEach(row => worksheet.addRow(Object.values(row)));\r\n  const mergeRanges = ['A1:A2', 'B1:G1', 'H1:L1', 'M1:Q1', 'R1:V1', 'W1:AB1', 'AC1:AE1', 'AF1:AH1', 'AI1:AN1'];\r\n  mergeRanges.forEach(range => worksheet.mergeCells(range));\r\n  styleWorksheet(worksheet);\r\n  styleVsProjection(worksheet);\r\n  applyPrintSettings(worksheet);\r\n  let ySplit = 2;\r\n  const totalRows = worksheet.rowCount;\r\n  for (let i = 3; i <= totalRows; i++) {\r\n    const cellValue = String(worksheet.getRow(i).getCell(1).value ?? '');\r\n    if (/^\\d+ - /.test(cellValue.trim()) && i !== 3) {\r\n      ySplit = i - 1;\r\n      break;\r\n    } if (i === totalRows) { ySplit = totalRows; } }\r\n  worksheet.views = [{ state: 'frozen', ySplit, xSplit: 1 }];\r\n  const styledBuffer = await workbook.xlsx.writeBuffer();\r\n  saveAs(new Blob([styledBuffer]), fileName);\r\n};\r\nexport const handleDownloadBothExcel = async (\r\n  performanceSummaryData: any,\r\n  forecastVarianceData: any,\r\n  smicData: any[] = [],\r\n  appliedFilters?: any\r\n) => {\r\n  // Handle both old array format and new object format\r\n  const hasPerformanceData = Array.isArray(performanceSummaryData)\r\n    ? performanceSummaryData.length > 0\r\n    : performanceSummaryData && (performanceSummaryData.divisions?.length > 0 || performanceSummaryData.id);\r\n\r\n  const hasForecastData = Array.isArray(forecastVarianceData)\r\n    ? forecastVarianceData.length > 0\r\n    : forecastVarianceData && (forecastVarianceData.divisions?.length > 0 || forecastVarianceData.id);\r\n\r\n  // if (!hasPerformanceData && !hasForecastData) {\r\n  //   return alert('No dashboard data to export!');\r\n  // }\r\n\r\n  const workbook = new ExcelJS.Workbook();\r\n  if (hasPerformanceData) {\r\n    const worksheet1 = workbook.addWorksheet('Performance Summary');\r\n    const quarterNumber = appliedFilters?.timeframe?.quarter || '';\r\n    const fiscalYear = appliedFilters?.timeframe?.fiscalYear || '';\r\n    const quarterDisplay = `Q${quarterNumber} ${fiscalYear}`;\r\n    worksheet1.addRow(getParentHeaderRow(quarterDisplay));\r\n    worksheet1.addRow(COMMON_HEADERS);\r\n\r\n    const rows: any[] = [];\r\n    if (Array.isArray(performanceSummaryData)) {\r\n      performanceSummaryData.forEach(dept => addRows(rows, dept, smicData));\r\n    } else {\r\n      addRows(rows, performanceSummaryData, smicData);\r\n    }\r\n    rows.forEach(row => worksheet1.addRow(Object.values(row)));\r\n\r\n    styleWorksheet(worksheet1);\r\n    styleVsProjection(worksheet1);\r\n    applyPrintSettings(worksheet1);\r\n  }\r\n  if (hasForecastData) {\r\n    const worksheet2 = workbook.addWorksheet('Forecast Variance');\r\n    const quarterNumber = appliedFilters?.timeframe?.quarter || '';\r\n    const fiscalYear = appliedFilters?.timeframe?.fiscalYear || '';\r\n    const quarterDisplay = `Q${quarterNumber} ${fiscalYear}`;\r\n    worksheet2.addRow(getParentHeaderRow(quarterDisplay));\r\n    worksheet2.addRow(COMMON_HEADERS);\r\n\r\n    const rows: any[] = [];\r\n    if (Array.isArray(forecastVarianceData)) {\r\n      forecastVarianceData.forEach(dept => addRows(rows, dept, smicData, true));\r\n    } else {\r\n      addRows(rows, forecastVarianceData, smicData, true);\r\n    }\r\n    rows.forEach(row => worksheet2.addRow(Object.values(row)));\r\n\r\n    styleWorksheet(worksheet2);\r\n    styleVsProjection(worksheet2);\r\n    applyPrintSettings(worksheet2);\r\n  }\r\n  const styledBuffer = await workbook.xlsx.writeBuffer();\r\n  saveAs(new Blob([styledBuffer]), 'Dashboard Excel Download.xlsx');\r\n};\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,SAAS;AAC7B,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,WAAW,QAAQ,WAAW;AACvC,SAASC,kBAAkB,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,4BAA4B,EAAEC,MAAM,QAAQ,gCAAgC;AAChJ,SAASC,kBAAkB,QAAQ,+BAA+B;AAElE,OAAO,MAAMC,cAAc,GAAIC,KAAU,IAAK;EAC5C,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,EAAE,EAAE,OAAO,EAAE;EACpE,MAAME,GAAG,GAAGC,MAAM,CAACH,KAAK,CAAC;EACzB,OAAOI,KAAK,CAACF,GAAG,CAAC,GAAGF,KAAK,GAAG,IAAIE,GAAG,CAACG,cAAc,CAAC,OAAO,EAAE;IAAEC,qBAAqB,EAAE;EAAE,CAAC,CAAC,EAAE;AAC7F,CAAC;AACD,OAAO,MAAMC,WAAW,GAAGA,CAACC,QAAe,EAAEC,MAAc,EAAEC,QAAgB,KAAK;EAChF,MAAMC,KAAK,GAAGH,QAAQ,CAACI,IAAI,CAAEC,IAAS,IAAKC,MAAM,CAACD,IAAI,CAACJ,MAAM,CAAC,CAACM,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACL,MAAM,CAAC,CAACM,IAAI,CAAC,CAAC,CAAC;EAChG,OAAOvB,WAAW,CAAC,CAAAmB,KAAK,oBAALA,KAAK,CAAEK,QAAQ,KAAIN,QAAQ,IAAI,EAAE,CAAC;AACvD,CAAC;AAED,OAAO,MAAMO,eAAe,GAAGA,CAACT,QAAe,EAAEU,UAAkB,EAAER,QAAgB,KAAK;EACxF,MAAMC,KAAK,GAAGH,QAAQ,CAACI,IAAI,CAAEC,IAAS,IAAKC,MAAM,CAACD,IAAI,CAACK,UAAU,CAAC,KAAKJ,MAAM,CAACI,UAAU,CAAC,CAAC;EAC1F,OAAOP,KAAK,GAAGnB,WAAW,CAACmB,KAAK,CAACQ,YAAY,IAAI,EAAE,CAAC,GAAG3B,WAAW,CAACkB,QAAQ,IAAIQ,UAAU,CAAC;AAC5F,CAAC;AAED,OAAO,MAAME,aAAa,GAAGA,CAACZ,QAAe,EAAEU,UAAkB,EAAEG,QAAgB,EAAEX,QAAgB,KAAK;EACxG,MAAMC,KAAK,GAAGH,QAAQ,CAACI,IAAI,CAAEC,IAAS,IAAKC,MAAM,CAACD,IAAI,CAACK,UAAU,CAAC,KAAKJ,MAAM,CAACI,UAAU,CAAC,IAAIJ,MAAM,CAACD,IAAI,CAACQ,QAAQ,CAAC,KAAKP,MAAM,CAACO,QAAQ,CAAC,CAAC;EACxI,OAAOV,KAAK,GAAGnB,WAAW,CAACmB,KAAK,CAACW,UAAU,IAAI,EAAE,CAAC,GAAG9B,WAAW,CAACkB,QAAQ,IAAIW,QAAQ,CAAC;AACxF,CAAC;AACD,MAAME,iBAAiB,GAAGA,CAACC,IAAW,EAAEC,IAAS,EAAEjB,QAAe,EAAEkB,SAAkB,GAAG,KAAK,KAAK;EAAA,IAAAC,UAAA,EAAAC,aAAA;EACjG,MAAMC,OAAO,GAAGJ,IAAI,CAACI,OAAO,IAAI,CAAC,CAAC;EAClC,MAAMb,QAAQ,GAAGT,WAAW,CAACC,QAAQ,EAAEiB,IAAI,CAACK,EAAE,GAAAH,UAAA,GAAEF,IAAI,oBAAJA,IAAI,CAAEM,IAAI,YAAAJ,UAAA,GAAI,EAAE,CAAC;EACjE,MAAMK,OAAO,GAAGP,IAAI,CAACK,EAAE,KAAK,OAAO;EACnC,MAAMG,OAAO,GAAG;IAAEC,cAAc,EAAEF,OAAO,GAAIP,IAAI,CAACM,IAAI,IAAI,OAAO,GAAI,GAAGN,IAAI,CAACK,EAAE,MAAMd,QAAQ;EAAG,CAAC;EACjGQ,IAAI,CAACW,IAAI,CAACtC,MAAM,CAACoC,OAAO,EAAEJ,OAAO,EAAE9B,cAAc,EAAE,SAAS,CAAC,CAAC;EAE9D,MAAMqC,aAAoC,GAAG,CAAC,CAAC;EAC/C,CAACX,IAAI,CAACY,KAAK,IAAI,EAAE,EAAEC,OAAO,CAAEC,IAAS,IAAK;IAAA,IAAAC,IAAA,EAAAC,kBAAA;IACxC,MAAMC,SAAS,IAAAF,IAAA,IAAAC,kBAAA,GAAGF,IAAI,CAACI,YAAY,YAAAF,kBAAA,GAAIF,IAAI,CAACK,SAAS,YAAAJ,IAAA,GAAI,EAAE;IAC3D,IAAI,CAACJ,aAAa,CAACM,SAAS,CAAC,EAAEN,aAAa,CAACM,SAAS,CAAC,GAAG,EAAE;IAC5DN,aAAa,CAACM,SAAS,CAAC,CAACP,IAAI,CAACI,IAAI,CAAC;EACrC,CAAC,CAAC;EAEF,CAAAX,aAAA,GAAAH,IAAI,CAACoB,OAAO,aAAZjB,aAAA,CAAcU,OAAO,CAAEQ,MAAW,IAAK;IAAA,IAAAC,KAAA,EAAAC,oBAAA;IACrC,MAAMN,SAAS,IAAAK,KAAA,IAAAC,oBAAA,GAAGF,MAAM,CAACH,YAAY,YAAAK,oBAAA,GAAIF,MAAM,CAACF,SAAS,YAAAG,KAAA,GAAI,EAAE;IAC/DvB,IAAI,CAACW,IAAI,CACPtC,MAAM,CAAAoD,MAAA,CAAAC,MAAA,KACCjB,OAAO;MAAEC,cAAc,EAAEQ,SAAS,GAAG,UAAUS,QAAQ,CAACrC,MAAM,CAAC4B,SAAS,CAAC,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG;IAAQ,IAC1GN,MAAM,EACN/C,cAAc,EACd,QAAQ,EACR2C,SACF,CACF,CAAC;IAED,MAAML,KAAK,GAAGD,aAAa,CAACM,SAAS,CAAC,IAAI,EAAE;IAC5C,MAAMW,WAAW,GAAGhB,KAAK,CAACe,KAAK,CAAC,CAAC,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC/C,MAAMC,IAAI,GAAG,OAAOF,CAAC,CAACG,UAAU,KAAK,QAAQ,GAAGH,CAAC,CAACG,UAAU,GAAGP,QAAQ,CAAC,CAACI,CAAC,CAACG,UAAU,IAAI,EAAE,EAAEN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC3G,MAAMO,IAAI,GAAG,OAAOH,CAAC,CAACE,UAAU,KAAK,QAAQ,GAAGF,CAAC,CAACE,UAAU,GAAGP,QAAQ,CAAC,CAACK,CAAC,CAACE,UAAU,IAAI,EAAE,EAAEN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC3G,OAAOK,IAAI,GAAGE,IAAI;IACpB,CAAC,CAAC;IACFN,WAAW,CAACf,OAAO,CAAEC,IAAS,IAAK;MACjC,IAAIqB,OAAO,GAAG,IAAI;MAClB,IAAIlC,SAAS,IAAI,OAAOa,IAAI,CAACT,EAAE,KAAK,QAAQ,IAAIS,IAAI,CAACT,EAAE,CAAC+B,UAAU,CAAC,OAAO,CAAC,EAAE;QAC3ED,OAAO,GAAG9C,MAAM,CAACqC,QAAQ,CAACZ,IAAI,CAACT,EAAE,CAACsB,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACnD,CAAC,MAAM,IAAI,CAAC1B,SAAS,IAAI,OAAOa,IAAI,CAACmB,UAAU,KAAK,QAAQ,EAAE;QAC5DE,OAAO,GAAG9C,MAAM,CAACyB,IAAI,CAACmB,UAAU,GAAG,GAAG,CAAC;MACzC,CAAC,MAAM,IAAI,CAAChC,SAAS,IAAI,OAAOa,IAAI,CAACmB,UAAU,KAAK,QAAQ,EAAE;QAC5DE,OAAO,GAAG9C,MAAM,CAACqC,QAAQ,CAACZ,IAAI,CAACmB,UAAU,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAC3D;MACA5B,IAAI,CAACW,IAAI,CACPtC,MAAM,CAAAoD,MAAA,CAAAC,MAAA,KACCjB,OAAO;QAAEC,cAAc,EAAE,QAAQ0B,OAAO,eAAeA,OAAO;MAAG,IACtErB,IAAI,EACJxC,cAAc,EACd,MAAM,EACN,EAAE,EACFe,MAAM,CAAC8C,OAAO,CAChB,CACF,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AAED,MAAME,OAAO,GAAGA,CAACtC,IAAW,EAAEuC,IAAS,EAAEvD,QAAe,EAAEkB,SAAkB,GAAG,KAAK,KAAK;EACvF;EACA,IAAIqC,IAAI,CAACC,SAAS,IAAIC,KAAK,CAACC,OAAO,CAACH,IAAI,CAACC,SAAS,CAAC,EAAE;IACnD;IACA,IAAID,IAAI,CAACjC,EAAE,KAAK,OAAO,IAAIiC,IAAI,CAAChC,IAAI,KAAK,OAAO,IAAIgC,IAAI,CAAClC,OAAO,IAAIkC,IAAI,CAAClB,OAAO,IAAIkB,IAAI,CAAC1B,KAAK,EAAE;MAC9F,MAAM8B,cAAc,GAAGJ,IAAI,CAACC,SAAS,GAAGD,IAAI,CAACC,SAAS,CAACI,MAAM,GAAG,CAAC;MACjE,MAAMC,SAAS,GAAG;QAChBvC,EAAE,EAAE,OAAO;QACXC,IAAI,EAAE,YAAYoC,cAAc,aAAaA,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;QAC9EtC,OAAO,EAAEkC,IAAI,CAAClC,OAAO;QACrBgB,OAAO,EAAEkB,IAAI,CAAClB,OAAO,IAAI,EAAE;QAC3BR,KAAK,EAAE0B,IAAI,CAAC1B,KAAK,IAAI;MACvB,CAAC;MACDd,iBAAiB,CAACC,IAAI,EAAE6C,SAAS,EAAE7D,QAAQ,EAAEkB,SAAS,CAAC;IACzD;;IAEA;IACAqC,IAAI,CAACC,SAAS,CAAC1B,OAAO,CAAEgC,QAAa,IAAK;MACxC,MAAMnD,YAAY,GAAGF,eAAe,CAACT,QAAQ,EAAE8D,QAAQ,CAACxC,EAAE,EAAEwC,QAAQ,CAACvC,IAAI,CAAC;MAC1E,MAAMwC,eAAe,GAAG;QAAErC,cAAc,EAAE,GAAGoC,QAAQ,CAACxC,EAAE,MAAMX,YAAY;MAAG,CAAC;;MAE9E;MACA,IAAImD,QAAQ,CAACzC,OAAO,EAAE;QACpBL,IAAI,CAACW,IAAI,CAACtC,MAAM,CAAC0E,eAAe,EAAED,QAAQ,CAACzC,OAAO,EAAE9B,cAAc,EAAE,SAAS,CAAC,CAAC;MACjF;;MAEA;MACA,IAAIuE,QAAQ,CAACzB,OAAO,IAAIoB,KAAK,CAACC,OAAO,CAACI,QAAQ,CAACzB,OAAO,CAAC,EAAE;QACvDyB,QAAQ,CAACzB,OAAO,CAACP,OAAO,CAAEQ,MAAW,IAAK;UAAA,IAAA0B,KAAA,EAAAC,qBAAA;UACxC,MAAM/B,SAAS,IAAA8B,KAAA,IAAAC,qBAAA,GAAG3B,MAAM,CAACH,YAAY,YAAA8B,qBAAA,GAAI3B,MAAM,CAACF,SAAS,YAAA4B,KAAA,GAAI,EAAE;UAC/DhD,IAAI,CAACW,IAAI,CACPtC,MAAM,CACJ;YAAEqC,cAAc,EAAEQ,SAAS,GAAG,UAAUS,QAAQ,CAACrC,MAAM,CAAC4B,SAAS,CAAC,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG;UAAS,CAAC,EAChGN,MAAM,EACN/C,cAAc,EACd,QAAQ,EACR2C,SACF,CACF,CAAC;QACH,CAAC,CAAC;MACJ;MAEA,IAAI4B,QAAQ,CAACjC,KAAK,IAAI4B,KAAK,CAACC,OAAO,CAACI,QAAQ,CAACjC,KAAK,CAAC,EAAE;QACnD,MAAMgB,WAAW,GAAGiB,QAAQ,CAACjC,KAAK,CAACe,KAAK,CAAC,CAAC,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UACxD,MAAMC,IAAI,GAAG,OAAOF,CAAC,CAACG,UAAU,KAAK,QAAQ,GAAGH,CAAC,CAACG,UAAU,GAAGP,QAAQ,CAAC,CAACI,CAAC,CAACG,UAAU,IAAI,EAAE,EAAEN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UAC3G,MAAMO,IAAI,GAAG,OAAOH,CAAC,CAACE,UAAU,KAAK,QAAQ,GAAGF,CAAC,CAACE,UAAU,GAAGP,QAAQ,CAAC,CAACK,CAAC,CAACE,UAAU,IAAI,EAAE,EAAEN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UAC3G,OAAOK,IAAI,GAAGE,IAAI;QACpB,CAAC,CAAC;QACFN,WAAW,CAACf,OAAO,CAAEC,IAAS,IAAK;UACjC,IAAIqB,OAAO,GAAG,IAAI;UAClB,IAAIlC,SAAS,IAAI,OAAOa,IAAI,CAACT,EAAE,KAAK,QAAQ,IAAIS,IAAI,CAACT,EAAE,CAAC+B,UAAU,CAAC,OAAO,CAAC,EAAE;YAC3ED,OAAO,GAAG9C,MAAM,CAACqC,QAAQ,CAACZ,IAAI,CAACT,EAAE,CAACsB,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;UACnD,CAAC,MAAM,IAAI,CAAC1B,SAAS,IAAI,OAAOa,IAAI,CAACmB,UAAU,KAAK,QAAQ,EAAE;YAC5DE,OAAO,GAAG9C,MAAM,CAACyB,IAAI,CAACmB,UAAU,GAAG,GAAG,CAAC;UACzC,CAAC,MAAM,IAAI,CAAChC,SAAS,IAAI,OAAOa,IAAI,CAACmB,UAAU,KAAK,QAAQ,EAAE;YAC5DE,OAAO,GAAG9C,MAAM,CAACqC,QAAQ,CAACZ,IAAI,CAACmB,UAAU,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;UAC3D;UACA5B,IAAI,CAACW,IAAI,CACPtC,MAAM,CACJ;YAAEqC,cAAc,EAAE,QAAQ0B,OAAO,eAAeA,OAAO;UAAI,CAAC,EAC5DrB,IAAI,EACJxC,cAAc,EACd,MAAM,EACN,EAAE,EACFe,MAAM,CAAC8C,OAAO,CAChB,CACF,CAAC;QACH,CAAC,CAAC;MACJ;;MAEA;MACA,IAAIU,QAAQ,CAACI,OAAO,IAAIT,KAAK,CAACC,OAAO,CAACI,QAAQ,CAACI,OAAO,CAAC,IAAIJ,QAAQ,CAACI,OAAO,CAACN,MAAM,GAAG,CAAC,EAAE;QACtFE,QAAQ,CAACI,OAAO,CAACpC,OAAO,CAAEqC,MAAW,IAAK;UACxC;UACA,MAAMC,eAAe,GAAGD,MAAM,CAAC7C,EAAE,KAAK,IAAI,IACnB6C,MAAM,CAAC7C,EAAE,KAAK,SAAS,IACtB6C,MAAM,CAAC5C,IAAI,IAAI4C,MAAM,CAAC5C,IAAI,CAAC8C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,SAAS,CAAE;UAErF,IAAIF,eAAe,EAAE;YACnB;YACA;YACA,IAAID,MAAM,CAACI,WAAW,IAAId,KAAK,CAACC,OAAO,CAACS,MAAM,CAACI,WAAW,CAAC,EAAE;cAC3DJ,MAAM,CAACI,WAAW,CAACzC,OAAO,CAAEb,IAAS,IAAK;gBACxCF,iBAAiB,CAACC,IAAI,EAAEC,IAAI,EAAEjB,QAAQ,EAAEkB,SAAS,CAAC;cACpD,CAAC,CAAC;YACJ;YACA,OAAO,CAAC;UACV;;UAEA;UACA,MAAMsD,kBAAkB,GAAIL,MAAM,CAAC9C,OAAO,IAAIoB,MAAM,CAACgC,IAAI,CAACN,MAAM,CAAC9C,OAAO,CAAC,CAACuC,MAAM,GAAG,CAAC,IACzDnB,MAAM,CAACiC,MAAM,CAACP,MAAM,CAAC9C,OAAO,CAAC,CAACsD,IAAI,CAACC,GAAG,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKnF,SAAS,IAAImF,GAAG,KAAK,EAAE,CAAC,IAC3FT,MAAM,CAACI,WAAW,IAAIJ,MAAM,CAACI,WAAW,CAACX,MAAM,GAAG,CAAE,IACpDO,MAAM,CAAC9B,OAAO,IAAI8B,MAAM,CAAC9B,OAAO,CAACuB,MAAM,GAAG,CAAE,IAC5CO,MAAM,CAACtC,KAAK,IAAIsC,MAAM,CAACtC,KAAK,CAAC+B,MAAM,GAAG,CAAE;;UAElE;UACA,IAAIY,kBAAkB,EAAE;YACtB,MAAM1D,UAAU,GAAGF,aAAa,CAACZ,QAAQ,EAAE8D,QAAQ,CAACxC,EAAE,EAAE6C,MAAM,CAAC7C,EAAE,EAAE6C,MAAM,CAAC5C,IAAI,CAAC;YAC/E,MAAMsD,aAAa,GAAG;cAAEnD,cAAc,EAAE,KAAKZ,UAAU;YAAG,CAAC;;YAE3D;YACA,IAAIqD,MAAM,CAAC9C,OAAO,EAAE;cAClBL,IAAI,CAACW,IAAI,CAACtC,MAAM,CAACwF,aAAa,EAAEV,MAAM,CAAC9C,OAAO,EAAE9B,cAAc,EAAE,SAAS,CAAC,CAAC;YAC7E;;YAEA;YACA,IAAI4E,MAAM,CAACI,WAAW,IAAId,KAAK,CAACC,OAAO,CAACS,MAAM,CAACI,WAAW,CAAC,EAAE;cAC3DJ,MAAM,CAACI,WAAW,CAACzC,OAAO,CAAEb,IAAS,IAAK;gBACxCF,iBAAiB,CAACC,IAAI,EAAEC,IAAI,EAAEjB,QAAQ,EAAEkB,SAAS,CAAC;cACpD,CAAC,CAAC;YACJ;UACF;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC,MAAM;IACL;IACAH,iBAAiB,CAACC,IAAI,EAAEuC,IAAI,EAAEvD,QAAQ,EAAEkB,SAAS,CAAC;EACpD;AACF,CAAC;AACD,OAAO,MAAM4D,cAAc,GAAGC,SAAS,IAAI;EACzCA,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAACC,IAAI,EAAEC,SAAS,KAAK;IAChD,IAAIA,SAAS,KAAK,CAAC,EAAE;MAAA,IAAAC,WAAA;MACnBL,SAAS,CAACM,SAAS,CAACF,SAAS,CAAC,CAACG,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAClF,MAAM,EAAA8E,WAAA,GAACF,IAAI,CAAC1F,KAAK,YAAA4F,WAAA,GAAI,EAAE,CAAC,CAACxB,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC;IAC1F;EACF,CAAC,CAAC;EACF,IAAI6B,IAAI,GAAG,CAAC;EACZV,SAAS,CAACW,OAAO,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAK;IAAA,IAAAC,kBAAA;IACpC,MAAMC,SAAS,GAAGxF,MAAM,EAAAuF,kBAAA,GAACF,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAACvG,KAAK,YAAAqG,kBAAA,GAAI,EAAE,CAAC;IACpD,IAAIC,SAAS,CAAClC,MAAM,GAAG6B,IAAI,EAAEA,IAAI,GAAGK,SAAS,CAAClC,MAAM;EACtD,CAAC,CAAC;EACFmB,SAAS,CAACM,SAAS,CAAC,CAAC,CAAC,CAACC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACC,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC;EAErD,MAAMO,cAAc,GAAG;IAAEC,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAW;EAAE,CAAC;EACrE,MAAMC,oBAAoB,GAAG;IAC7BC,GAAG,EAAEL,cAAc;IAAEM,IAAI,EAAEN,cAAc;IAACO,MAAM,EAAEP,cAAc;IAACQ,KAAK,EAAER;EACxE,CAAC;EACAjB,SAAS,CAACW,OAAO,CAACC,GAAG,IAAI;IACxBA,GAAG,CAACV,QAAQ,CAACC,IAAI,IAAI;MACnBA,IAAI,CAACuB,MAAM,GAAGL,oBAAoB;IACpC,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMM,aAAa,GAAG;IAAEC,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,OAAO;IAAEC,OAAO,EAAE;MAAEV,IAAI,EAAE;IAAW;EAAE,CAAC;EAC1FpB,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACC,IAAI,IAAI;IACnCA,IAAI,CAAC4B,IAAI,GAAGJ,aAAa;IACzBxB,IAAI,CAAC6B,IAAI,GAAG;MAAEC,IAAI,EAAE;IAAK,CAAC;IAC1B9B,IAAI,CAAC+B,SAAS,GAAG;MAAEC,QAAQ,EAAE,QAAQ;MAAEC,UAAU,EAAE;IAAS,CAAC;EAC/D,CAAC,CAAC;EACFpC,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACC,IAAI,IAAI;IACnCA,IAAI,CAAC6B,IAAI,GAAG;MAAEC,IAAI,EAAE;IAAK,CAAC;IAC1B9B,IAAI,CAAC+B,SAAS,GAAG;MAAEC,QAAQ,EAAE,QAAQ;MAAEC,UAAU,EAAE;IAAS,CAAC;EAC/D,CAAC,CAAC;EACFpC,SAAS,CAACgB,OAAO,CAAC,IAAI,CAAC,CAACe,IAAI,GAAGJ,aAAa;EAAE3B,SAAS,CAACgB,OAAO,CAAC,IAAI,CAAC,CAACgB,IAAI,GAAG;IAAEC,IAAI,EAAE;EAAK,CAAC;EAC3F,MAAMI,aAAa,GAAG;IAAET,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,OAAO;IAAEC,OAAO,EAAE;MAAEV,IAAI,EAAE;IAAW;EAAE,CAAC;EAC1F,MAAMkB,iBAAiB,GAAG;IAAEV,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,OAAO;IAAEC,OAAO,EAAE;MAAEV,IAAI,EAAE;IAAW;EAAE,CAAC;EAC9FpB,SAAS,CAACW,OAAO,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAK;IACpC,IAAIA,SAAS,IAAI,CAAC,EAAE;MAClB,MAAM0B,SAAS,GAAG3B,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAACvG,KAAK;MACtC,IAAI,OAAO8H,SAAS,KAAK,QAAQ,EAAE;QACjC,MAAMC,QAAQ,GAAGD,SAAS,CAAC/G,IAAI,CAAC,CAAC;QACjC,IAAIgH,QAAQ,KAAK,OAAO,IAAI,YAAY,CAACC,IAAI,CAACD,QAAQ,CAAC,EAAE;UACvD;UACA5B,GAAG,CAACV,QAAQ,CAACC,IAAI,IAAI;YAAEA,IAAI,CAAC4B,IAAI,GAAGO,iBAAiB;UAAE,CAAC,CAAC;QAC1D,CAAC,MAAM,IAAI,cAAc,CAACG,IAAI,CAACD,QAAQ,CAAC,EAAE;UACxC;UACA5B,GAAG,CAACV,QAAQ,CAACC,IAAI,IAAI;YAAEA,IAAI,CAAC4B,IAAI,GAAGM,aAAa;UAAE,CAAC,CAAC;QACtD,CAAC,MAAM,IAAI,WAAW,CAACI,IAAI,CAACD,QAAQ,CAAC,EAAE;UACrC;UACA5B,GAAG,CAACV,QAAQ,CAACC,IAAI,IAAI;YAAEA,IAAI,CAAC4B,IAAI,GAAGM,aAAa;UAAE,CAAC,CAAC;QACtD;MACF;IACF;EACF,CAAC,CAAC;EACFrC,SAAS,CAACgB,OAAO,CAAC,IAAI,CAAC,CAACkB,SAAS,GAAG;IAAEC,QAAQ,EAAE,QAAQ;IAAEC,UAAU,EAAE;EAAS,CAAC;EAChF,CAAC,IAAI,EAAE,IAAI,CAAC,CAACrF,OAAO,CAACoD,IAAI,IAAI;IAC3BH,SAAS,CAACgB,OAAO,CAACb,IAAI,CAAC,CAAC+B,SAAS,GAAG;MAAEC,QAAQ,EAAE,QAAQ;MAAEC,UAAU,EAAE;IAAS,CAAC;EAClF,CAAC,CAAC;EAACpC,SAAS,CAACgB,OAAO,CAAC,IAAI,CAAC,CAACgB,IAAI,GAAAtE,MAAA,CAAAC,MAAA,KAAQqC,SAAS,CAACgB,OAAO,CAAC,IAAI,CAAC,CAACgB,IAAI;IAAEC,IAAI,EAAE;EAAI,EAAE;AACnF,CAAC;AACD,OAAO,MAAMS,iBAAiB,GAAI1C,SAA4B,IAAK;EACjE,MAAM2C,sBAAgC,GAAG,EAAE;EAC3C3C,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAACC,IAAI,EAAEC,SAAS,KAAK;IAChD,IAAIhG,qBAAqB,CAACmF,QAAQ,CAAChE,MAAM,CAAC4E,IAAI,CAAC1F,KAAK,CAAC,CAACe,IAAI,CAAC,CAAC,CAAC,EAAEmH,sBAAsB,CAAC/F,IAAI,CAACwD,SAAS,CAAC;EACvG,CAAC,CAAC;EACFJ,SAAS,CAACW,OAAO,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAK;IACpC,IAAIA,SAAS,IAAI,CAAC,EAAE;MAClB8B,sBAAsB,CAAC5F,OAAO,CAAC6F,MAAM,IAAI;QACvC,MAAMzC,IAAI,GAAGS,GAAG,CAACI,OAAO,CAAC4B,MAAM,CAAC;QAChC,IAAIC,GAAG,GAAG,OAAO1C,IAAI,CAAC1F,KAAK,KAAK,QAAQ,GAAG0F,IAAI,CAAC1F,KAAK,CAACqI,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAACtH,IAAI,CAAC,CAAC,GAAG2E,IAAI,CAAC1F,KAAK;QACrG,MAAME,GAAG,GAAGC,MAAM,CAACiI,GAAG,CAAC;QACvB,IAAI,CAAChI,KAAK,CAACF,GAAG,CAAC,IAAIkI,GAAG,KAAK,EAAE,EAAE;UAC7B1C,IAAI,CAAC6B,IAAI,GAAAtE,MAAA,CAAAC,MAAA,KAAQwC,IAAI,CAAC6B,IAAI;YAAEb,KAAK,EAAE;cAAEC,IAAI,EAAEzG,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG;YAAW;UAAC,EAAE;UAChF,MAAMoI,MAAM,GAAG/C,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACe,OAAO,CAAC4B,MAAM,CAAC,CAACnI,KAAK;UACxD,IAAIJ,4BAA4B,CAACkF,QAAQ,CAAChE,MAAM,CAACwH,MAAM,CAAC,CAACvH,IAAI,CAAC,CAAC,CAAC,EAAE;YAChE,IAAIb,GAAG,GAAG,CAAC,EAAE;cACXwF,IAAI,CAAC1F,KAAK,GAAG,KAAK+F,IAAI,CAACwC,GAAG,CAACrI,GAAG,CAAC,CAACG,cAAc,CAAC,OAAO,EAAE;gBAAEC,qBAAqB,EAAE;cAAE,CAAC,CAAC,GAAG;YAC1F,CAAC,MAAM;cACLoF,IAAI,CAAC1F,KAAK,GAAG,IAAIE,GAAG,CAACG,cAAc,CAAC,OAAO,EAAE;gBAAEC,qBAAqB,EAAE;cAAE,CAAC,CAAC,EAAE;YAC9E;UACF;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAMkI,mBAAmB,GAAG,MAAAA,CACjCC,aAAkB,EAClBjI,QAAe,GAAG,EAAE,EACpBkI,cAAoB,EACpBC,QAAgB,GAAG,+BAA+B,KAC/C;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACH;EACA,MAAMC,OAAO,GAAG9E,KAAK,CAACC,OAAO,CAACuE,aAAa,CAAC,GACxCA,aAAa,CAACrE,MAAM,GAAG,CAAC,GACxBqE,aAAa,KAAK,EAAAG,qBAAA,GAAAH,aAAa,CAACzE,SAAS,qBAAvB4E,qBAAA,CAAyBxE,MAAM,IAAG,CAAC,IAAIqE,aAAa,CAAC3G,EAAE,CAAC;EAE9E,IAAI,CAACiH,OAAO,EAAE,OAAOC,KAAK,CAAC,8BAA8B,CAAC;EAE1D,IAAIC,aAAa,GAAGP,cAAc,aAAAG,qBAAA,GAAdH,cAAc,CAAEQ,SAAS,qBAAzBL,qBAAA,CAA2BhH,OAAO;EACtD,IAAI,CAACoH,aAAa,EAAE;IAAA,IAAAE,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAClB,IAAIpF,KAAK,CAACC,OAAO,CAACuE,aAAa,CAAC,IAAIA,aAAa,CAACrE,MAAM,EAAE;MAAA,IAAAkF,eAAA,EAAAC,gBAAA;MACxDN,aAAa,GAAG,EAAAK,eAAA,GAAAb,aAAa,CAAC,CAAC,CAAC,cAAAa,eAAA,GAAhBA,eAAA,CAAkBzH,OAAO,qBAAzByH,eAAA,CAA2BL,aAAa,OAAAM,gBAAA,GAAId,aAAa,CAAC,CAAC,CAAC,qBAAhBc,gBAAA,CAAkBN,aAAa,KAAI,EAAE;IACnG,CAAC,MAAM,IAAIR,aAAa,aAAAU,qBAAA,GAAbV,aAAa,CAAE5G,OAAO,aAAtBsH,qBAAA,CAAwBF,aAAa,EAAE;MAChDA,aAAa,GAAGR,aAAa,CAAC5G,OAAO,CAACoH,aAAa;IACrD,CAAC,MAAM,IAAIR,aAAa,aAAAW,sBAAA,GAAbX,aAAa,CAAEzE,SAAS,aAAxBoF,sBAAA,CAA0BhF,MAAM,KAAAiF,sBAAA,GAAIZ,aAAa,CAACzE,SAAS,CAAC,CAAC,CAAC,cAAAqF,sBAAA,GAA1BA,sBAAA,CAA4BxH,OAAO,aAAnCwH,sBAAA,CAAqCJ,aAAa,EAAE;MACjGA,aAAa,GAAGR,aAAa,CAACzE,SAAS,CAAC,CAAC,CAAC,CAACnC,OAAO,CAACoH,aAAa;IAClE;IACA,IAAI,OAAOA,aAAa,KAAK,QAAQ,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;MAC1E,MAAMO,IAAI,GAAG1I,MAAM,CAACmI,aAAa,CAAC;MAAE,IAAIO,IAAI,CAACpF,MAAM,KAAK,CAAC,EAAE6E,aAAa,GAAG9I,MAAM,CAACqJ,IAAI,CAACpG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrG;EACF;EACA,MAAMqG,UAAU,GAAG,CAAAf,cAAc,aAAAI,sBAAA,GAAdJ,cAAc,CAAEQ,SAAS,qBAAzBJ,sBAAA,CAA2BW,UAAU,KAAI,EAAE;EAC9D,MAAMC,cAAc,GAAG,IAAIT,aAAa,IAAIQ,UAAU,EAAE;EACxD,MAAME,eAAe,GAAGlK,kBAAkB,CAACiK,cAAc,CAAC;EAC1D,MAAME,UAAU,GAAGjB,QAAQ,CAAC9D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,UAAU,CAAC;EAC9D,MAAMtD,IAAW,GAAG,EAAE;;EAEtB;EACA,IAAIyC,KAAK,CAACC,OAAO,CAACuE,aAAa,CAAC,EAAE;IAChCA,aAAa,CAACnG,OAAO,CAACb,IAAI,IAAIqC,OAAO,CAACtC,IAAI,EAAEC,IAAI,EAAEjB,QAAQ,EAAEoJ,UAAU,CAAC,CAAC;EAC1E,CAAC,MAAM;IACL9F,OAAO,CAACtC,IAAI,EAAEiH,aAAa,EAAEjI,QAAQ,EAAEoJ,UAAU,CAAC;EACpD;EAEA,MAAMC,QAAQ,GAAG,IAAIvK,OAAO,CAACwK,QAAQ,CAAC,CAAC;EACvC,MAAMvE,SAAS,GAAGsE,QAAQ,CAACE,YAAY,CAAC,WAAW,CAAC;EACpDxE,SAAS,CAACyE,MAAM,CAACL,eAAe,CAAC;EACjCpE,SAAS,CAACyE,MAAM,CAACtK,cAAc,CAAC;EAChC8B,IAAI,CAACc,OAAO,CAAC6D,GAAG,IAAIZ,SAAS,CAACyE,MAAM,CAAC/G,MAAM,CAACiC,MAAM,CAACiB,GAAG,CAAC,CAAC,CAAC;EACzD,MAAM8D,WAAW,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAC5GA,WAAW,CAAC3H,OAAO,CAAC4H,KAAK,IAAI3E,SAAS,CAAC4E,UAAU,CAACD,KAAK,CAAC,CAAC;EACzD5E,cAAc,CAACC,SAAS,CAAC;EACzB0C,iBAAiB,CAAC1C,SAAS,CAAC;EAC5BzF,kBAAkB,CAACyF,SAAS,CAAC;EAC7B,IAAI6E,MAAM,GAAG,CAAC;EACd,MAAMC,SAAS,GAAG9E,SAAS,CAAC+E,QAAQ;EACpC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIF,SAAS,EAAEE,CAAC,EAAE,EAAE;IAAA,IAAAC,qBAAA;IACnC,MAAMlE,SAAS,GAAGxF,MAAM,EAAA0J,qBAAA,GAACjF,SAAS,CAACC,MAAM,CAAC+E,CAAC,CAAC,CAAChE,OAAO,CAAC,CAAC,CAAC,CAACvG,KAAK,YAAAwK,qBAAA,GAAI,EAAE,CAAC;IACpE,IAAI,SAAS,CAACxC,IAAI,CAAC1B,SAAS,CAACvF,IAAI,CAAC,CAAC,CAAC,IAAIwJ,CAAC,KAAK,CAAC,EAAE;MAC/CH,MAAM,GAAGG,CAAC,GAAG,CAAC;MACd;IACF;IAAE,IAAIA,CAAC,KAAKF,SAAS,EAAE;MAAED,MAAM,GAAGC,SAAS;IAAE;EAAE;EACjD9E,SAAS,CAACkF,KAAK,GAAG,CAAC;IAAEC,KAAK,EAAE,QAAQ;IAAEN,MAAM;IAAEO,MAAM,EAAE;EAAE,CAAC,CAAC;EAC1D,MAAMC,YAAY,GAAG,MAAMf,QAAQ,CAACgB,IAAI,CAACC,WAAW,CAAC,CAAC;EACtDvL,MAAM,CAAC,IAAIwL,IAAI,CAAC,CAACH,YAAY,CAAC,CAAC,EAAEjC,QAAQ,CAAC;AAC5C,CAAC;AACD,OAAO,MAAMqC,uBAAuB,GAAG,MAAAA,CACrCC,sBAA2B,EAC3BC,oBAAyB,EACzB1K,QAAe,GAAG,EAAE,EACpBkI,cAAoB,KACjB;EAAA,IAAAyC,qBAAA,EAAAC,qBAAA;EACH;EACA,MAAMC,kBAAkB,GAAGpH,KAAK,CAACC,OAAO,CAAC+G,sBAAsB,CAAC,GAC5DA,sBAAsB,CAAC7G,MAAM,GAAG,CAAC,GACjC6G,sBAAsB,KAAK,EAAAE,qBAAA,GAAAF,sBAAsB,CAACjH,SAAS,qBAAhCmH,qBAAA,CAAkC/G,MAAM,IAAG,CAAC,IAAI6G,sBAAsB,CAACnJ,EAAE,CAAC;EAEzG,MAAMwJ,eAAe,GAAGrH,KAAK,CAACC,OAAO,CAACgH,oBAAoB,CAAC,GACvDA,oBAAoB,CAAC9G,MAAM,GAAG,CAAC,GAC/B8G,oBAAoB,KAAK,EAAAE,qBAAA,GAAAF,oBAAoB,CAAClH,SAAS,qBAA9BoH,qBAAA,CAAgChH,MAAM,IAAG,CAAC,IAAI8G,oBAAoB,CAACpJ,EAAE,CAAC;;EAEnG;EACA;EACA;;EAEA,MAAM+H,QAAQ,GAAG,IAAIvK,OAAO,CAACwK,QAAQ,CAAC,CAAC;EACvC,IAAIuB,kBAAkB,EAAE;IAAA,IAAAE,sBAAA,EAAAC,sBAAA;IACtB,MAAMC,UAAU,GAAG5B,QAAQ,CAACE,YAAY,CAAC,qBAAqB,CAAC;IAC/D,MAAMd,aAAa,GAAG,CAAAP,cAAc,aAAA6C,sBAAA,GAAd7C,cAAc,CAAEQ,SAAS,qBAAzBqC,sBAAA,CAA2B1J,OAAO,KAAI,EAAE;IAC9D,MAAM4H,UAAU,GAAG,CAAAf,cAAc,aAAA8C,sBAAA,GAAd9C,cAAc,CAAEQ,SAAS,qBAAzBsC,sBAAA,CAA2B/B,UAAU,KAAI,EAAE;IAC9D,MAAMC,cAAc,GAAG,IAAIT,aAAa,IAAIQ,UAAU,EAAE;IACxDgC,UAAU,CAACzB,MAAM,CAACvK,kBAAkB,CAACiK,cAAc,CAAC,CAAC;IACrD+B,UAAU,CAACzB,MAAM,CAACtK,cAAc,CAAC;IAEjC,MAAM8B,IAAW,GAAG,EAAE;IACtB,IAAIyC,KAAK,CAACC,OAAO,CAAC+G,sBAAsB,CAAC,EAAE;MACzCA,sBAAsB,CAAC3I,OAAO,CAACb,IAAI,IAAIqC,OAAO,CAACtC,IAAI,EAAEC,IAAI,EAAEjB,QAAQ,CAAC,CAAC;IACvE,CAAC,MAAM;MACLsD,OAAO,CAACtC,IAAI,EAAEyJ,sBAAsB,EAAEzK,QAAQ,CAAC;IACjD;IACAgB,IAAI,CAACc,OAAO,CAAC6D,GAAG,IAAIsF,UAAU,CAACzB,MAAM,CAAC/G,MAAM,CAACiC,MAAM,CAACiB,GAAG,CAAC,CAAC,CAAC;IAE1Db,cAAc,CAACmG,UAAU,CAAC;IAC1BxD,iBAAiB,CAACwD,UAAU,CAAC;IAC7B3L,kBAAkB,CAAC2L,UAAU,CAAC;EAChC;EACA,IAAIH,eAAe,EAAE;IAAA,IAAAI,sBAAA,EAAAC,sBAAA;IACnB,MAAMC,UAAU,GAAG/B,QAAQ,CAACE,YAAY,CAAC,mBAAmB,CAAC;IAC7D,MAAMd,aAAa,GAAG,CAAAP,cAAc,aAAAgD,sBAAA,GAAdhD,cAAc,CAAEQ,SAAS,qBAAzBwC,sBAAA,CAA2B7J,OAAO,KAAI,EAAE;IAC9D,MAAM4H,UAAU,GAAG,CAAAf,cAAc,aAAAiD,sBAAA,GAAdjD,cAAc,CAAEQ,SAAS,qBAAzByC,sBAAA,CAA2BlC,UAAU,KAAI,EAAE;IAC9D,MAAMC,cAAc,GAAG,IAAIT,aAAa,IAAIQ,UAAU,EAAE;IACxDmC,UAAU,CAAC5B,MAAM,CAACvK,kBAAkB,CAACiK,cAAc,CAAC,CAAC;IACrDkC,UAAU,CAAC5B,MAAM,CAACtK,cAAc,CAAC;IAEjC,MAAM8B,IAAW,GAAG,EAAE;IACtB,IAAIyC,KAAK,CAACC,OAAO,CAACgH,oBAAoB,CAAC,EAAE;MACvCA,oBAAoB,CAAC5I,OAAO,CAACb,IAAI,IAAIqC,OAAO,CAACtC,IAAI,EAAEC,IAAI,EAAEjB,QAAQ,EAAE,IAAI,CAAC,CAAC;IAC3E,CAAC,MAAM;MACLsD,OAAO,CAACtC,IAAI,EAAE0J,oBAAoB,EAAE1K,QAAQ,EAAE,IAAI,CAAC;IACrD;IACAgB,IAAI,CAACc,OAAO,CAAC6D,GAAG,IAAIyF,UAAU,CAAC5B,MAAM,CAAC/G,MAAM,CAACiC,MAAM,CAACiB,GAAG,CAAC,CAAC,CAAC;IAE1Db,cAAc,CAACsG,UAAU,CAAC;IAC1B3D,iBAAiB,CAAC2D,UAAU,CAAC;IAC7B9L,kBAAkB,CAAC8L,UAAU,CAAC;EAChC;EACA,MAAMhB,YAAY,GAAG,MAAMf,QAAQ,CAACgB,IAAI,CAACC,WAAW,CAAC,CAAC;EACtDvL,MAAM,CAAC,IAAIwL,IAAI,CAAC,CAACH,YAAY,CAAC,CAAC,EAAE,+BAA+B,CAAC;AACnE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}