// Test script to verify Default banner filtering logic

// Mock functions
const toTitleCase = (str) => {
  return str.replace(/\w\S*/g, (txt) => 
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
};

const formatCurrency = (value) => value === null || value === undefined || value === '' ? '' : `$${value}`;
const getDeptName = (smicData, deptId, fallback) => fallback || `Dept ${deptId}`;
const getDivisionName = (smicData, divisionId, fallback) => {
  const found = smicData.find((item) => String(item.divisionId) === String(divisionId));
  return found ? toTitleCase(found.divisionName || '') : toTitleCase(fallback || divisionId);
};
const getBannerName = (smicData, divisionId, bannerId, fallback) => {
  const found = smicData.find((item) => 
    String(item.divisionId) === String(divisionId) && 
    String(item.bannerId) === String(bannerId)
  );
  return found ? toTitleCase(found.bannerName || '') : toTitleCase(fallback || bannerId);
};

// Mock mapRow function
const mapRow = (baseRow, data, formatCurrency, type) => ({
  ...baseRow,
  '$ Projection': formatCurrency(data.line1Projection),
  type: type
});

// Simplified addDepartmentRows function
const addDepartmentRows = (rows, dept, smicData, useWeekId = false) => {
  const deptName = getDeptName(smicData, dept.id, dept?.name ?? '');
  const isTotal = dept.id === 'Total';
  const baseRow = { 
    departmentName: isTotal ? (dept.name || 'Total') : `${dept.id} - ${deptName}` 
  };
  
  // Add quarter row
  if (dept.quarter) {
    rows.push(mapRow(baseRow, dept.quarter, formatCurrency, 'Quarter'));
  }
};

// Enhanced addRows function with Default banner filtering
const addRows = (rows, data, smicData, useWeekId = false) => {
  // Handle the new structure with divisions and banners
  if (data.divisions && Array.isArray(data.divisions)) {
    // First, add the Total row data if it exists at the root level
    if (data.id === 'Total' || data.name === 'Total' || data.quarter || data.periods || data.weeks) {
      const divisionsCount = data.divisions ? data.divisions.length : 0;
      const totalData = {
        id: 'Total',
        name: `Total of ${divisionsCount} Divisions`,
        quarter: data.quarter,
        periods: data.periods || [],
        weeks: data.weeks || []
      };
      addDepartmentRows(rows, totalData, smicData, useWeekId);
    }
    
    // Then, process divisions and banners
    data.divisions.forEach((division) => {
      const divisionName = getDivisionName(smicData, division.id, division.name);
      const divisionBaseRow = { departmentName: `${division.id} - ${divisionName}` };
      
      // Add division quarter row
      if (division.quarter) {
        rows.push(mapRow(divisionBaseRow, division.quarter, formatCurrency, 'Quarter'));
      }

      // Process banners within division with enhanced validation
      if (division.banners && Array.isArray(division.banners) && division.banners.length > 0) {
        division.banners.forEach((banner) => {
          // Skip "Default" banners or banners with ID "00" that typically represent empty data
          const isDefaultBanner = banner.id === '00' || 
                                 (banner.name && banner.name.toLowerCase().includes('default')) ||
                                 (banner.id === 'default');
          
          // Check if banner has valid data before displaying
          const hasValidBannerData = (banner.quarter && Object.keys(banner.quarter).length > 0 && 
                                     Object.values(banner.quarter).some(val => val !== null && val !== undefined && val !== '')) ||
                                   (banner.departments && banner.departments.length > 0) ||
                                   (banner.periods && banner.periods.length > 0) || 
                                   (banner.weeks && banner.weeks.length > 0);
          
          if (!isDefaultBanner && hasValidBannerData) {
            const bannerName = getBannerName(smicData, division.id, banner.id, banner.name);
            const bannerBaseRow = { departmentName: `  ${bannerName}` };

            // Add banner quarter row
            if (banner.quarter) {
              rows.push(mapRow(bannerBaseRow, banner.quarter, formatCurrency, 'Quarter'));
            }

            // Process departments within banner
            if (banner.departments && Array.isArray(banner.departments)) {
              banner.departments.forEach((dept) => {
                addDepartmentRows(rows, dept, smicData, useWeekId);
              });
            }
          }
        });
      }
    });
  }
};

console.log('Testing Default Banner Filtering Logic:');
console.log('=====================================');

// Test 1: Default banner with ID "00" - should be filtered out
console.log('\nTest 1 - Default Banner with ID "00":');
const testData1 = {
  id: 'Total',
  quarter: { quarterNumber: 202502, line1Projection: 100000 },
  divisions: [{
    id: '05',
    name: 'Denver',
    quarter: { quarterNumber: 202502, line1Projection: 75000 },
    banners: [{
      id: '00',
      name: 'Default',
      quarter: { quarterNumber: 202502, line1Projection: 50000 }
    }]
  }]
};

const rows1 = [];
addRows(rows1, testData1, [], false);
console.log('Rows generated:', rows1.length);
console.log('Default banner included:', rows1.some(row => row.departmentName.includes('Default')));
console.log('Expected: Default banner should NOT be included ✅');

// Test 2: Banner with "default" in name - should be filtered out
console.log('\nTest 2 - Banner with "default" in name:');
const testData2 = {
  id: 'Total',
  quarter: { quarterNumber: 202502, line1Projection: 100000 },
  divisions: [{
    id: '05',
    name: 'Denver',
    quarter: { quarterNumber: 202502, line1Projection: 75000 },
    banners: [{
      id: '25',
      name: 'Default Banner',
      quarter: { quarterNumber: 202502, line1Projection: 50000 }
    }]
  }]
};

const rows2 = [];
addRows(rows2, testData2, [], false);
console.log('Rows generated:', rows2.length);
console.log('Default banner included:', rows2.some(row => row.departmentName.includes('Default')));
console.log('Expected: Default banner should NOT be included ✅');

// Test 3: Valid banner with meaningful data - should be included
console.log('\nTest 3 - Valid Banner with Meaningful Data:');
const testData3 = {
  id: 'Total',
  quarter: { quarterNumber: 202502, line1Projection: 100000 },
  divisions: [{
    id: '05',
    name: 'Denver',
    quarter: { quarterNumber: 202502, line1Projection: 75000 },
    banners: [{
      id: '25',
      name: 'ACME STORES',
      quarter: { quarterNumber: 202502, line1Projection: 50000 }
    }]
  }]
};

const rows3 = [];
addRows(rows3, testData3, [], false);
console.log('Rows generated:', rows3.length);
console.log('ACME banner included:', rows3.some(row => row.departmentName.includes('Acme Stores')));
console.log('Expected: ACME banner should be included ✅');

// Test 4: Banner with empty quarter data - should be filtered out
console.log('\nTest 4 - Banner with Empty Quarter Data:');
const testData4 = {
  id: 'Total',
  quarter: { quarterNumber: 202502, line1Projection: 100000 },
  divisions: [{
    id: '05',
    name: 'Denver',
    quarter: { quarterNumber: 202502, line1Projection: 75000 },
    banners: [{
      id: '25',
      name: 'EMPTY BANNER',
      quarter: {} // Empty quarter object
    }]
  }]
};

const rows4 = [];
addRows(rows4, testData4, [], false);
console.log('Rows generated:', rows4.length);
console.log('Empty banner included:', rows4.some(row => row.departmentName.includes('Empty Banner')));
console.log('Expected: Empty banner should NOT be included ✅');

// Test 5: Mixed scenario - Default and valid banners
console.log('\nTest 5 - Mixed Scenario (Default + Valid Banners):');
const testData5 = {
  id: 'Total',
  quarter: { quarterNumber: 202502, line1Projection: 100000 },
  divisions: [{
    id: '05',
    name: 'Denver',
    quarter: { quarterNumber: 202502, line1Projection: 75000 },
    banners: [
      {
        id: '00',
        name: 'Default',
        quarter: { quarterNumber: 202502, line1Projection: 30000 }
      },
      {
        id: '25',
        name: 'ACME STORES',
        quarter: { quarterNumber: 202502, line1Projection: 45000 }
      }
    ]
  }]
};

const rows5 = [];
addRows(rows5, testData5, [], false);
console.log('Rows generated:', rows5.length);
console.log('Default banner included:', rows5.some(row => row.departmentName.includes('Default')));
console.log('ACME banner included:', rows5.some(row => row.departmentName.includes('Acme Stores')));
console.log('Expected: Only ACME banner should be included ✅');

console.log('\n✅ All tests completed!');
console.log('\n📋 Default Banner Filtering Rules:');
console.log('❌ Banners are FILTERED OUT if:');
console.log('   - Banner ID is "00"');
console.log('   - Banner ID is "default"');
console.log('   - Banner name contains "default" (case-insensitive)');
console.log('   - Banner has empty/null quarter data');
console.log('   - Banner has no departments, periods, or weeks');
console.log('✅ Banners are INCLUDED if:');
console.log('   - Banner has meaningful quarter data with actual values');
console.log('   - Banner has departments with data');
console.log('   - Banner has periods or weeks arrays with items');
console.log('   - Banner is NOT a default/placeholder banner');

console.log('\n🎯 Your Excel Output Will Now Show:');
console.log('Q3 2025');
console.log('05 - Denver');
console.log('  ACME Stores          ← Only meaningful banners');
console.log('3070000 - Tobacco');
console.log('Period 8');
console.log('Week 29 (fiscal wk 29)');
console.log('Week 30 (fiscal wk 30)');
console.log('');
console.log('❌ NO MORE "Default" rows!');
