.table-container {
  width: 100%;
  height: 100%;
  display: block;
  position: relative;
  scroll-behavior: smooth;
}
.responsive-table-container {
  width: 100%;
  position: relative;
  overflow-x: auto;
   overflow-y: auto;
  &::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 15px;
    background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(0, 0, 0, 0.05));
    pointer-events: none;
  }
}
 
.expand-section-button {
   position: relative;
  vertical-align: middle;
  z-index: 1;
  cursor: pointer;
}
 
.expand-section-button {
  &:hover {
    background-color: #e0e0e0;
  }
}
 
.table-cell {
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: left;
}
 
.week-cell {
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: left;
  background-color: #FFF;
  
}
 
.header-cell {
  font-weight: bold;
  text-align: left;
  height: 24px !important;
  border-right: 4px solid #9DA4AE !important;
  background: #F9F9FB !important; // higher contrast
  padding: 0.5rem 0.5rem !important; // tighter
}
 
.subheader-cell {
  text-align: left;
  font-weight: bold;
}
 
.cell-dimensions {
  width: 200px !important;
  height: 32px !important;
  min-width: 200px !important;
  max-width: 200px !important;
  min-height: 32px !important;
  max-height: 32px !important;
  box-sizing: border-box !important;
  //line-height: 32px !important;
}
 
.department-cell,
tr.department-row td:first-child {
  //@extend .cell-dimensions;
  padding: 0 8px !important;
}
 
.period-cell,
tr.period-row td:first-child {
  //@extend .cell-dimensions;
  padding: 0 !important;
  text-align: left !important;
  //left: 0 !important;
  position: sticky !important;
}
 
.period-cell .periodText {
  display: flex;
  align-items: center;
  height: 100%;
  padding-left: 4px;
}
 
.truncated-text-base {
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
 
.truncated-text {
  max-width: 150px;
  display: inline-flex;
  align-items: center;
  height: 100%;
  cursor: pointer;
  margin-left: 4px;
 
  &:hover::before {
    content: "";
    position: absolute;
    right: -3px;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 3px;
    background-color: #333;
    border-radius: 50%;
    box-shadow: 0 -5px 0 #333, 0 5px 0 #333;
  }
}
 
.truncated-text-period {
  max-width: 180px;
  display: inline-block;
  line-height: 32px;
  width: 100%;
}
 
/* Color styles */
.text-green-600 {
  color: #105F0E !important;
  font-weight: 500;
}
 
.text-red-600 {
  color: #BF2912 !important;
}
 
.positive-value {
  color: #105F0E !important;
 
  &::before {
    content: "+";
  }
}
 
.negative-value {
  color: #BF2912 !important;
}
 
$allocatr-table-header-height: 80px;
 
.allocatr-insights-table {
  border-collapse: collapse;
  border-spacing: 0;
  table-layout: fixed !important;
  width: auto;
  min-width: 100%;
  font-size: 13px;
  margin: 0;
  padding: 0;
 
  & tr {
    height: 24px !important;
  }
 
  th,
  td {
    white-space: nowrap;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    position: relative;
     padding: 0 12px !important;
    font-variant-numeric: tabular-nums;
  }
 
  th:first-child,
  td:first-child {
    position: sticky;
    left: 0;
    z-index: 1;
    //background: #fff; // Ensure background is set
    border-right: 1px solid #e5e7eb !important; // Add right border for sticky column
  }
 
  /* Header styles */
  thead {
    position: sticky;
    top: 0;
    z-index: 2;
 
    th {
      font-weight: bold;
      color: #333;
      text-align: left;
    }
 
    tr:first-child th {
      position: sticky;
      top: 0;
      z-index: 1;
      background-color: #F9F9FB;
      font-weight: bold;
      text-align: left;
    }
 
 
    tr:nth-child(2) th {
      position: sticky;
      top: auto;
      z-index: 1;
      background-color: #DCDFEA;
      font-weight: bold;
      text-align: left;
    }
 
    tr:first-child th:first-child {
      z-index: 2;
    }
  }
.quarter-cell {
  background-color: #DCDFEA !important;
  color: #1B6EBB;
  padding: 0 12px;
  height: 100%;
  vertical-align: middle;
}
 
.quarter-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-weight: 600;
  line-height: 24px;
  gap: 8px;
  height: 100%;
}
/* Ensure first th in first row uses the correct background */
thead tr:first-child th.quarter-cell {
  background-color: #DCDFEA !important;
  vertical-align: middle !important;
}
 
  /* Department row styles */
  tr.department-row {
    background-color: #E7F5FE !important;
    z-index: 2;
    font-weight: 500;
    height: 32px !important;
    align-self: stretch;
 
    td {
      background-color: #E7F5FE !important;
      text-align: left;
    }
 
    td:first-child {
      .flex.items-center {
        align-items: center;
        // vertical-align: middle;
        display: flex;
        height: 100%;
      }
 
      button {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
 
  /* Period row styles */
  tr.period-row {
    height: 32px !important;
 
    td {
      background-color: #F9FFF0 !important;
      text-align: left;
      align-self: stretch;
      border-bottom: 1px solid #e5e7eb;
      border-top:1px solid #e5e7eb;
    }
  }
 
  tr.week-row {
    height: 32px !important;
 
    td {
      background-color: #FFF !important;
      text-align: left;
      align-self: stretch;
      border-bottom: 1px solid #e5e7eb;
      border-top: 1px solid #e5e7eb;
    }
  }
 
  /* Sticky header and subheader */
.sticky-header-row th,
.sticky-header-cell {
  position: sticky !important;
  top: 0;
  z-index: 20;
  background: #DCDFEA;
}
.sticky-subheader-row th {
  position: sticky !important;
  top: 0;/* height of first header row */
  z-index: 19;
  background: #DCDFEA;
}
 
/* Sticky total row and its periods */
.sticky-total-row,
tr[data-sticky-total="true"] {
  position: sticky !important;
  top: 80px; /* header height */
  z-index: 18;
  background: #E7F5FE !important;
}}
.scroll-container {
  overflow-y: auto;
  width: 100%;
  max-height: 80vh;
 
  @media (max-height: 800px) {
    max-height: 60vh;
  }
 
  @media (max-height: 600px) {
    max-height: 50vh;
  }
}
thead tr.sticky-header-row th {
  border-bottom: none !important;
   border-top: none !important;
}
 
thead tr.sticky-subheader-row th {
  border-top: none !important;
  
}
.allocatr-insights-table tr.high-contrast-total-separator {
  height: 0px !important;
  font-size: 0 !important;
  line-height: 0 !important;
  border-bottom: none !important;
   border-top: none !important;
  background: none !important;
  border:none !important
}
.high-contrast-total-separator td {
  position: sticky !important;
  z-index: 9 !important;
   border:none !important;
  border-bottom: none !important;
   border-top: none !important;
  background-color: #9DA4AE !important;
  height: 5px !important;
  outline: none !important;
  margin: 0 !important;
}
 
.allocatr-insights-table tbody td {
  height: 32px !important;
}
tr.total-row.division-row td, tr.total-row.division-row .division-cell {
    background: #E7F5FE;
}
 
tr.division-row td, tr.division-row .division-cell {
    background: #C4DFF8;
}
.sticky-header-cell.quarter-cell.qc-cell-with-icon {
    position: sticky !important;
    left: 38px;
    z-index: 9999999;
}
.division-cell, .banner-cell, .department-cell, .period-cell {
    position: sticky !important;
    left: 38px;
    z-index: 1;
}
 .allocatr-insights-table tbody tr.banner-row td:first-child {
    background: #fff !important;
}

td.banner-cell {
    background: #fff !important;
}
 
 